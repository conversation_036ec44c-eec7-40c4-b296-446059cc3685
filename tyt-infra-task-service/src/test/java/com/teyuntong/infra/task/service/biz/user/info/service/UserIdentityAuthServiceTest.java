package com.teyuntong.infra.task.service.biz.user.info.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.entity.TransportExtendDO;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.entity.TytTransport;
import com.teyuntong.infra.task.service.biz.goods.refresh.entity.GoodsRefreshExtraConfig;
import com.teyuntong.infra.task.service.utils.TransportUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2024/6/7 14:42
 */
@Slf4j
class UserIdentityAuthServiceTest {

    @Test
    void verifyPermissionExpireNotice() {
    }

    /**
     * 校验价格等级
     */
    private boolean checkPriceLevel(String price, Integer priceLevel, TransportExtendDO transportExtend) {
        if (TransportUtil.nonPrice(price) && priceLevel == 1) {
            return true;
        } else if (transportExtend.getSuggestMinPrice() != null
                && transportExtend.getSuggestMaxPrice() != null
                && transportExtend.getFixPriceFast() != null) {
            int intPrice = new BigDecimal(price).intValue();
            if (intPrice < transportExtend.getSuggestMinPrice() && priceLevel == 2) {
                return true;
            } else if (intPrice < transportExtend.getFixPriceFast() && priceLevel == 3) {
                return true;
            } else if (intPrice < transportExtend.getSuggestMaxPrice() && priceLevel == 4) {
                return true;
            } else if (intPrice >= transportExtend.getSuggestMaxPrice() && priceLevel == 5) {
                return true;
            }
        }
        return false;
    }

    @Test
    void newUserVerifyNotice() {

        // "startProvince":["北京","天津"],
        GoodsRefreshExtraConfig extraConfig = JSON.parseObject("""
                {
                    "routes": [
                        {
                            "startProvince":["北京","天津"],
                            "startCities":["北京市","保定市"],
                            "destProvince":["山东","江苏","河北"],
                            "destCities":["滨州市","保定市"]
                        }
                    ]
                }
                """, GoodsRefreshExtraConfig.class);
        TytTransport transport = new TytTransport();
        transport.setStartProvinc("北京");
        transport.setStartCity("济南市");
        transport.setDestProvinc("山东");
        transport.setDestCity("滨州市");

        // 校验路线
        if (CollectionUtil.isNotEmpty(extraConfig.getRoutes())) {
            boolean b = extraConfig.getRoutes().stream().anyMatch(t -> {
                // 出发地省市可多选，且无级联关系，有一个匹配即可
                if (CollectionUtil.isNotEmpty(t.getStartProvinces()) || CollectionUtil.isNotEmpty(t.getStartCities())) {
                    boolean isStartProvinceValid = CollectionUtil.isNotEmpty(t.getStartProvinces()) && t.getStartProvinces().contains(transport.getStartProvinc());
                    boolean isStartCityValid = CollectionUtil.isNotEmpty(t.getStartCities()) && t.getStartCities().contains(transport.getStartCity());
                    if (!isStartProvinceValid && !isStartCityValid) {
                        return false;
                    }
                }
                // 目的地省市可多选，且无级联关系，有一个匹配即可
                if (CollectionUtil.isNotEmpty(t.getDestProvinces()) || CollectionUtil.isNotEmpty(t.getDestCities())) {
                    boolean isDestProvinceValid = CollectionUtil.isNotEmpty(t.getDestProvinces()) && t.getDestProvinces().contains(transport.getDestProvinc());
                    boolean isDestCityValid = CollectionUtil.isNotEmpty(t.getDestCities()) && t.getDestCities().contains(transport.getDestCity());
                    if (!isDestProvinceValid && !isDestCityValid) {
                        return false;
                    }
                }
                return true;
            });
            if (!b) {
                log.info("未通过货源维度刷新配置，校验路线不符合");
            } else {
                log.info("通过货源维度刷新配置");
            }
        }
    }
}