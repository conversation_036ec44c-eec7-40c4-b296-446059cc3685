<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.goods.goodsinfo.mapper.TransportExposureMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.teyuntong.infra.task.service.biz.goods.goodsinfo.entity.TransportExposureDO">
        <id column="id" property="id"/>
        <result column="src_msg_id" property="srcMsgId"/>
        <result column="status" property="status"/>
        <result column="has_call" property="hasCall"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="change_id" property="changeId"/>
    </resultMap>
    <select id="getTransportExposureList" resultMap="BaseResultMap">
        select *
        from tyt_transport_exposure te
                 inner join tyt_transport_main tr on te.src_msg_id = tr.id
        where te.status = 1
          and tr.status = 1
          and te.has_call = 0
          and tr.ctime &lt;= #{maxDate}
          and tr.ctime >= #{minDate}
          and te.id > #{markTsId}
        order by te.id limit #{pageSize}
    </select>
    <select id="getMaxChangeId" resultType="java.lang.Long">
        select max(change_id)
        from tyt_transport_exposure
    </select>


</mapper>
