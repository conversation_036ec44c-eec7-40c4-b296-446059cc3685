<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.goods.goodsinfo.mapper.TransportDispatchViewMapper">
  <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.goods.goodsinfo.entity.TransportDispatchView">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="src_msg_id" jdbcType="BIGINT" property="srcMsgId" />
    <result column="car_user_id" jdbcType="BIGINT" property="carUserId" />
    <result column="car_user_name" jdbcType="VARCHAR" property="carUserName" />
    <result column="car_nick_name" jdbcType="VARCHAR" property="carNickName" />
    <result column="car_phone" jdbcType="VARCHAR" property="carPhone" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="remark_user_id" jdbcType="BIGINT" property="remarkUserId" />
    <result column="remark_user_name" jdbcType="VARCHAR" property="remarkUserName" />
    <result column="remark_time" jdbcType="TIMESTAMP" property="remarkTime" />
    <result column="view_count" jdbcType="INTEGER" property="viewCount" />
    <result column="contact_count" jdbcType="INTEGER" property="contactCount" />
    <result column="view_time" jdbcType="TIMESTAMP" property="viewTime" />
    <result column="contact_time" jdbcType="TIMESTAMP" property="contactTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>

  <!-- 统计某个货源的联系次数和查看次数，一个用户拨打多次算一次 -->
  <select id="sumBySrcMsgId" resultMap="BaseResultMap">
    SELECT COUNT(IF(contact_count > 0, 1, NULL)) contact_count, COUNT(IF(view_count > 0, 1, NULL)) view_count
    FROM tyt_transport_dispatch_view
    WHERE src_msg_id= #{srcMsgId}
  </select>

  <select id="selectViewListBySrcMsgIdList" resultType="com.teyuntong.infra.task.service.biz.goods.goodsinfo.entity.TransportDispatchView">
	  select
        src_msg_id as srcMsgId,
        COUNT(IF(view_count > 0, 1, NULL)) as view_count
	  from tyt_transport_dispatch_view
	  where src_msg_id in
	  <foreach collection="srcMsgIdList" open="(" item="item" separator="," close=")">
		  #{item}
	  </foreach>
      group by src_msg_id
      having view_count >= 3
  </select>
</mapper>