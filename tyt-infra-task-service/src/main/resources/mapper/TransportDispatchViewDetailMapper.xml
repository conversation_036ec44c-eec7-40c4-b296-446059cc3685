<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.goods.goodsinfo.mapper.TransportDispatchViewDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.goods.goodsinfo.entity.TransportDispatchViewDetailDO">
        <id column="id" property="id" />
        <result column="src_msg_id" property="srcMsgId" />
        <result column="car_user_id" property="carUserId" />
        <result column="car_user_name" property="carUserName" />
        <result column="car_nick_name" property="carNickName" />
        <result column="car_phone" property="carPhone" />
        <result column="type" property="type" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, src_msg_id, car_user_id, car_user_name, car_nick_name, car_phone, type, create_time, modify_time
    </sql>

	<select id="selectContactUserIds" resultType="java.lang.Long">
        select car_user_id
        from tyt_transport_dispatch_view_detail
        where type = 2 and src_msg_id in
        <foreach collection="similarTransportIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryContactCount" resultType="java.lang.Integer">
        select count(*)
        from tyt_transport_dispatch_view_detail
        where src_msg_id = #{srcMsgId} and  type = 2 and create_time > #{startTime} and create_time &lt; #{endTime}
    </select>

</mapper>
