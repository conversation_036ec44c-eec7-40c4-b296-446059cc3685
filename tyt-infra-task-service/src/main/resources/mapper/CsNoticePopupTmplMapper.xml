<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.customerservice.mapper.CsNoticePopupTmplMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.customerservice.entity.CsNoticePopupTmpl">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="title" property="title" />
        <result column="content" property="content" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, code, title, content, status, create_time, modify_time
    </sql>

    <select id="selectByCode" resultMap="BaseResultMap">
        select title,content from cs_notice_popup_tmpl where code = #{code} and status = 1 limit 1
    </select>

</mapper>
