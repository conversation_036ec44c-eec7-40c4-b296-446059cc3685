<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.car.mybatis.mapper.TytSigningCarInfoMapper">
  <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.car.mybatis.entity.TytSigningCarInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="signing_id" jdbcType="BIGINT" property="signingId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="car_type" jdbcType="VARCHAR" property="carType" />
    <result column="head_city_no" jdbcType="VARCHAR" property="headCityNo" />
    <result column="tail_city_no" jdbcType="VARCHAR" property="tailCityNo" />
    <result column="length" jdbcType="VARCHAR" property="length" />
    <result column="table_height" jdbcType="VARCHAR" property="tableHeight" />
    <result column="other_pure_flat" jdbcType="VARCHAR" property="otherPureFlat" />
    <result column="ladder_type" jdbcType="VARCHAR" property="ladderType" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="dispatch" jdbcType="VARCHAR" property="dispatch" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="county" jdbcType="VARCHAR" property="county" />

    <result column="driver_id" jdbcType="BIGINT" property="driverId" />
    <result column="driver_user_id" jdbcType="BIGINT" property="driverUserId" />
    <result column="driver_phone" jdbcType="VARCHAR" property="driverPhone" />
    <result column="favorable_comment" jdbcType="DECIMAL" property="favorableComment" />
    <result column="compre_fraction" jdbcType="DECIMAL" property="compreFraction" />

    <result column="assign_num" jdbcType="INTEGER" property="assignNum" />
    <result column="assign_success_num" jdbcType="INTEGER" property="assignSuccessNum" />
    <result column="receiving_orders" jdbcType="DECIMAL" property="receivingOrders" />
  </resultMap>

  <select id="getBySigningId" resultMap="BaseResultMap">
    select * from tyt_signing_car_info where signing_id = #{id}

  </select>
</mapper>