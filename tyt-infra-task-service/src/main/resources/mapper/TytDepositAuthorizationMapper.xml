<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.car.mybatis.mapper.TytDepositAuthorizationMapper">
  <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.car.mybatis.entity.TytDepositAuthorization">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="require_amount" jdbcType="DECIMAL" property="requireAmount" />
    <result column="auth_status" jdbcType="INTEGER" property="authStatus" />
    <result column="auth_time" jdbcType="TIMESTAMP" property="authTime" />
    <result column="black_status" jdbcType="INTEGER" property="blackStatus" />
    <result column="delete_status" jdbcType="INTEGER" property="deleteStatus" />
    <result column="operate_id" jdbcType="BIGINT" property="operateId" />
    <result column="user_group" jdbcType="INTEGER" property="userGroup" />
    <result column="operate_user_name" jdbcType="VARCHAR" property="operateUserName" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="refund_limit_status" jdbcType="INTEGER" property="refundLimitStatus" />
  </resultMap>

  <select id="getByUserIds" resultMap="BaseResultMap">
    select user_id,mtime from tyt_deposit_authorization where user_id in
    <foreach item="userId" collection="userIds" open="(" separator="," close=")">
      #{userId}
    </foreach>
     and auth_status = 1 and black_status = 0 and delete_status = 0

  </select>

  <select id="getDepositAuthByUserId" resultMap="BaseResultMap">
    select * from tyt_deposit_authorization
    where user_id = #{userId}
    and delete_status = 0
    limit 1
  </select>
</mapper>