<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.goods.dispatch.mapper.TytTransportDispatchMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.goods.dispatch.entity.TytTransportDispatchDO">
        <id column="id" property="id" />
        <result column="src_msg_id" property="srcMsgId" />
        <result column="user_id" property="userId" />
        <result column="publish_user_id" property="publishUserId" />
        <result column="publish_user_name" property="publishUserName" />
        <result column="dispatcher_id" property="dispatcherId" />
        <result column="dispatcher_name" property="dispatcherName" />
        <result column="owner_freight" property="ownerFreight" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
        <result column="give_goods_phone" property="giveGoodsPhone" />
        <result column="give_goods_name" property="giveGoodsName" />
        <result column="publish_platform" property="publishPlatform" />
        <result column="info_fee_diff" property="infoFeeDiff" />
        <result column="hong_xin_order_id" property="hongXinOrderId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, src_msg_id, user_id, publish_user_id, publish_user_name, dispatcher_id, dispatcher_name, owner_freight, create_time, modify_time, give_goods_phone, give_goods_name, publish_platform, info_fee_diff, hong_xin_order_id
    </sql>

    <select id="selectBySrcMsgId" resultMap="BaseResultMap">
        select *
        from tyt_transport_dispatch
        where src_msg_id = #{srcMsgId}
    </select>

</mapper>
