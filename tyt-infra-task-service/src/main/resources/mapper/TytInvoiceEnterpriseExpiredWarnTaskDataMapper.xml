<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.enterprise.mybatis.TytInvoiceEnterpriseExpiredWarnTaskDataMapper">
  <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.enterprise.pojo.TytInvoiceEnterpriseExpiredWarnTaskData">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="invoice_enterprise_id" jdbcType="BIGINT" property="invoiceEnterpriseId" />
    <result column="expired_type" jdbcType="VARCHAR" property="expiredType" />
    <result column="sms_content" jdbcType="VARCHAR" property="smsContent" />
    <result column="status" jdbcType="INTEGER" property="status" />
  </resultMap>

  <insert id="insertIgnoreList" parameterType="list">
    insert ignore into tyt_invoice_enterprise_expired_warn_task_data (user_id, invoice_enterprise_id, expired_type, sms_content, status) VALUES
    <foreach collection="warnDataList" index="index" item="item" separator=",">
      (
      #{item.userId}
      , #{item.invoiceEnterpriseId}
      , #{item.expiredType}
      , #{item.smsContent}
      , #{item.status}
      )
    </foreach>
  </insert>

  <update id="setDoneById">
    update tyt_invoice_enterprise_expired_warn_task_data set status = 1 where id = #{id}
  </update>

  <delete id="cleanAllData">
    delete from tyt_invoice_enterprise_expired_warn_task_data where 1 = 1
  </delete>

  <select id="getAllDataStatusIsZero" resultMap="BaseResultMap">
    select id, user_id, invoice_enterprise_id, expired_type, sms_content, status
    from tyt_invoice_enterprise_expired_warn_task_data where status = 0
  </select>


</mapper>