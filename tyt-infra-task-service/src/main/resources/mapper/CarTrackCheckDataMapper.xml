<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.trade.infofee.mapper.CarTrackCheckDataMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.trade.infofee.entity.CarTrackCheckDataDO">
        <id column="id" property="id" />
        <result column="ts_id" property="tsId" />
        <result column="car_head_no" property="carHeadNo" />
        <result column="start_longitude" property="startLongitude" />
        <result column="start_latitude" property="startLatitude" />
        <result column="dest_longitude" property="destLongitude" />
        <result column="dest_latitude" property="destLatitude" />
        <result column="check_begin_time" property="checkBeginTime" />
        <result column="check_end_time" property="checkEndTime" />
        <result column="data_source" property="dataSource" />
        <result column="into_load_location" property="intoLoadLocation" />
        <result column="into_unLoad_location" property="intoUnloadLocation" />
        <result column="opera_user_id" property="operaUserId" />
        <result column="opera_user_name" property="operaUserName" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, ts_id, car_head_no, start_longitude, start_latitude, dest_longitude, dest_latitude, check_begin_time, check_end_time, data_source, into_load_location, into_unLoad_location, opera_user_id, opera_user_name, create_time, update_time
    </sql>

    <select id="getNeedTraceCheckList"  resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from car_track_check_data  where into_load_location=0 or into_unLoad_location=0 and check_end_time &lt; now()
        order by id desc
    </select>


</mapper>
