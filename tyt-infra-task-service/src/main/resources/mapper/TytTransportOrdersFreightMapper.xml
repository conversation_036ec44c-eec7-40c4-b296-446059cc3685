<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.trade.infofee.mapper.TytTransportOrdersFreightMapper">

  <select id="getFreightByOrderId" resultType="com.teyuntong.infra.task.service.biz.trade.infofee.entity.TransportOrdersFreight">
    select
        id, order_id orderId, user_id userId, amount, status, trade_no tradeNo, thirdly_trade_no thirdlyTradeNo, pay_time payTime, create_time createTime
    from tyt_transport_orders_freight
    where order_id = #{orderId}
  </select>

</mapper>