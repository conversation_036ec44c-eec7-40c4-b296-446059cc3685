<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.trade.infofee.mapper.TransportProtocolMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.trade.infofee.entity.TransportProtocolDO">
        <id column="id" property="id" />
        <result column="protocol_name" property="protocolName" />
        <result column="protocol_status" property="protocolStatus" />
        <result column="sign_time" property="signTime" />
        <result column="transport_order_id" property="transportOrderId" />
        <result column="carriage_pay_day" property="carriagePayDay" />
        <result column="carriage_fee" property="carriageFee" />
        <result column="additional_price" property="additionalPrice" />
        <result column="enterprise_tax_rate" property="enterpriseTaxRate" />
        <result column="total_carriage_fee" property="totalCarriageFee" />
        <result column="task_content" property="taskContent" />
        <result column="loading_time" property="loadingTime" />
        <result column="begin_unload_time" property="beginUnloadTime" />
        <result column="unload_time" property="unloadTime" />
        <result column="begin_loading_time" property="beginLoadingTime" />
        <result column="start_point" property="startPoint" />
        <result column="dest_point" property="destPoint" />
        <result column="start_coord" property="startCoord" />
        <result column="dest_coord" property="destCoord" />
        <result column="start_coord_x" property="startCoordX" />
        <result column="start_coord_y" property="startCoordY" />
        <result column="dest_coord_x" property="destCoordX" />
        <result column="dest_coord_y" property="destCoordY" />
        <result column="start_longitude" property="startLongitude" />
        <result column="start_latitude" property="startLatitude" />
        <result column="dest_longitude" property="destLongitude" />
        <result column="dest_latitude" property="destLatitude" />
        <result column="start_detail_add" property="startDetailAdd" />
        <result column="dest_detail_add" property="destDetailAdd" />
        <result column="start_provinc" property="startProvinc" />
        <result column="start_city" property="startCity" />
        <result column="start_area" property="startArea" />
        <result column="dest_provinc" property="destProvinc" />
        <result column="dest_city" property="destCity" />
        <result column="dest_area" property="destArea" />
        <result column="weight" property="weight" />
        <result column="length" property="length" />
        <result column="wide" property="wide" />
        <result column="high" property="high" />
        <result column="car_type" property="carType" />
        <result column="car_style" property="carStyle" />
        <result column="car_length" property="carLength" />
        <result column="car_min_length" property="carMinLength" />
        <result column="car_max_length" property="carMaxLength" />
        <result column="work_plane_min_high" property="workPlaneMinHigh" />
        <result column="work_plane_max_high" property="workPlaneMaxHigh" />
        <result column="work_plane_min_length" property="workPlaneMinLength" />
        <result column="work_plane_max_length" property="workPlaneMaxLength" />
        <result column="other_require" property="otherRequire" />
        <result column="head_city" property="headCity" />
        <result column="head_no" property="headNo" />
        <result column="tail_city" property="tailCity" />
        <result column="tail_no" property="tailNo" />
        <result column="driver_id" property="driverId" />
        <result column="driver_name" property="driverName" />
        <result column="climb" property="climb" />
        <result column="tyre_exposed_flag" property="tyreExposedFlag" />
        <result column="car_length_labels" property="carLengthLabels" />
        <result column="deposit_amount" property="depositAmount" />
        <result column="refund_flag" property="refundFlag" />
        <result column="cargo_size" property="cargoSize" />
        <result column="car_require" property="carRequire" />
        <result column="loading_time_str" property="loadingTimeStr" />
        <result column="unload_time_str" property="unloadTimeStr" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, protocol_name, protocol_status, sign_time, transport_order_id, carriage_pay_day, carriage_fee, additional_price, enterprise_tax_rate, total_carriage_fee, task_content, loading_time, begin_unload_time, unload_time, begin_loading_time, start_coord, dest_coord, start_coord_x, start_coord_y, dest_coord_x, dest_coord_y, start_longitude, start_latitude, start_detail_add, dest_detail_add, start_provinc, start_city, start_area, dest_provinc, dest_city, dest_area, weight, length, wide, high, car_type, car_style, car_length, car_min_length, car_max_length, work_plane_min_high, work_plane_max_high, work_plane_min_length, work_plane_max_length, other_require, create_time, update_time
    </sql>

    <select id="getLastFinishProtocol"
            resultType="com.teyuntong.infra.task.service.biz.trade.infofee.entity.TransportProtocolDO">
        select * from tyt_transport_protocol where transport_order_id = #{orderId} and protocol_status = 2 order by id desc limit 1
    </select>

    <select id="getProtocolByOrderId" resultType="com.teyuntong.infra.task.service.biz.trade.infofee.entity.TransportProtocolDO">
        select
            id,transport_order_id transportOrderId, protocol_name protocolName, protocol_status protocolStatus
        from tyt_transport_protocol
        where transport_order_id = #{orderId} order by id desc limit 1
    </select>


</mapper>
