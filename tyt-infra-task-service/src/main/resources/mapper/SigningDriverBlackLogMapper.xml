<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.driver.mybatis.mapper.SigningDriverBlackLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.driver.mybatis.entity.SigningDriverBlackLogDO">
        <id column="id" property="id" />
        <result column="black_id" property="blackId" />
        <result column="user_id" property="userId" />
        <result column="sign_status" property="signStatus" />
        <result column="black_type" property="blackType" />
        <result column="restrict_num" property="restrictNum" />
        <result column="restrict_start_time" property="restrictStartTime" />
        <result column="restrict_end_time" property="restrictEndTime" />
        <result column="remark" property="remark" />
        <result column="operator_name" property="operatorName" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, black_id, user_id, sign_status, black_type, restrict_num, restrict_start_time, restrict_end_time, remark, operator_name, create_time, modify_time
    </sql>

</mapper>
