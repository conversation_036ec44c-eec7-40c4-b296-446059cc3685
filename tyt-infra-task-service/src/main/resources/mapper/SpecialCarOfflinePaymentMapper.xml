<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.dispatch.mapper.SpecialCarOfflinePaymentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.dispatch.entity.SpecialCarOfflinePaymentDO">
        <id column="id" property="id" />
        <result column="dispatch_id" property="dispatchId" />
        <result column="order_id" property="orderId" />
        <result column="offline_payment" property="offlinePayment" />
        <result column="recipient_name" property="recipientName" />
        <result column="bank_no" property="bankNo" />
        <result column="open_bank" property="openBank" />
        <result column="driver_payment_amount" property="driverPaymentAmount" />
        <result column="tax_payment_amount" property="taxPaymentAmount" />
        <result column="navigation_distance" property="navigationDistance" />
        <result column="unable_invoice_reason" property="unableInvoiceReason" />
        <result column="payment_progress" property="paymentProgress" />
        <result column="pic_url" property="picUrl" />
        <result column="remark" property="remark" />
        <result column="delete_status" property="deleteStatus" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="operate_id" property="operateId" />
        <result column="operate_name" property="operateName" />
        <result column="send_message_status" property="sendMessageStatus" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, dispatch_id, order_id, offline_payment, recipient_name, bank_no, open_bank, driver_payment_amount, tax_payment_amount, navigation_distance, unable_invoice_reason, payment_progress, pic_url, remark, delete_status, create_time, update_time, operate_id, operate_name, send_message_status
    </sql>

    <update id="batchUpdateStatus">
        update tyt_special_car_offline_payment
        set send_message_status = #{status},
            update_time = now()
        where id in
        <foreach collection="idList" item="id" open="(" close=")">
            #{id}
        </foreach>
    </update>

</mapper>
