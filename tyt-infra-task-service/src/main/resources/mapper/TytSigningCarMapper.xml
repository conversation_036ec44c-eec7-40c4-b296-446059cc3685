<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.car.mybatis.mapper.TytSigningCarMapper">
  <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.car.mybatis.entity.TytSigningCar">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />

    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="tyt_cell_phone" jdbcType="VARCHAR" property="tytCellPhone" />

    <result column="signing" jdbcType="VARCHAR" property="signing" />
    <result column="cooperate_num" jdbcType="INTEGER" property="cooperateNum" />
    <result column="assign_num" jdbcType="INTEGER" property="assignNum" />
    <result column="assign_success_num" jdbcType="INTEGER" property="assignSuccessNum" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />

    <result column="dispatch" jdbcType="VARCHAR" property="dispatch" />
    <result column="receiving_orders" jdbcType="DECIMAL" property="receivingOrders" />
    <result column="favorable_comment" jdbcType="DECIMAL" property="favorableComment" />
    <result column="compre_fraction" jdbcType="DECIMAL" property="compreFraction" />
    <result column="status" jdbcType="INTEGER" property="status" />

  </resultMap>

  <select id="getByMaxId" resultMap="BaseResultMap">
        select * from tyt_signing_car where id > #{maxId} order by id asc limit #{limit}

  </select>
  <update id="updateStatus">
      update tyt_signing_car set status = 1 where status = 0
  </update>
</mapper>