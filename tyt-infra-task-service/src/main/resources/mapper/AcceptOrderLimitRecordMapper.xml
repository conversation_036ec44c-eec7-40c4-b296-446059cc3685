<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.limit.mybatis.mapper.AcceptOrderLimitRecordMapper">

  <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.limit.mybatis.entity.AcceptOrderLimitRecord">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="cell_phone" jdbcType="VARCHAR" property="cellPhone" />
    <result column="accept_order_limit_start_time" jdbcType="TIMESTAMP" property="acceptOrderLimitStartTime" />
    <result column="accept_order_limit_end_time" jdbcType="TIMESTAMP" property="acceptOrderLimitEndTime" />
    <result column="accept_order_limit_num" jdbcType="INTEGER" property="acceptOrderLimitNum" />
    <result column="accept_order_limit_item" jdbcType="INTEGER" property="acceptOrderLimitItem" />
    <result column="accept_order_limit_status" jdbcType="INTEGER" property="acceptOrderLimitStatus" />
    <result column="opera_user_id" jdbcType="BIGINT" property="operaUserId" />
    <result column="opera_user_name" jdbcType="VARCHAR" property="operaUserName" />
    <result column="opera_time" jdbcType="TIMESTAMP" property="operaTime" />
    <result column="lift_user_id" jdbcType="BIGINT" property="liftUserId" />
    <result column="lift_user_name" jdbcType="VARCHAR" property="liftUserName" />
    <result column="lift_time" jdbcType="TIMESTAMP" property="liftTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
  </resultMap>

  <select id="selectMaxEndTime" resultType="java.util.Date">
    SELECT
        MAX(accept_order_limit_end_time) AS maxEndTime
    FROM
        accept_order_limit_record
    WHERE
        user_id = #{userId}
    AND
        accept_order_limit_status = #{status}
    order by id desc limit 1
  </select>
</mapper>