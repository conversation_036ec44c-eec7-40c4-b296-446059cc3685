<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.goods.goodsinfo.mapper.TytTransportMainMapper">


<select id="findMarkUpUsers" resultType="com.teyuntong.infra.task.service.biz.goods.goodsfee.bean.GoodMasterMarkUpPO">
   <!-- select
    tt.src_msg_id as srcMsgId,
    ttdv.contact_count as contactCount,
    tt.user_id as userId,
    tt.start_point as startPoint,
    tt.publish_type as publishType,
    tt.price price,
    tt.dest_point as destPoint
    from tyt_transport tt
    left join tyt_transport_dispatch_view ttdv on
    tt.src_msg_id = ttdv.src_msg_id
    where tt.status = 1
    and tt.pub_date between #{startTime} and #{endTime} limit #{startIndex},#{endIndex}-->
    select
    tt.src_msg_id as srcMsgId,
    tt.user_id as userId,
    tt.start_point as startPoint,
    tt.publish_type as publishType,
    tt.price price,
    tt.dest_point as destPoint
    from tyt_transport tt
    where tt.status = 1
    and tt.pub_date between #{startTime} and #{endTime} limit #{startIndex},#{endIndex}
</select>
<select id="findMarkUpUserCount" resultType="java.lang.Integer">
    select
    count(1)
    from tyt_transport tt
    where tt.status = 1 and tt.pub_date between #{startTime} and #{endTime}
</select>
    
 <select id="findContacts" resultType="com.teyuntong.infra.task.service.biz.goods.goodsfee.bean.GoodMasterMarkUpPO">
     select src_msg_id as srcMsgId
     from tyt_transport_dispatch_view
     where contact_count > 0 and src_msg_id in
     <foreach close=")" collection="srcMsgIds" index="index" item="srcMsgId" open="(" separator=",">
         #{srcMsgId}
     </foreach>
     group by src_msg_id
 </select>
</mapper>