<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.abtest.mapper.AbtestConfigUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.abtest.entity.AbtestConfigUserDO">
        <id column="id" property="id" />
        <result column="abtest_id" property="abtestId" />
        <result column="user_id" property="userId" />
        <result column="type" property="type" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
        <result column="modify_employee_id" property="modifyEmployeeId" />
        <result column="create_name" property="createName" />
        <result column="modify_name" property="modifyName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, abtest_id, user_id, type, create_time, modify_time, modify_employee_id, create_name, modify_name
    </sql>

    <insert id="insertList" parameterType="java.util.List">
        INSERT INTO tyt_abtest_config_user (id, abtest_id, user_id, type, create_time, modify_time, modify_employee_id, create_name, modify_name)
        VALUES
        <foreach item="item" index="index" collection="list" open="(" separator="),(" close=")">
            #{item.id}, #{item.abtestId}, #{item.userId}, #{item.type}, #{item.createTime}, #{item.modifyTime}, #{item.modifyEmployeeId}, #{item.createName}, #{item.modifyName}
        </foreach>
    </insert>

    <select id="getByUserId" resultMap="BaseResultMap">
        select * from tyt_abtest_config_user where user_id = #{userId} and abtest_id = #{abtestId}
    </select>

	<select id="getAbtestConfigUserByIdAndUserList" resultType="java.lang.Long">
        select user_id
        from tyt_abtest_config_user
        where abtest_id = #{abtestId} and type = 1 and user_id in
        <foreach collection="userIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="getUserIdList" resultType="java.lang.Long">
        select user_id from tyt_abtest_config_user
                       where abtest_id = #{abConfigId}
                         and type = 1
                         and user_id > #{startUserId}
                       order by user_id asc limit 100
    </select>

</mapper>
