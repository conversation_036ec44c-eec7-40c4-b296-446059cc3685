<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.permission.mybatis.mapper.TytExcellentGoodsCardUserNofifyLogMapper">
  <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.permission.mybatis.entity.TytExcellentGoodsCardUserNofifyLog">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="notify_time" jdbcType="TIMESTAMP" property="notifyTime" />
    <result column="notify_type" jdbcType="INTEGER" property="notifyType" />
    <result column="notify_content_message" jdbcType="VARCHAR" property="notifyContentMessage" />
    <result column="notify_content_push" jdbcType="VARCHAR" property="notifyContentPush" />
    <result column="notify_content_news" jdbcType="VARCHAR" property="notifyContentNews" />
    <result column="is_send_messge" jdbcType="INTEGER" property="isSendMessge" />
    <result column="creat_time" jdbcType="TIMESTAMP" property="creatTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <insert id="excellentGoodsCardWarnNoteLog" parameterType="com.teyuntong.infra.task.service.biz.user.permission.mybatis.entity.TytExcellentGoodsCardUserNofifyLog">
    insert into tyt_excellent_goods_card_user_nofify_log
        (user_id, notify_time, notify_type, notify_content_message, notify_content_push, notify_content_news, is_send_messge, creat_time, update_time)
        value
        (#{userId}, now(), #{notifyType}, #{shortMessageContent}, #{pushContent}, #{newsContent}, #{iNneedSendMessage}, now(), now())
  </insert>

</mapper>