<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.car.mybatis.mapper.CarteamClewMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.car.mybatis.entity.CarteamClewDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="user_name" property="userName" />
        <result column="user_identity" property="userIdentity" />
        <result column="link_phone" property="linkPhone" />
        <result column="gather_name" property="gatherName" />
        <result column="gather_identity" property="gatherIdentity" />
        <result column="sale_name" property="saleName" />
        <result column="sale_phone" property="salePhone" />
        <result column="sale_id" property="saleId" />
        <result column="dial_num" property="dialNum" />
        <result column="is_allot" property="isAllot" />
        <result column="status" property="status" />
        <result column="rel_result" property="relResult" />
        <result column="source" property="source" />
        <result column="allo_time" property="alloTime" />
        <result column="province" property="province" />
        <result column="city" property="city" />
        <result column="area" property="area" />
        <result column="import_source" property="importSource" />
        <result column="opera_user_id" property="operaUserId" />
        <result column="opera_user_name" property="operaUserName" />
        <result column="ctime" property="ctime" />
        <result column="mtime" property="mtime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, user_name, user_identity, link_phone, gather_name, gather_identity, sale_name, sale_phone, sale_id, dial_num, is_allot, status, rel_result, source, allo_time, province, city, area, import_source, opera_user_id, opera_user_name, ctime, mtime
    </sql>


    <resultMap id="carClewUserResultMap" type="com.teyuntong.infra.task.service.biz.user.car.dto.CarClewUserBean">
        <id property="id" column="id"/>
        <result property="trueName" column="true_name"/>
        <result property="deliverTypeOne" column="deliver_type_one"/>
        <result property="cellPhone" column="cell_phone"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
    </resultMap>

    <select id="getCarClewUserList" resultMap="carClewUserResultMap">
        SELECT u.id, u.true_name, u.deliver_type_one, u.cell_phone, u.province, u.city
        FROM tyt_car c
        JOIN tyt_user u ON c.user_id = u.id
        WHERE c.examine_time &gt;= #{startDate,jdbcType=TIMESTAMP}
        AND c.examine_time &lt;= #{endDate,jdbcType=TIMESTAMP}
        AND c.auth = #{auth,jdbcType=INTEGER}
    </select>

    <select id="getByCellPhone" resultMap="BaseResultMap">
        SELECT *
        FROM tyt_carteam_clew
        WHERE link_phone = #{cellPhone,jdbcType=VARCHAR} limit 1
    </select>


    <select id="getCountByDateRange" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM `tyt_carteam_clew` WHERE
                source = 2 AND
                ctime &gt;= #{startDate,jdbcType=TIMESTAMP} AND
                ctime &lt; #{endDate,jdbcType=TIMESTAMP}
    </select>

    <select id="selectPhonesByDateRange" resultType="java.lang.String">
        SELECT link_phone
        FROM `tyt_carteam_clew`
        WHERE source =2 AND ctime &gt;= #{startDate} AND ctime &lt;= #{endDate}
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 批量更新省份和城市信息 -->
    <update id="updateProvinceAndCityByPhone" parameterType="map">
            UPDATE `tyt_carteam_clew`  SET province = #{map.province}, city = #{map.city} WHERE link_phone = #{map.phone}
    </update>

</mapper>
