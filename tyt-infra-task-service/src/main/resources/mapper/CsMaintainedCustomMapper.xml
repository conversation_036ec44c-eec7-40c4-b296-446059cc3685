<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.info.mybatis.mapper.CsMaintainedCustomMapper">
  <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.info.mybatis.entity.CsMaintainedCustom">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="belong_to" jdbcType="SMALLINT" property="belongTo" />
    <result column="custom_id" jdbcType="BIGINT" property="customId" />
    <result column="custom_phone" jdbcType="VARCHAR" property="customPhone" />
    <result column="maintainer_id" jdbcType="BIGINT" property="maintainerId" />
    <result column="maintainer_name" jdbcType="VARCHAR" property="maintainerName" />
    <result column="last_comm_time" jdbcType="TIMESTAMP" property="lastCommTime" />
    <result column="no_pay_reason_one" jdbcType="BIGINT" property="noPayReasonOne" />
    <result column="not_pay_reason_two" jdbcType="BIGINT" property="notPayReasonTwo" />
    <result column="no_pay_reason_three" jdbcType="BIGINT" property="noPayReasonThree" />
    <result column="utime" jdbcType="TIMESTAMP" property="utime" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="modify_id" jdbcType="BIGINT" property="modifyId" />
    <result column="modify_name" jdbcType="VARCHAR" property="modifyName" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="rvisit_time" jdbcType="TIMESTAMP" property="rvisitTime" />
    <result column="user_label" jdbcType="VARCHAR" property="userLabel" />
    <result column="car_user_label" jdbcType="VARCHAR" property="carUserLabel" />
    <result column="is_need_defender" jdbcType="SMALLINT" property="isNeedDefender" />
    <result column="is_need_move" jdbcType="SMALLINT" property="isNeedMove" />
    <result column="is_need_show" jdbcType="SMALLINT" property="isNeedShow" />
    <result column="is_need_force_show" jdbcType="SMALLINT" property="isNeedForceShow" />
    <result column="show_time" jdbcType="TIMESTAMP" property="showTime" />
    <result column="intention_rank" jdbcType="INTEGER" property="intentionRank" />
    <result column="previous_maintainer_id" jdbcType="BIGINT" property="previousMaintainerId" />
    <result column="previous_maintainer_name" jdbcType="VARCHAR" property="previousMaintainerName" />
    <result column="area_type" jdbcType="SMALLINT" property="areaType" />
    <result column="empower_status" jdbcType="SMALLINT" property="empowerStatus" />
    <result column="goods_intention" jdbcType="SMALLINT" property="goodsIntention" />
    <result column="dispatcher_id" jdbcType="BIGINT" property="dispatcherId" />
    <result column="dispatcher_name" jdbcType="VARCHAR" property="dispatcherName" />
    <result column="binding_dispatch_time" jdbcType="TIMESTAMP" property="bindingDispatchTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="goods_maintainer_id" jdbcType="BIGINT" property="goodsMaintainerId" />
    <result column="goods_maintainer_name" jdbcType="VARCHAR" property="goodsMaintainerName" />
    <result column="goods_owner" jdbcType="INTEGER" property="goodsOwner" />
    <result column="goods_sync" jdbcType="INTEGER" property="goodsSync" />
    <result column="dispatch_identity" jdbcType="INTEGER" property="dispatchIdentity" />
    <result column="frequent_delivery" jdbcType="VARCHAR" property="frequentDelivery" />
    <result column="delivery_num" jdbcType="INTEGER" property="deliveryNum" />
    <result column="delivery_frequency" jdbcType="INTEGER" property="deliveryFrequency" />
    <result column="free" jdbcType="INTEGER" property="free" />
  </resultMap>
  <update id="updateDispatcher">
    UPDATE `cs_maintained_custom`
    set dispatcher_id = #{userBindId},
        binding_dispatch_time=NOW(),
        dispatcher_name = #{realName}
    where custom_phone = #{phone}
      AND dispatcher_id is null
  </update>

  <select id="getByUser" resultMap="BaseResultMap">
    select id,custom_id,maintainer_id,goods_maintainer_id from
          cs_maintained_custom where id > #{maxId} and
          (maintainer_id is not null or goods_maintainer_id is not null) limit #{limit}
  </select>

  <select id="selectByCustomPhone" resultMap="BaseResultMap">
    select *
    from cs_maintained_custom
    where custom_phone = #{customPhone}
  </select>

  <select id="getByCustomId" resultMap="BaseResultMap">
    select id, custom_id, maintainer_id, goods_maintainer_id, free
    from cs_maintained_custom
    where custom_id = #{customId} limit 1
  </select>

  <select id="getCustomByPhone" resultMap="BaseResultMap">
    select * from cs_maintained_custom where custom_phone=#{phone} order by id desc limit 1
  </select>

  <update id="updateMaintainedCus">
    UPDATE tyt.`cs_maintained_custom` cmc LEFT JOIN tyt.tyt_user u ON cmc.custom_id = u.id SET cmc.`custom_phone` = u.cell_phone where mtime>#{startTime}
  </update>

  <update id="updateIsNeedShow">
    UPDATE `cs_maintained_custom` c LEFT JOIN tyt_user_archives a ON c.custom_id = a.user_id SET c.is_need_show = 1,c.show_time=NOW(),c.is_need_defender = 1 WHERE c.is_need_show = 2 AND c.is_need_force_show=2 AND a.carvip_pay_times=0 AND a.goodvip_pay_times=0 AND c.ctime &gt;#{startTime} AND c.ctime &lt;#{endTime} and c.belong_to in(1,2) and c.maintainer_id is null and c.goods_maintainer_id is null
  </update>

  <select id="getNeedMaintainer" resultMap="BaseResultMap">
    select * from cs_maintained_custom where belong_to=#{belongTo} and (is_need_defender =1 or is_need_move=1)
  </select>

  <update id="updateMaintainer">
    UPDATE `cs_maintained_custom` SET `is_need_defender`=2, `is_need_move`=2
    <if test="belongTo==1">
      ,`maintainer_id`=#{maintainerId}, `maintainer_name`=#{maintainerName}
    </if>
    <if test="belongTo==2">
      ,`goods_maintainer_id`=#{maintainerId}, `goods_maintainer_name`=#{maintainerName}
    </if>
    WHERE custom_id=#{customId}
    <if test="belongTo==1">
      and goods_maintainer_id is null
    </if>
    <if test="belongTo==2">
      and maintainer_id is null
    </if>
  </update>

</mapper>