<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.trade.infofee.mapper.CarGoodsDealNumMapper">

    <insert id="insertCarGoodDealNum">
        INSERT INTO `tyt`.`tyt_car_good_deal_num`
        (`car_id`, `goods_id`, `deal_num`, `status`, `ctime`, `utime`)
        SELECT tto.`pay_user_id`, tto.`user_id`, 0, 1, NOW(), NOW()
        FROM tyt.`tyt_transport_orders` tto WHERE tto.`rob_status` IN (5, 6)
        AND tto.`load_time` &gt; #{loadTime} and tto.`load_time` &lt; #{loadTime1}
        AND NOT EXISTS (SELECT tc.id FROM tyt.`tyt_car_good_deal_num` tc
        WHERE tc.car_id=tto.`pay_user_id` AND tc.goods_id=tto.user_id)
        GROUP BY tto.`user_id`, tto.`pay_user_id`
    </insert>

    <update id="updateCarGoodDealNum">
        UPDATE tyt.`tyt_car_good_deal_num` tc
        SET tc.`deal_num`=tc.`deal_num` + (SELECT COUNT(*)
        FROM( SELECT tto.`pay_user_id`, tto.`user_id`
        FROM tyt.`tyt_transport_orders` tto
        WHERE tto.`load_time` &gt; #{loadTime} AND tto.`load_time` &lt; #{loadTime1}
        AND tto.`rob_status` IN(5,6)
        GROUP BY tto.`user_id`,tto.`ts_id` ) temp
        WHERE temp.pay_user_id=tc.car_id AND
        temp.user_id=tc.goods_id)
    </update>
</mapper>
