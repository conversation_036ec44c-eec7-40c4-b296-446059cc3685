<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.enterprise.mybatis.TytInoviceEnterpriseMapper">
  <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.enterprise.pojo.TytInvoiceEnterprise">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="enterprise_name" jdbcType="VARCHAR" property="enterpriseName" />
    <result column="legal_person_name" jdbcType="VARCHAR" property="legalPersonName" />
    <result column="legal_person_phone" jdbcType="VARCHAR" property="legalPersonPhone" />
    <result column="legal_person_card" jdbcType="VARCHAR" property="legalPersonCard" />
    <result column="legal_person_card_url_g" jdbcType="VARCHAR" property="legalPersonCardUrlG" />
    <result column="legal_person_card_url_t" jdbcType="VARCHAR" property="legalPersonCardUrlT" />
    <result column="enterprise_credit_code" jdbcType="VARCHAR" property="enterpriseCreditCode" />
    <result column="enterprise_type" jdbcType="VARCHAR" property="enterpriseType" />
    <result column="enterprise_business_scope" jdbcType="VARCHAR" property="enterpriseBusinessScop" />
    <result column="enterprise_home_address" jdbcType="VARCHAR" property="enterpriseHomeAddress" />
    <result column="enterprise_detail_address" jdbcType="VARCHAR" property="enterpriseDetailAddress" />
    <result column="license_url" jdbcType="VARCHAR" property="licenseUrl" />
    <result column="license_start_time" jdbcType="TIMESTAMP" property="licenseStartTime" />
    <result column="license_end_time" jdbcType="TIMESTAMP" property="licenseEndTime" />
    <result column="transport_license_url" jdbcType="VARCHAR" property="transportLicenseUrl" />
    <result column="sign_type" jdbcType="INTEGER" property="signType" />
    <result column="contract_no" jdbcType="VARCHAR" property="contractNo" />
    <result column="contract_start_time" jdbcType="TIMESTAMP" property="contractStartTime" />
    <result column="contract_end_time" jdbcType="TIMESTAMP" property="contractEndTime" />
    <result column="contract_url" jdbcType="VARCHAR" property="contractUrl" />
    <result column="certigier_user_id" jdbcType="BIGINT" property="certigierUserId" />
    <result column="certigier_user_phone" jdbcType="VARCHAR" property="certigierUserPhone" />
    <result column="certigier_user_name" jdbcType="VARCHAR" property="certigierUserName" />
    <result column="authorization_url" jdbcType="VARCHAR" property="authorizationUrl" />
    <result column="enterprise_tax_rate" jdbcType="DECIMAL" property="enterpriseTaxRate" />
    <result column="enterprise_verify_status" jdbcType="INTEGER" property="enterpriseVerifyStatus" />
    <result column="info_verify_status" jdbcType="INTEGER" property="infoVerifyStatus" />
    <result column="info_verify_reason" jdbcType="VARCHAR" property="infoVerifyReason" />
    <result column="info_audit_time" jdbcType="TIMESTAMP" property="infoAuditTime" />
    <result column="contract_verify_status" jdbcType="INTEGER" property="contractVerifyStatus" />
    <result column="contract_verify_reason" jdbcType="VARCHAR" property="contractVerifyReason" />
    <result column="certigier_verify_status" jdbcType="INTEGER" property="certigierVerifyStatus" />
    <result column="certigier_verify_reason" jdbcType="VARCHAR" property="certigierVerifyReason" />
    <result column="account_status" jdbcType="INTEGER" property="accountStatus" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="transport_license_status" jdbcType="INTEGER" property="transportLicenseStatus" />
    <result column="transport_reject_reason" jdbcType="VARCHAR" property="transportRejectReason" />
    <result column="transport_audit_time" jdbcType="TIMESTAMP" property="transportAuditTime" />
    <result column="licensePermanent" jdbcType="INTEGER" property="licensePermanent" />
  </resultMap>

  <select id="getInoviceEnterpriseExpiredCount" resultType="java.lang.Integer">
    select count(1) from tyt_invoice_enterprise where info_verify_status = 2 and license_end_time is not null
  </select>

  <select id="getInoviceEnterpriseExpiredList" resultMap="BaseResultMap">
    select
      id, enterprise_name, legal_person_name, legal_person_phone, legal_person_card, legal_person_card_url_g
         , legal_person_card_url_t, enterprise_credit_code, enterprise_type, enterprise_business_scope
         , enterprise_home_address, enterprise_detail_address, license_url, license_end_time, transport_license_url
         , sign_type, contract_no, contract_start_time, contract_end_time, contract_url, certigier_user_id
         , certigier_user_name, certigier_user_phone, authorization_url, enterprise_tax_rate, enterprise_verify_status
         , info_verify_status, info_verify_reason, contract_verify_status, contract_verify_reason
         , certigier_verify_status, certigier_verify_reason, account_status, ctime, mtime, remark, info_audit_time
         , transport_reject_reason, transport_license_status, transport_audit_time, real_verify, license_permanent
    from
      tyt_invoice_enterprise
    where
      id > #{lastInoviceEnterpriseId}
      and info_verify_status = 2
      and license_permanent != 1
      and license_end_time is not null
    order by id limit #{pageSize}
  </select>

  <update id="doInoviceEnterpriseAuthFailed">
    update tyt_invoice_enterprise set
    info_verify_status = 3,
    info_verify_reason = '营业执照过期',
    info_audit_time = NOW(),
    info_audit_user_id = null,
    info_audit_user_name = '自动审核',
    <if test="transportLicenseStatus != null and transportLicenseStatus != 0">
      transport_license_status = 3,
      transport_reject_reason = '营业执照过期',
      transport_audit_time = NOW(),
      transport_audit_user_id = null,
      transport_audit_user_name = '自动审核',
    </if>
    enterprise_verify_status = 3,
    <!-- 驳回 -->
    legal_person_phone = null,
    legal_person_card = null,
    sign_type = null,
    contract_no = null,
    contract_start_time = null,
    contract_end_time = null,
    manager_flag = 0,
    contract_verify_status = 0,
    account_status = null,
    <if test="contractVerifyStatus != null and contractVerifyStatus == 2">
      contract_final_status = 3,
      contract_final_reason = '营业执照过期驳回',
    </if>
    <if test="contractVerifyStatus != 2">
      contract_final_status = 0,
      contract_final_reason = null,
    </if>
    mtime = NOW()
    where id = #{id}
  </update>

  <update id="updateEnterpriseAuthStatusByUserIdInt">
    update tyt_user_identity_auth SET enterprise_auth_status = 3 where user_id = #{userId}
  </update>

  <select id="getContractSuccessList"
          resultType="com.teyuntong.infra.task.service.biz.user.enterprise.pojo.TytInvoiceEnterprise" parameterType="long">
    select id, contract_end_time contractEndTime
    from tyt_invoice_enterprise
    where contract_final_status = 2
    <if test="id != null">
      and id > #{id}
    </if>
    order by id limit 50
  </select>

  <update id="updateContractEndTime">
    update tyt_invoice_enterprise set
      contract_end_time = #{newContractEndTime},
      contract_continue_time = NOW(),
      mtime = NOW()
    where id = #{id}
  </update>

  <select id="getTaxRateFloatOpenList" resultType="com.teyuntong.infra.task.service.biz.user.enterprise.pojo.TytInvoiceEnterprise">
    select id, enterprise_credit_code enterpriseCreditCode, enterprise_tax_rate enterpriseTaxRate,
        contract_start_time contractStartTime, contract_continue_time contractContinueTime
    from tyt_invoice_enterprise
    where tax_rate_float = 1
    <if test="id != null">
      and id > #{id}
    </if>
    order by id limit 50
  </select>

  <update id="updateCurrentTaxRate">
        update tyt_invoice_enterprise set
          enterprise_tax_rate = #{taxRateForGmv},
          mtime = NOW()
        where id = #{enterpriseId}
  </update>

  <select id="getNeedOcrList" resultMap="BaseResultMap">
    select id, enterprise_name, license_url,license_start_time,license_end_time,license_permanent
    from tyt_invoice_enterprise
    where id &gt; #{maxId} and license_url !='' and ctime &gt; '2023-08-01' order by id asc limit 50
  </select>
</mapper>
