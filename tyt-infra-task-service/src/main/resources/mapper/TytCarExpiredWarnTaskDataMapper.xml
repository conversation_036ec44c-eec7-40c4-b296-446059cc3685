<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.car.mybatis.mapper.TytCarExpiredWarnTaskDataMapper">
  <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.car.mybatis.entity.TytCarExpiredWarnTaskData">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="car_id" jdbcType="BIGINT" property="carId" />
    <result column="car_city" jdbcType="VARCHAR" property="carCity" />
    <result column="car_no" jdbcType="VARCHAR" property="carNo" />
    <result column="sms_content" jdbcType="VARCHAR" property="smsContent" />
    <result column="status" jdbcType="INTEGER" property="status" />
  </resultMap>

  <insert id="insertIgnoreList" parameterType="list">
    insert ignore into tyt_car_expired_warn_task_data (user_id, car_id, car_city, car_no, sms_content, status) VALUES
    <foreach collection="warnDataList" index="index" item="item" separator=",">
      (
      #{item.userId}
      , #{item.carId}
      , #{item.carCity}
      , #{item.carNo}
      , #{item.smsContent}
      , #{item.status}
      )
    </foreach>
  </insert>

  <update id="setDoneById">
    update tyt_car_expired_warn_task_data set status = 1 where id = #{tytCarExpiredWarnTaskDatumId}
  </update>

  <select id="getAllDataStatusIsZero" resultMap="BaseResultMap">
    select id, user_id, car_id, car_city, car_no, sms_content, status from tyt_car_expired_warn_task_data where status = 0
  </select>

  <delete id="cleanAllData">
    delete from tyt_car_expired_warn_task_data where 1 = 1
  </delete>

</mapper>