<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.info.mybatis.mapper.CsAutomaticAssignLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.info.mybatis.entity.CsAutomaticAssignLog">
        <id column="id" property="id" />
        <id column="assign_id" property="assignId" />
        <result column="type" property="type" />
        <result column="user_member" property="userMember" />
        <result column="user_identity" property="userIdentity" />
        <result column="department" property="department" />
        <result column="custom_id" property="customId" />
        <result column="identity" property="identity" />
        <result column="maintainer_id" property="maintainerId" />
        <result column="maintainer_name" property="maintainerName" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, assign_id, type, user_member, user_identity, department, custom_id, identity, maintainer_id, maintainer_name, create_time, modify_time
    </sql>

</mapper>
