<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.info.mybatis.mapper.UserSecurityFlowMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.info.mybatis.entity.UserSecurityFlowDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="security_user_id" property="securityUserId" />
        <result column="security_user_phone" property="securityUserPhone" />
        <result column="source_type" property="sourceType" />
        <result column="source_id" property="sourceId" />
        <result column="source_desc" property="sourceDesc" />
        <result column="money_amount" property="moneyAmount" />
        <result column="before_amount" property="beforeAmount" />
        <result column="after_amount" property="afterAmount" />
        <result column="status" property="status" />
        <result column="manager_user_id" property="managerUserId" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, security_user_id, security_user_phone, source_type, source_id, source_desc, money_amount, before_amount, after_amount, status, manager_user_id, create_time, modify_time
    </sql>

</mapper>
