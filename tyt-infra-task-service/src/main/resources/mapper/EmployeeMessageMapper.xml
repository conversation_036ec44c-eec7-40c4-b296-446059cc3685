<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.message.center.mapper.EmployeeMessageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.message.center.entity.EmployeeMessage">
        <id column="id" property="id" />
        <result column="title" property="title" />
        <result column="content" property="content" />
        <result column="remarks" property="remarks" />
        <result column="push_user_id" property="pushUserId" />
        <result column="push_user_name" property="pushUserName" />
        <result column="ctime" property="ctime" />
        <result column="operation_time" property="operationTime" />
        <result column="type" property="type" />
        <result column="read_status" property="readStatus" />
        <result column="read_time" property="readTime" />
        <result column="user_id" property="userId" />
        <result column="user_name" property="userName" />
        <result column="c1" property="c1" />
        <result column="c2" property="c2" />
        <result column="c3" property="c3" />
        <result column="c4" property="c4" />
        <result column="c5" property="c5" />
        <result column="push_status" property="pushStatus" />
        <result column="push_time" property="pushTime" />
        <result column="utime" property="utime" />
        <result column="status" property="status" />
        <result column="tmpl_id" property="tmplId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, title, content, remarks, push_user_id, push_user_name, ctime, operation_time, type, read_status, read_time, user_id, user_name, c1, c2, c3, c4, c5, push_status, push_time, utime, status, tmpl_id
    </sql>

	<insert id="batchInsert">
        insert into employee_message
            (title, content, push_user_id, push_user_name, ctime, operation_time, type, user_id, user_name)
        values
        <foreach collection="messageList" item="item" separator=",">
            (#{item.title}, #{item.content}, #{item.pushUserId}, #{item.pushUserName}, #{item.ctime}, #{item.operationTime}, #{item.type}, #{item.userId}, #{item.userName})
        </foreach>
    </insert>

</mapper>
