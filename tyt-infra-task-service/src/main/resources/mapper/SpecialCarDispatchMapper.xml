<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.dispatch.mapper.SpecialCarDispatchMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.dispatch.entity.SpecialCarDispatchDO">
        <id column="id" property="id" />
        <result column="ts_id" property="tsId" />
        <result column="publish_user_type" property="publishUserType" />
        <result column="dispatch_type" property="dispatchType" />
        <result column="after_minutes" property="afterMinutes" />
        <result column="dispatch_user_id" property="dispatchUserId" />
        <result column="dispatch_user_name" property="dispatchUserName" />
        <result column="dispatch_cell_phone" property="dispatchCellPhone" />
        <result column="dispatch_ding_talk_phone" property="dispatchDingTalkPhone" />
        <result column="first_dispatch_time" property="firstDispatchTime" />
        <result column="last_dispatch_time" property="lastDispatchTime" />
        <result column="user_count" property="userCount" />
        <result column="dispatch_user_count" property="dispatchUserCount" />
        <result column="accept_status" property="acceptStatus" />
        <result column="accept_time" property="acceptTime" />
        <result column="accept_user_id" property="acceptUserId" />
        <result column="notify_count" property="notifyCount" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
        <result column="dispatch_complete" property="dispatchComplete" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, ts_id, publish_user_type, dispatch_type, after_minutes, dispatch_complete, dispatch_user_id, dispatch_user_name, dispatch_cell_phone, dispatch_ding_talk_phone, first_dispatch_time, last_dispatch_time, user_count, dispatch_user_count, accept_status, accept_time, accept_user_id, notify_count, create_time, modify_time
    </sql>

    <select id="selectUnDispatchGoodsV2" resultMap="BaseResultMap">
        SELECT * FROM
        tyt_special_car_dispatch
        WHERE
        accept_status = 0
        AND
        first_dispatch_time IS NULL
        AND
        dispatch_user_count = 0
        AND
        notify_count = 0
        AND
        dispatch_type = 1
        AND
        create_time &gt; #{todayDate};
    </select>

    <select id="selectUnDispatchGoodsV3" resultMap="BaseResultMap">
        SELECT * FROM
            tyt_special_car_dispatch
        WHERE
            accept_status = 0
          AND
            dispatch_complete = 0
          AND
            dispatch_type = 1
          AND
            create_time &gt; #{todayDate};
    </select>

    <select id="selectDispatchFailOrdersV2" resultMap="BaseResultMap">
        SELECT * FROM
        tyt_special_car_dispatch
        WHERE
        accept_status = 0
        AND
        (
        (first_dispatch_time IS NOT NULL AND first_dispatch_time &lt; #{thresholdTime})
        OR
        (first_dispatch_time IS NULL AND user_count = 0 AND dispatch_user_count = 0)
        )
        AND
        notify_count = 0
        AND
        create_time &gt; #{todayDate};
    </select>

    <select id="selectDispatchFailOrdersV3" resultMap="BaseResultMap">
        SELECT * FROM
            tyt_special_car_dispatch
        WHERE
            accept_status = 0
          AND
            notify_count = 0
          AND
            create_time &gt; #{todayDate};
    </select>

    <select id="selectDispatchInOrderRecords" resultMap="BaseResultMap">
        SELECT *
        FROM tyt_special_car_dispatch
        WHERE
            accept_status = 0
        AND dispatch_type = 2
        AND dispatch_user_count &lt; user_count
        AND create_time > current_date()
        UNION
        SELECT *
        FROM tyt_special_car_dispatch
        WHERE
            accept_status = 0
          AND dispatch_type = 2
          AND user_count = 0
          AND first_dispatch_time is null
          AND create_time > current_date()
    </select>

    <!-- 更新一次性指派司机数量 -->
    <update id="updateDispatchCarAmount">
        UPDATE tyt_special_car_dispatch
        SET
        <if test="firstDispatchTime == null">
            first_dispatch_time = #{nowDate},
        </if>
        <if test="dispatchComplete != null">
            dispatch_complete = #{dispatchComplete},
        </if>
        last_dispatch_time = #{nowDate},
        dispatch_user_count = #{dispatchCarAmount},
        modify_time = #{nowDate}
        WHERE id = #{id}
    </update>

    <!-- 更新派单失败通知次数 -->
    <update id="updateNotifyCountAndModifyTime">
        UPDATE tyt_special_car_dispatch
        SET notify_count = #{notifyCount},
        modify_time = #{nowDate}
        WHERE id = #{id}
    </update>
</mapper>
