<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.trade.infofee.mapper.NoticeRemindTmplMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.teyuntong.infra.task.service.biz.trade.infofee.entity.NoticeRemindTmplDO">
        <id column="id" property="id"/>
        <result column="type1" property="type1"/>
        <result column="type2" property="type2"/>
        <result column="remark" property="remark"/>
        <result column="priority" property="priority"/>
        <result column="is_popup" property="isPopup"/>
        <result column="content" property="content"/>
        <result column="status" property="status"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, type1, type2, remark, priority, is_popup, content, status
    </sql>

    <select id="getTytNoticeRemindTmpl"
            resultType="com.teyuntong.infra.task.service.biz.trade.infofee.entity.NoticeRemindTmplDO">
        select * from tyt_notice_remind_tmpl where type1=#{type1} and type2=#{type2} and status=0 order by id desc limit 1;
    </select>

</mapper>
