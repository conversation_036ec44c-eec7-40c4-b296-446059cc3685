<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.trade.infofee.mapper.TransportWaybillMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.trade.infofee.entity.TransportWaybillDO">
        <id column="ts_order_no" property="tsOrderNo" />
        <result column="ts_id" property="tsId" />
        <result column="sort_id" property="sortId" />
        <result column="is_info_fee" property="isInfoFee" />
        <result column="info_status" property="infoStatus" />
        <result column="time_limit_identification" property="timeLimitIdentification" />
        <result column="start_point" property="startPoint" />
        <result column="dest_point" property="destPoint" />
        <result column="task_content" property="taskContent" />
        <result column="tel" property="tel" />
        <result column="pub_time" property="pubTime" />
        <result column="ctime" property="ctime" />
        <result column="upload_cellphone" property="uploadCellphone" />
        <result column="tel3" property="tel3" />
        <result column="tel4" property="tel4" />
        <result column="user_id" property="userId" />
        <result column="pub_user_name" property="pubUserName" />
        <result column="linkman" property="linkman" />
        <result column="guarantee_goods" property="guaranteeGoods" />
        <result column="create_time" property="createTime" />
        <result column="pay_user_id" property="payUserId" />
        <result column="pay_user_name" property="payUserName" />
        <result column="pay_cell_phone" property="payCellPhone" />
        <result column="pay_link_phone" property="payLinkPhone" />
        <result column="pay_amount" property="payAmount" />
        <result column="pay_time" property="payTime" />
        <result column="pay_number" property="payNumber" />
        <result column="agree_time" property="agreeTime" />
        <result column="load_time" property="loadTime" />
        <result column="mtime" property="mtime" />
        <result column="machine_remark" property="machineRemark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ts_order_no, ts_id, sort_id, is_info_fee, info_status, time_limit_identification, start_point, dest_point, task_content, tel, pub_time, ctime, upload_cellphone, tel3, tel4, user_id, pub_user_name, linkman, guarantee_goods, create_time, pay_user_id, pay_user_name, pay_cell_phone, pay_link_phone, pay_amount, pay_time, pay_number, agree_time, load_time, mtime, machine_remark
    </sql>

    <update id="updateTransportWayBillByParam">
        update tyt_transport_waybill set info_status=#{infoStatus} ,load_time=now(),mtime=now() where ts_order_no=#{tsOrderNo}
    </update>


    <select id="getTytTransportWaybillForFinishRemind" resultType="com.teyuntong.infra.task.service.biz.trade.infofee.entity.TransportWaybillDO">
        select ts_order_no tsOrderNo,agree_time agreeTime,pay_user_id payUserId,info_status infoStatus from tyt_transport_waybill where is_info_fee=#{isInfoFee} and
        info_status=#{infoStatus} and agree_time>#{beginTime} and agree_time &lt;= #{endTime}
    </select>
</mapper>
