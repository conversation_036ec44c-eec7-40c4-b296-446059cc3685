<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.ymm.mybatis.TytSyncgoodsDefinitionRuleMapper">

    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.ymm.entity.TytSyncgoodsDefinitionRule">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="rule_group_id" property="ruleGroupId" jdbcType="INTEGER"/>
        <result column="good_model_score_min" property="goodModelScoreMin" jdbcType="DECIMAL"/>
        <result column="good_model_score_max" property="goodModelScoreMax" jdbcType="DECIMAL"/>
        <result column="good_transport_labe" property="goodTransportLabe" jdbcType="VARCHAR"/>
        <result column="distance_min" property="distanceMin" jdbcType="DECIMAL"/>
        <result column="distance_max" property="distanceMax" jdbcType="DECIMAL"/>
        <result column="transport_user_identity_type" property="transportUserIdentityType" jdbcType="VARCHAR"/>
        <result column="apply_normal_goods" property="applyNormalGoods" jdbcType="TINYINT"/>
        <result column="apply_excellent_goods" property="applyExcellentGoods" jdbcType="TINYINT"/>
        <result column="apply_excellent_goods_two" property="applyExcellentGoodsTwo" jdbcType="TINYINT"/>
        <result column="apply_special_car" property="applySpecialCar" jdbcType="TINYINT"/>
        <result column="publish_type_fixed_price" property="publishTypeFixedPrice" jdbcType="TINYINT"/>
        <result column="publish_type_no_price" property="publishTypeNoPrice" jdbcType="TINYINT"/>
        <result column="publish_type_call_price" property="publishTypeCallPrice" jdbcType="TINYINT"/>
        <result column="data_integrity_min" property="dataIntegrityMin" jdbcType="TINYINT"/>
        <result column="first_publish_timeout_minutes" property="firstPublishTimeoutMinutes" jdbcType="INTEGER"/>
        <result column="enable_view_count_check" property="enableViewCountCheck" jdbcType="TINYINT"/>
        <result column="view_count_timeout_minutes" property="viewCountTimeoutMinutes" jdbcType="INTEGER"/>
        <result column="view_count_threshold" property="viewCountThreshold" jdbcType="INTEGER"/>
        <result column="enable_call_count_check" property="enableCallCountCheck" jdbcType="TINYINT"/>
        <result column="call_count_timeout_minutes" property="callCountTimeoutMinutes" jdbcType="INTEGER"/>
        <result column="call_count_threshold" property="callCountThreshold" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="ctime" property="ctime" jdbcType="TIMESTAMP"/>
        <result column="create_user_id" property="createUserId" jdbcType="BIGINT"/>
        <result column="create_username" property="createUsername" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="getEnableRuleListByRuleGroupId" resultMap="BaseResultMap">
        select * from tyt_syncgoods_definition_rule where rule_group_id = #{ruleGroupId} and status = 1
    </select>

</mapper>
