<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.trade.infofee.mapper.DepositAccountMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.trade.infofee.entity.DepositAccount">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="balance" property="balance" />
        <result column="type" property="type" />
        <result column="remark" property="remark" />
        <result column="ctime" property="ctime" />
        <result column="mtime" property="mtime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, balance, type, remark, ctime, mtime
    </sql>

    <select id="getUserBalance" resultMap="BaseResultMap">
        SELECT *
        FROM tyt_deposit_account
        WHERE user_id = #{userId}
    </select>
</mapper>
