<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.market.activity.mybatis.mapper.AutomaticInformBlackListMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.AutomaticInformBlackListDO">
        <id column="id" property="id" />
        <result column="rule_id" property="ruleId" />
        <result column="user_id" property="userId" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, rule_id, user_id, status, create_time, modify_time
    </sql>

    <select id="getBlackList" resultType="java.lang.Long">
        select user_id from tyt_automatic_inform_black_list where rule_id=#{ruleId} and status=1
    </select>

</mapper>
