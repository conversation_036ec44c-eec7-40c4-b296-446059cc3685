<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.info.mybatis.mapper.TytPreferenceMapper">

    <update id="updateFindGoodOnOff">
        UPDATE tyt_recommend.tyt_preference c
        SET find_good_onoff = 0
        WHERE EXISTS
            (SELECT id
             FROM tyt_user  u
             WHERE u.id=c.user_id
               AND end_time &lt;= CONCAT(DATE_FORMAT(NOW(),'%Y-%m-%d'),' 00:00:00'))
          AND find_good_onoff = 1
    </update>
</mapper>
