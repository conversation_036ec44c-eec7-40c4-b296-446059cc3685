<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.goods.goodsinfo.mapper.TransportExposureResultMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.goods.goodsinfo.entity.TransportExposureResultDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="src_msg_id" property="srcMsgId" />
        <result column="show_count" property="showCount" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, src_msg_id, show_count, create_time, modify_time
    </sql>

    <select id="countCouponExpire" resultType="java.lang.Integer">
        select count(*)
        from tyt_transport_exposure_result
        where create_time &gt;= #{defaultStartDate} and create_time &lt; #{defaultEndDate}
    </select>

    <select id="getUserExposureCount"
            resultType="com.teyuntong.infra.task.service.biz.goods.goodsinfo.bean.TransportExposureResultVo">
        select
            t.user_id userId , count(*) count
        FROM tyt_transport_exposure_result t
        WHERE
            t.create_time >= #{startDate}
          AND t.create_time &lt; #{endDate}
        GROUP BY t.user_id
            limit #{offset}, #{pageSize}
    </select>

</mapper>
