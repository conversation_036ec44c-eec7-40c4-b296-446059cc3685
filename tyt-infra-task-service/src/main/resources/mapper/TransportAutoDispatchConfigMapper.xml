<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.dispatch.mapper.TransportAutoDispatchConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.dispatch.entity.TransportAutoDispatchConfigDO">
        <id column="id" property="id" />
        <result column="start_city" property="startCity" />
        <result column="dest_city" property="destCity" />
        <result column="citys" property="citys" />
        <result column="distance_type" property="distanceType" />
        <result column="match_goods_score" property="matchGoodsScore" />
        <result column="goods_score_config" property="goodsScoreConfig" />
        <result column="goods_type" property="goodsType" />
        <result column="price_type" property="priceType" />
        <result column="include_invoice" property="includeInvoice" />
        <result column="after_minutes" property="afterMinutes" />
        <result column="distance_limit" property="distanceLimit" />
        <result column="status" property="status" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
        <result column="op_user_id" property="opUserId" />
        <result column="op_user_name" property="opUserName" />
        <result column="del_status" property="delStatus" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, start_city, dest_city, match_goods_score, goods_score_config, goods_type, price_type, include_invoice, after_minutes, distance_limit, status, remark, create_time, modify_time, op_user_id, op_user_name, del_status
    </sql>

	<select id="selectAllEnableConfig" resultMap="BaseResultMap">
        select *
        from tyt_transport_auto_dispatch_config
        where del_status = 0 and status = 1
        limit #{start}, #{pageSize}
    </select>

	<select id="selectByRoute"
	        resultType="com.teyuntong.infra.task.service.biz.dispatch.entity.TransportAutoDispatchConfigDO">
        select *
        from tyt_transport_auto_dispatch_config
        where del_status = 0 and status = 1 and start_city = #{startCity} and dest_city = #{destCity}
        limit 1
    </select>

</mapper>
