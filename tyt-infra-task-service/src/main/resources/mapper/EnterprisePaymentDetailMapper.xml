<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.enterprise.mybatis.EnterprisePaymentDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.enterprise.mybatis.entity.EnterprisePaymentDetailDO">
        <id column="id" property="id" />
        <result column="enterprise_id" property="enterpriseId" />
        <result column="enterprise_name" property="enterpriseName" />
        <result column="enterprise_credit_code" property="enterpriseCreditCode" />
        <result column="order_id" property="orderId" />
        <result column="driver_confirm_amount" property="driverConfirmAmount" />
        <result column="additional_price" property="additionalPrice" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
        <result column="del_status" property="delStatus" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, enterprise_id, enterprise_name, enterprise_credit_code, order_id, member_id, orders_freight_id, driver_confirm_amount, additional_price, create_time, modify_time, del_status
    </sql>

</mapper>
