<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.trade.infofee.mapper.TytUserSubMapper">

    <update id="updateDealNum" parameterType="java.util.Map">
        UPDATE tyt.`tyt_user_sub` tus
        RIGHT JOIN (
        SELECT
        un.user_id,
        count(*) ucount
        FROM
        (
        SELECT
        tto.`user_id`
        FROM
        tyt.`tyt_transport_orders` tto
        WHERE
        tto.`load_time` &gt; #{loadTime}
        AND tto.`load_time` &lt; #{loadTime1}
        AND (( tto.`rob_status` = #{robStatus} OR tto.`rob_status` = #{robStatus1})
        OR  (tto.refund_reason IN (#{reason1},#{reason2},#{reason3},#{reason4}) AND tto.refund_flag = 1 ))
        GROUP BY
        tto.`ts_id`
        UNION ALL
        SELECT
        tto.`pay_user_id` AS user_id
        FROM
        tyt.`tyt_transport_orders` tto
        WHERE
        tto.`load_time` &gt; #{loadTime}
        AND tto.`load_time` &lt; #{loadTime1}
        AND (( tto.`rob_status` = #{robStatus} OR tto.`rob_status` = #{robStatus1} )
        OR  (tto.refund_reason IN (#{reason1},#{reason2},#{reason3},#{reason4}) AND tto.refund_flag = 1 ))
        GROUP BY
        tto.`ts_id`
        ) un
        GROUP BY
        un.user_id
        ) temp ON tus.`user_id` = temp.user_id
        SET tus.`deal_num` = ifnull( tus.`deal_num`, 0 ) + temp.ucount
    </update>
    <select id="selectByUserId"
            resultType="com.teyuntong.infra.task.service.biz.trade.infofee.entity.TytUserSub">
        select * from tyt.`tyt_user_sub` where user_id = #{userId} limit 1
    </select>

</mapper>
