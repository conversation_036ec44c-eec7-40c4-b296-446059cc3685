<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.permission.mybatis.mapper.ExposurePermissionExpiredRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.permission.mybatis.entity.ExposurePermissionExpiredRecordDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="expired_before_num" property="expiredBeforeNum" />
        <result column="expired_num" property="expiredNum" />
        <result column="expired_after_num" property="expiredAfterNum" />
        <result column="expired_time" property="expiredTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, expired_before_num, expired_num, expired_after_num, expired_time
    </sql>

</mapper>
