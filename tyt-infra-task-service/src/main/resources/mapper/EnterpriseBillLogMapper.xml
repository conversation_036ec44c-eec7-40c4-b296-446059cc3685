<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.enterprise.mybatis.EnterpriseBillLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.enterprise.mybatis.entity.EnterpriseBillLogDO">
        <id column="id" property="id" />
        <result column="enterprise_id" property="enterpriseId" />
        <result column="enterprise_name" property="enterpriseName" />
        <result column="member_id" property="memberId" />
        <result column="enterprise_credit_code" property="enterpriseCreditCode" />
        <result column="trade_no" property="tradeNo" />
        <result column="biz_order_no" property="bizOrderNo" />
        <result column="order_no" property="orderNo" />
        <result column="payment_seq_no" property="paymentSeqNo" />
        <result column="trade_type" property="tradeType" />
        <result column="amount" property="amount" />
        <result column="trade_date" property="tradeDate" />
        <result column="product_name" property="productName" />
        <result column="product_desc" property="productDesc" />
        <result column="order_amount" property="orderAmount" />
        <result column="status" property="status" />
        <result column="tag" property="tag" />
        <result column="submit_time" property="submitTime" />
        <result column="pay_method" property="payMethod" />
        <result column="pay_method_amount" property="payMethodAmount" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, enterprise_id, enterprise_name, member_id, trade_no, biz_order_no, order_no, payment_seq_no, trade_type, amount, trade_date, direction, txn_amt, before_amt, after_amt, product_name, product_desc, order_amount, status, tag, submit_time, pay_method, pay_method_amount, create_time, modify_time
    </sql>


    <select id="queryBillCountByParam" resultType="java.lang.Integer">
        select count(*)
        from tyt_enterprise_bill_log
        where enterprise_credit_code = #{enterPriseCreditCode}
          and create_time >= #{startDate}
    </select>

    <insert id="batchBillLogInsert" parameterType="java.util.List">
        INSERT INTO tyt_enterprise_bill_log (enterprise_id,
        enterprise_name,member_id,enterprise_credit_code,trade_no,biz_order_no,order_no,
        payment_seq_no,trade_type,amount,trade_date,product_name,product_desc,order_amount,status,tag,submit_time,pay_method,pay_method_amount,create_time,modify_time)
        VALUES
        <foreach collection="billLogList" item="item" index="index" separator=",">
            (#{item.enterpriseId}, #{item.enterpriseName}, #{item.memberId}, #{item.enterpriseCreditCode},
            #{item.tradeNo}, #{item.bizOrderNo}, #{item.orderNo},
            #{item.paymentSeqNo},#{item.tradeType}, #{item.amount}, #{item.tradeDate}, #{item.productName},
            #{item.productDesc}, #{item.orderAmount}, #{item.status}, #{item.tag}, #{item.submitTime},
            #{item.payMethod}, #{item.payMethodAmount},
            #{item.createTime}, #{item.modifyTime})
        </foreach>
    </insert>


</mapper>
