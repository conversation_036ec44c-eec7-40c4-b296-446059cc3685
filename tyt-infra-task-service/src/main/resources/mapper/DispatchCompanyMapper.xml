<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.trade.infofee.mapper.DispatchCompanyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.trade.infofee.entity.DispatchCompanyDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="user_name" property="userName" />
        <result column="true_name" property="trueName" />
        <result column="cell_phone" property="cellPhone" />
        <result column="ding_talk_phone" property="dingTalkPhone" />
        <result column="accept_status" property="acceptStatus" />
        <result column="rotate_status" property="rotateStatus" />
        <result column="is_valid" property="isValid" />
        <result column="ctime" property="ctime" />
        <result column="mtime" property="mtime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, user_name, true_name, cell_phone, ding_talk_phone, accept_status, is_valid, ctime, mtime
    </sql>

	<select id="selectByUserId" resultMap="BaseResultMap">
        select *
        from tyt_dispatch_company
        where user_id = #{userId}
        order by id desc limit 1
    </select>

</mapper>
