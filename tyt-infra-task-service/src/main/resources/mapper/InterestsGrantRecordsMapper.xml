<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.permission.mybatis.mapper.InterestsGrantRecordsMapper">
  <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.permission.mybatis.entity.InterestsGrantRecords">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="src_msg_id" jdbcType="BIGINT" property="srcMsgId" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="grant_counts" jdbcType="INTEGER" property="grantCounts" />
    <result column="enabled" jdbcType="INTEGER" property="enabled" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>

  <select id="selectTransportGiveExposureInfo" resultType="com.teyuntong.infra.task.service.biz.user.permission.mybatis.entity.TransportGiveExposureDto">
    SELECT user_id AS userId,
    ( SELECT count( id ) FROM interests_grant_records WHERE user_id = userId AND ctime >= #{startTime,jdbcType=TIMESTAMP} AND ctime &lt;= #{endTime,jdbcType=TIMESTAMP} AND `status` = 0 AND enabled = 1 AND grant_counts >0) AS publishNum
    FROM interests_grant_records
    WHERE ctime >= #{startTime,jdbcType=TIMESTAMP}
    AND ctime &lt;= #{endTime,jdbcType=TIMESTAMP}
    AND `status` = 0
    AND enabled = 1
    AND grant_counts >0
    GROUP BY
    user_id
    </select>


  <update id="updateStatusByUserId">
    update interests_grant_records set `status` = 1 ,mtime = now()
    where  user_id = #{userId,jdbcType=BIGINT}
      AND ctime >= #{startTime,jdbcType=TIMESTAMP}
      AND ctime &lt;= #{endTime,jdbcType=TIMESTAMP}
      AND `status` = 0
      AND enabled = 1
      AND grant_counts >0
  </update>

</mapper>