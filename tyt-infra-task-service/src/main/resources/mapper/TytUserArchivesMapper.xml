<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.car.mybatis.mapper.TytUserArchivesMapper">
  <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.car.mybatis.entity.TytUserArchives">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="user_nickname" jdbcType="VARCHAR" property="userNickname" />
    <result column="user_cell_phone" jdbcType="VARCHAR" property="userCellPhone" />
    <result column="user_true_name" jdbcType="VARCHAR" property="userTrueName" />
    <result column="identity" jdbcType="VARCHAR" property="identity" />
    <result column="goods_identity" jdbcType="VARCHAR" property="goodsIdentity" />
    <result column="register_time" jdbcType="TIMESTAMP" property="registerTime" />
    <result column="identity_auth" jdbcType="SMALLINT" property="identityAuth" />
    <result column="car_auth" jdbcType="SMALLINT" property="carAuth" />
    <result column="user_car_auth" jdbcType="INTEGER" property="userCarAuth" />
    <result column="enter_auth" jdbcType="INTEGER" property="enterAuth" />
    <result column="deliver_type_one" jdbcType="VARCHAR" property="deliverTypeOne" />
    <result column="last_login_time" jdbcType="TIMESTAMP" property="lastLoginTime" />
    <result column="car_vip_label" jdbcType="VARCHAR" property="carVipLabel" />
    <result column="carvip_due_date" jdbcType="TIMESTAMP" property="carvipDueDate" />
    <result column="good_vip_label" jdbcType="VARCHAR" property="goodVipLabel" />
    <result column="goodvip_due_date" jdbcType="TIMESTAMP" property="goodvipDueDate" />
    <result column="deliver_goods_label" jdbcType="VARCHAR" property="deliverGoodsLabel" />
    <result column="deliver_goods_due_date" jdbcType="TIMESTAMP" property="deliverGoodsDueDate" />
    <result column="carvip_first_pay_date" jdbcType="TIMESTAMP" property="carvipFirstPayDate" />
    <result column="carvip_last_pay_date" jdbcType="TIMESTAMP" property="carvipLastPayDate" />
    <result column="carvip_pay_times" jdbcType="INTEGER" property="carvipPayTimes" />
    <result column="goodvip_first_pay_date" jdbcType="TIMESTAMP" property="goodvipFirstPayDate" />
    <result column="goodvip_last_pay_date" jdbcType="TIMESTAMP" property="goodvipLastPayDate" />
    <result column="goodvip_pay_times" jdbcType="INTEGER" property="goodvipPayTimes" />
    <result column="deliver_goods_first_pay_date" jdbcType="TIMESTAMP" property="deliverGoodsFirstPayDate" />
    <result column="deliver_goods_last_pay_date" jdbcType="TIMESTAMP" property="deliverGoodsLastPayDate" />
    <result column="deliver_goods_pay_times" jdbcType="INTEGER" property="deliverGoodsPayTimes" />
    <result column="fist_send_goods_date" jdbcType="TIMESTAMP" property="fistSendGoodsDate" />
    <result column="last_send_goods_date" jdbcType="TIMESTAMP" property="lastSendGoodsDate" />
    <result column="first_call_phone_date" jdbcType="TIMESTAMP" property="firstCallPhoneDate" />
    <result column="last_call_phone_date" jdbcType="TIMESTAMP" property="lastCallPhoneDate" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="utime" jdbcType="TIMESTAMP" property="utime" />
    <result column="goods_order_no" jdbcType="INTEGER" property="goodsOrderNo" />
    <result column="car_order_no" jdbcType="INTEGER" property="carOrderNo" />
    <result column="good_goods_order_no" jdbcType="INTEGER" property="goodGoodsOrderNo" />
    <result column="plat_publish_no" jdbcType="INTEGER" property="platPublishNo" />
    <result column="car_favorable_rate" jdbcType="INTEGER" property="carFavorableRate" />
    <result column="car_favorable_no" jdbcType="INTEGER" property="carFavorableNo" />
    <result column="goods_favorable_rate" jdbcType="INTEGER" property="goodsFavorableRate" />
    <result column="goods_favorable_no" jdbcType="INTEGER" property="goodsFavorableNo" />
    <result column="car_last_login_time" jdbcType="TIMESTAMP" property="carLastLoginTime" />
    <result column="goods_last_login_time" jdbcType="TIMESTAMP" property="goodsLastLoginTime" />
    <result column="client_sign" jdbcType="SMALLINT" property="clientSign" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="agent_publish_count" jdbcType="INTEGER" property="agentPublishCount" />
    <result column="during_count" jdbcType="INTEGER" property="duringCount" />
    <result column="bought_vip" jdbcType="INTEGER" property="boughtVip" />
    <result column="pay_infee_last_time" jdbcType="TIMESTAMP" property="payInfeeLastTime" />
    <result column="plat_id" jdbcType="INTEGER" property="platId" />

    <result column="deposit_user" jdbcType="INTEGER" property="depositUser" />
    <result column="deposit_time" jdbcType="TIMESTAMP" property="depositTime" />

    <result column="goods_dial_time" jdbcType="TIMESTAMP" property="goodsDialTime" />
    <result column="goods_dial_user" jdbcType="INTEGER" property="goodsDialUser" />

    <result column="car_active" jdbcType="VARCHAR" property="carActive" />

    <result column="goods_active" jdbcType="VARCHAR" property="goodsActive" />
  </resultMap>

  <resultMap id="ListResultMap" type="com.teyuntong.infra.task.service.biz.user.car.dto.UserArchivesDto" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <id column="userId" property="userId" jdbcType="BIGINT" />
    <result column="auth" property="auth" jdbcType="VARCHAR" />
    <result column="department" property="department" jdbcType="VARCHAR" />

  </resultMap>

  <resultMap id="ResultMap" type="com.teyuntong.infra.task.service.biz.user.car.dto.ArchiveCellentDto" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <id column="userId" property="userId" jdbcType="BIGINT" />
    <result column="maintainerId" property="maintainerId" jdbcType="BIGINT" />
    <result column="goodsMaintainerId" property="goodsMaintainerId" jdbcType="BIGINT" />

  </resultMap>

  <select id="getByUserIdLImit" resultMap="ListResultMap">
    select user.id,user.user_id as userId ,car.auth
    from tyt_user_archives user left join
    tyt_car car on user.user_id = car.user_id
    where user.id > #{maxId} and is_delete = 1  order by user.id asc limit #{limit}

  </select>

  <update id="updateUserArchives">
    update tyt_user_archives set user_car_auth = #{auth} where user_id = #{userId}
  </update>


  <select id="getByYeasterDayUser" resultMap="ListResultMap">
    select user.id as userId ,car.auth
    from tyt_user user left join
    tyt_car car on user.id = car.user_id
    where user.id > #{maxId}
        and ((user.car_last_login_time &gt;= #{startTime} and user.car_last_login_time &lt;= #{endTime} ) or
             (user.goods_last_login_time &gt;= #{startTime} and user.goods_last_login_time &lt;= #{endTime}))
    group by user.id
    order by user.id asc limit #{limit}

  </select>


  <select id="getByUserId" resultMap="ListResultMap">
    select user.id,user.user_id as userId ,car.info_verify_status as auth
    from tyt_user_archives user left join
    tyt_invoice_enterprise car on user.user_id = car.certigier_user_id
    where user.id > #{maxId}  and car.info_verify_status is not null order by user.id asc limit #{limit}

  </select>

  <update id="updateUserEnter">
    update tyt_user_archives set enter_auth = #{dto.auth} where user_id = #{dto.userId}
  </update>

  <select id="getByDateUserId" resultMap="ListResultMap">
    select user.id as userId ,car.info_verify_status as auth
    from tyt_user user left join
    tyt_invoice_enterprise car on user.id = car.certigier_user_id
    where user.id > #{maxId}
        and car.info_verify_status is not null
      and ((user.car_last_login_time &gt;= #{startTime} and user.car_last_login_time &lt;= #{endTime} ) or
        (user.goods_last_login_time &gt;= #{startTime} and user.goods_last_login_time &lt;= #{endTime}))
    group by user.id
    order by user.id asc limit #{limit}

  </select>

  <select id="getByUserIdOne" resultMap="BaseResultMap">
        select * from tyt_user_archives where user_id = #{userId} limit 1
  </select>

  <select id="getTransportByUserIdDate" resultType="java.lang.Integer">
    select count(*) from tyt_transport_main where user_id = #{userId} and  ctime &gt;= #{eightTimeStart} and ctime &lt;= #{eightTimeEnd} limit 1

  </select>

  <select id="getCallPhoneUserDate" resultType="java.lang.Integer">
    select count(*) from tyt_user_call_phone_record where user_id = #{userId} and  ctime &gt;= #{eightTimeStart} and ctime &lt;= #{eightTimeEnd} limit 1

  </select>


  <select id="getConfigUser"  resultMap="ResultMap">
        select user.id,user.user_id as userId,custom.id as maintainerId  from tyt_user_archives user
                 left join cs_maintained_custom custom on user.user_id = custom.custom_id
        where 1 = 1
        <if test="userMember != null and '' != userMember and type != null and '' != type">
          <if test="userMember == 1 and type == 1">
            and user.car_vip_label = '车会员'
          </if>
          <if test="userMember == 2 and type == 1">
            and user.car_vip_label != '车会员'
          </if>
          <if test="userMember == 1 and type == 2">
            and user.good_vip_label = '货会员'
          </if>
          <if test="userMember == 2 and type == 2">
            and user.good_vip_label != '货会员'
          </if>
        </if>

        <if test="type != null and '' != type">
            <if test="type == 1">
              and custom.maintainer_id is null
            </if>
            <if test="type == 2">
              and custom.goods_maintainer_id is null
            </if>
        </if>

        <if test="userIdentity != null and '' != userIdentity">
          and user.deliver_type_one in
          <foreach collection="userIdentity.split(',')" item="roleId" open="(" close=")" separator= ",">
            #{roleId}
          </foreach>
        </if>
        and user.id > #{maxId}  order by user.id asc limit #{limit}
  </select>


  <update id="updateMaintaine">
    update cs_maintained_custom set maintainer_id = #{id},maintainer_name = #{name},utime = now(),modify_name = '系统' where id = #{maintainerId} and maintainer_id is null
  </update>

  <update id="updateGoodsMaintaine">
    update cs_maintained_custom set goods_maintainer_id = #{id},goods_maintainer_name = #{name},utime = now(),modify_name = '系统' where id = #{maintainerId} and goods_maintainer_id is null
  </update>

  <select id="getOrdersNum" resultType="java.lang.Integer">
    select count(*) from tyt_transport_orders where pay_user_id = #{userId} and  ctime &gt;= #{eightTimeStart} and ctime &lt;= #{eightTimeEnd} and cost_status >= 15 limit 1

  </select>

  <select id="getArchivesByUserId" resultMap="BaseResultMap">
    select * from tyt_user_archives where user_id = #{userId} limit 1
  </select>

  <update id="updateUserArchivesInfo">
    UPDATE tyt.`tyt_user_archives` ua LEFT JOIN tyt.tyt_user u ON ua.user_id = u.id
    SET ua.`user_true_name` = u.true_name,ua.`user_cell_phone` = u.cell_phone,ua.`user_nickname`=u.user_name,
    ua.`last_login_time`=u.`last_time`,ua.`car_auth` =u.is_car,ua.`identity_auth` = u.verify_photo_sign,
    ua.client_sign = u.client_sign,ua.car_last_login_time = u.car_last_login_time,ua.goods_last_login_time =u.goods_last_login_time,
    ua.province = u.province,ua.city=u.city,ua.`identity` = (  CASE u.identity_type WHEN 1 THEN '个人货主' WHEN 2 THEN '企业货主' WHEN 3 THEN '货站' WHEN 4 THEN '工程车司机' WHEN 5 THEN '设备服务' WHEN 6 THEN '个人车主' WHEN 7 THEN '板车司机' WHEN 8 THEN '运输公司或车队' WHEN 9 THEN '板车服务' ELSE '未知身份' END ),
    ua.deliver_type_one = (CASE u.deliver_type_one WHEN 1 THEN '个人货主' WHEN 2 THEN '企业货主' WHEN 3 THEN '货站' WHEN 5 THEN '设备服务商' WHEN 6 THEN '个人车主' WHEN 7 THEN '司机' WHEN 8 THEN '运输公司' WHEN 9 THEN '板车服务商'WHEN 13 THEN '未确认' WHEN 14 THEN '物流公司' WHEN 18 THEN '非行业用户' ELSE '未知' END)
    where u.id in (
    <foreach collection="userIdList" item="userId" separator=",">
      #{userId}
    </foreach>
    )
  </update>

  <select id="getUserArchivesListByUserIdList" resultMap="BaseResultMap">
    select * from tyt_user_archives where user_id in (
    <foreach collection="userIds" item="userId" separator=",">
      #{userId}
    </foreach>
    )
  </select>

  <update id="updateArchivesDispatchCount">
    UPDATE `tyt_user_archives` SET `agent_publish_count` = `agent_publish_count`+#{agentPublishCount},utime=NOW() WHERE user_id=#{userId}
  </update>

  <update id="updateArchivesDuringCount">
    UPDATE `tyt_user_archives` SET `during_count` = `during_count`+#{duringCount},utime=NOW() WHERE user_id=#{userId}
  </update>

  <update id="updateUserArchivesGoodsPermission">
    UPDATE `tyt_user_archives`
    SET good_vip_label   =#{goodsVipLabel},
        goodvip_due_date =#{endTime}
    WHERE user_id = #{userId}
  </update>

  <update id="updateSignStatusByUserId">
    UPDATE `tyt_user_archives`
    SET invoice_sign_status   =#{signStatus}
    WHERE user_id = #{userId}
  </update>

</mapper>