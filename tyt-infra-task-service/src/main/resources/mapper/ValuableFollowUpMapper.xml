<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.goods.valuable.mapper.ValuableFollowUpMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.goods.valuable.entity.ValuableFollowUpDO">
        <id column="id" property="id" />
        <result column="type" property="type" />
        <result column="user_id" property="userId" />
        <result column="employee_id" property="employeeId" />
        <result column="employee_name" property="employeeName" />
        <result column="op_user_id" property="opUserId" />
        <result column="op_user_name" property="opUserName" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
        <result column="del_status" property="delStatus" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, type, user_id, employee_id, employee_name, op_user_id, op_user_name, create_time, modify_time, del_status
    </sql>

    <select id="selectMarketByUserId" resultMap="BaseResultMap">
        select *
        from tyt_valuable_follow_up
        where user_id = #{userId} and type = 2 and del_status = 0
        order by id desc limit 1
    </select>

    <select id="queryAllCsFollowup" resultMap="BaseResultMap">
        select *
        from tyt_valuable_follow_up
        where type = 1 and del_status = 0
    </select>

    <select id="getMarketValuableStatus" resultType="java.lang.Integer">
        select count(1)
        from tyt_cs_user_valuable_status where cs_business_user_id = #{employeeId} and status = 1
    </select>

    <insert id="insertValuableLog">
        insert into tyt_cs_user_valuable_assign_log (cs_business_user_id, src_msg_id, type, create_time) VALUE (#{csBusinessUserId}, #{srcMsgId}, 1, now())
    </insert>

</mapper>
