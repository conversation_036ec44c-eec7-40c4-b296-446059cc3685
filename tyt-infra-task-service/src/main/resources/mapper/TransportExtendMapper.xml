<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.goods.goodsinfo.mapper.TransportExtendMapper">

    <delete id="clearTransportExtend">
        delete
        from tyt_transport_extend
        where create_time &lt; DATE_SUB(CURDATE(), INTERVAL #{days} DAY)
    </delete>

    <select id="selectByTsIds" resultType="com.teyuntong.infra.task.service.biz.goods.goodsinfo.entity.TransportExtendDO">
        select *
        from tyt_transport_extend
        where ts_id in
        <foreach item="item" collection="tsIds" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
</mapper>
