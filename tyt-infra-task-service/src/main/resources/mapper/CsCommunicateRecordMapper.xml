<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.goods.custom.mapper.CsCommunicateRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.goods.custom.entity.CsCommunicateRecordDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="cell_phone" property="cellPhone" />
        <result column="record_source" property="recordSource" />
        <result column="user_source" property="userSource" />
        <result column="busi_type" property="busiType" />
        <result column="busi_type_name" property="busiTypeName" />
        <result column="busi_id" property="busiId" />
        <result column="busi_src_id" property="busiSrcId" />
        <result column="source_mt_id" property="sourceMtId" />
        <result column="ques_one_category" property="quesOneCategory" />
        <result column="ques_one_category_name" property="quesOneCategoryName" />
        <result column="ques_two_category" property="quesTwoCategory" />
        <result column="ques_two_category_name" property="quesTwoCategoryName" />
        <result column="ques_three_category" property="quesThreeCategory" />
        <result column="ques_three_category_name" property="quesThreeCategoryName" />
        <result column="ques_four_category" property="quesFourCategory" />
        <result column="ques_four_category_name" property="quesFourCategoryName" />
        <result column="ques_content" property="quesContent" />
        <result column="deal_result" property="dealResult" />
        <result column="remark" property="remark" />
        <result column="modify_reason" property="modifyReason" />
        <result column="record_state" property="recordState" />
        <result column="rvisit_time" property="rvisitTime" />
        <result column="ctime" property="ctime" />
        <result column="mtime" property="mtime" />
        <result column="op_group" property="opGroup" />
        <result column="op_user_id" property="opUserId" />
        <result column="op_user" property="opUser" />
        <result column="call_phone" property="callPhone" />
        <result column="intention_info" property="intentionInfo" />
        <result column="offer_type" property="offerType" />
        <result column="cash_offer" property="cashOffer" />
        <result column="oil_offer" property="oilOffer" />
        <result column="min_intention_price" property="minIntentionPrice" />
        <result column="max_intention_price" property="maxIntentionPrice" />
        <result column="dept_id" property="deptId" />
        <result column="dept_name" property="deptName" />
        <result column="dept_top_id" property="deptTopId" />
        <result column="dept_top_name" property="deptTopName" />
        <result column="intention_rank" property="intentionRank" />
        <result column="report" property="report" />
        <result column="number" property="number" />
        <result column="complaint_no" property="complaintNo" />
        <result column="communication_state" property="communicationState" />
        <result column="work_order_status" property="workOrderStatus" />
        <result column="record_part" property="recordPart" />
        <result column="client_side" property="clientSide" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, cell_phone, record_source, user_source, busi_type, busi_type_name, busi_id, busi_src_id, source_mt_id, ques_one_category, ques_one_category_name, ques_two_category, ques_two_category_name, ques_three_category, ques_three_category_name, ques_four_category, ques_four_category_name, ques_content, deal_result, remark, modify_reason, record_state, rvisit_time, ctime, mtime, op_group, op_user_id, op_user, call_phone, intention_info, offer_type, cash_offer, oil_offer, min_intention_price, max_intention_price, dept_id, dept_name, dept_top_id, dept_top_name, intention_rank, report, number, complaint_no, communication_state, work_order_status, record_part, client_side
    </sql>

    <select id="selectCommunicatePhoneList" resultType="java.lang.String">
        select tmp.cellPhone
        from (
        select cell_phone as cellPhone,
        max(ctime) as maxCtime
        from cs_communicate_record
        where cell_phone in
        <foreach collection="phoneList" item="phone" open="(" separator="," close=")">
            #{phone}
        </foreach>
        group by cell_phone
        ) tmp
        where tmp.maxCtime &lt; CONCAT(DATE_SUB(CURRENT_DATE, INTERVAL #{day} DAY),'00:00:00')
    </select>
</mapper>
