<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.goods.custom.mapper.DispatchCustomPublicReasonMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.goods.custom.entity.DispatchCustomPublicReasonDO">
        <id column="id" property="id" />
        <result column="custom_id" property="customId" />
        <result column="role_type" property="roleType" />
        <result column="reason" property="reason" />
        <result column="operator_name" property="operatorName" />
        <result column="operator_id" property="operatorId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, custom_id, role_type, reason, operator_name, operator_id, create_time, update_time
    </sql>

</mapper>
