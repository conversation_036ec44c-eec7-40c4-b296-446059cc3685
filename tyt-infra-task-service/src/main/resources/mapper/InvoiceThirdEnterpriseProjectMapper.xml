<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.enterprise.mybatis.InvoiceThirdEnterpriseProjectMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.enterprise.mybatis.entity.InvoiceThirdEnterpriseProjectDO">
        <id column="id" property="id" />
        <result column="third_enterprise_id" property="thirdEnterpriseId" />
        <result column="contract_no" property="contractNo" />
        <result column="project_id" property="projectId" />
        <result column="project_name" property="projectName" />
        <result column="project_simple_name" property="projectSimpleName" />
        <result column="create_amount_user" property="createAmountUser" />
        <result column="driver_carriage_user" property="driverCarriageUser" />
        <result column="certificate_type" property="certificateType" />
        <result column="goods_source" property="goodsSource" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, third_enterprise_id, contract_no, project_id, project_name, project_simple_name, create_amount_user, driver_carriage_user, certificate_type, goods_source, status, create_time, modify_time
    </sql>
    <select id="getByThirdEnterpriseId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tyt_invoice_third_enterprise_project
        where third_enterprise_id = #{thirdEnterpriseId} and status = #{status}
        limit 1
    </select>

</mapper>
