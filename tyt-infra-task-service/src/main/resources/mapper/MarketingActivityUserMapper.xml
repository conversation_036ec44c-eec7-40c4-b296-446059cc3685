<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.market.activity.mybatis.mapper.MarketingActivityUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.MarketingActivityUserDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="user_grade" property="userGrade" />
        <result column="user_cell_phone" property="userCellPhone" />
        <result column="activity_id" property="activityId" />
        <result column="is_delete" property="isDelete" />
        <result column="is_join" property="isJoin" />
        <result column="is_send_push" property="isSendPush" />
        <result column="operater" property="operater" />
        <result column="ctime" property="ctime" />
        <result column="mtime" property="mtime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, user_grade, user_cell_phone, activity_id, is_delete, is_join, is_send_push, operater, ctime, mtime
    </sql>

</mapper>
