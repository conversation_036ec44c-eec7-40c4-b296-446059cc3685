<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.goods.shipper.mapper.BmShipperStatsDayMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.goods.shipper.entity.BmShipperStatsDayDO">
        <id column="id" property="id" />
        <result column="arc_id" property="arcId" />
        <result column="plat_user_id" property="platUserId" />
        <result column="cell_phone" property="cellPhone" />
        <result column="mt_ship_no_today" property="mtShipNoToday" />
        <result column="mt_traded_no_today" property="mtTradedNoToday" />
        <result column="plat_ship_no_today" property="platShipNoToday" />
        <result column="plat_traded_no_today" property="platTradedNoToday" />
        <result column="stats_date" property="statsDate" />
        <result column="ctime" property="ctime" />
        <result column="mtime" property="mtime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, arc_id, plat_user_id, cell_phone, mt_ship_no_today, mt_traded_no_today, plat_ship_no_today, plat_traded_no_today, stats_date, ctime, mtime
    </sql>

    <insert id="insertShipperStatsDay">
        insert into bm_shipper_stats_day
            (arc_id, plat_user_id, cell_phone, mt_ship_no_today, mt_traded_no_today, plat_ship_no_today, plat_traded_no_today, stats_date)
        select id, plat_user_id, cell_phone, mt_ship_no_today, mt_traded_no_today, plat_ship_no_today, null, #{statsDate}
        from bm_shipper_archive
        WHERE mtime >= #{queryDate}
        AND (plat_ship_no_today != 0 OR mt_ship_no_today != 0 OR mt_traded_no_today != 0)
    </insert>

</mapper>
