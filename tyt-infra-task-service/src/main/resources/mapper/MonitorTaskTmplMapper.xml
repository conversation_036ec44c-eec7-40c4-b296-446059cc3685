<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.trade.monitor.mapper.MonitorTaskTmplMapper">

  <select id="getMonitorTaskTmplList" resultType="com.teyuntong.infra.task.service.biz.trade.monitor.entity.MonitorTaskTmpl">
      SELECT
          id,
          monitor_item monitorItem,
          content_type contentType,
          notify_type notifyType,
          tmpl_key tmplKey,
          tmpl_content tmplContent,
          tmpl_status tmplStatus,
          trigger_condition triggerCondition,
          backward_time backwardTime,
          execute_sql executeSql,
          ctime,
          remark,
          max_service_id maxServiceId,
          table_name tableName
      FROM
          monitor_task_tmpl
      where tmpl_status = 1
      order by id asc
  </select>


</mapper>
