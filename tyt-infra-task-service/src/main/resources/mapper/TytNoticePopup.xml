<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.coupon.mybatis.mapper.TytNoticePopupMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.coupon.mybatis.entity.TytNoticePopup">
        <id column="id" property="id" />
        <result column="type" property="type" />
        <result column="title" property="title" />
        <result column="msg_id" property="msgId" />
        <result column="content" property="content" />
        <result column="templ_id" property="templId" />
        <result column="production_id" property="productionId" />
        <result column="receive_id" property="receiveId" />
        <result column="receive_time" property="receiveTime" />
        <result column="receive_status" property="receiveStatus" />
        <result column="status" property="status" />
        <result column="ctime" property="ctime" />
        <result column="origin_popup" property="originPopup" />
        <result column="car_popup" property="carPopup" />
        <result column="goods_popup" property="goodsPopup" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, type, title, msg_id, content, templ_id, production_id, receive_id, receive_time, receive_status, status, ctime, origin_popup, car_popup, goods_popup
    </sql>

</mapper>
