<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.driver.mybatis.mapper.InvoiceDriverCredentialMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.driver.mybatis.entity.InvoiceDriverCredentialDO">
        <id column="id" property="id" />
        <result column="invoice_driver_id" property="invoiceDriverId" />
        <result column="pic_type" property="picType" />
        <result column="pic_url" property="picUrl" />
        <result column="verify_status" property="verifyStatus" />
        <result column="verify_desc" property="verifyDesc" />
        <result column="orc_json" property="orcJson" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, invoice_driver_id, pic_type, pic_url, verify_status, verify_desc, orc_json, create_time, update_time
    </sql>

    <select id="getBackUrl" resultMap="BaseResultMap">
        select * from tyt_invoice_driver_credential where invoice_driver_id=#{driverId} and pic_type=2 order by id desc limit 1
    </select>

</mapper>
