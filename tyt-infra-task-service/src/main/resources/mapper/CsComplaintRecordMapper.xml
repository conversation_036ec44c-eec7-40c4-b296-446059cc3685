<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.customerservice.mapper.CsComplaintRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.customerservice.entity.CsComplaintRecord">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="cell_phone" property="cellPhone" />
        <result column="complaint_phone" property="complaintPhone" />
        <result column="complaint_auth" property="complaintAuth" />
        <result column="record_source" property="recordSource" />
        <result column="busi_type" property="busiType" />
        <result column="busi_id" property="busiId" />
        <result column="ques_one_category" property="quesOneCategory" />
        <result column="ques_two_category" property="quesTwoCategory" />
        <result column="ques_three_category" property="quesThreeCategory" />
        <result column="ques_four_category" property="quesFourCategory" />
        <result column="ques_content" property="quesContent" />
        <result column="deal_result" property="dealResult" />
        <result column="remark" property="remark" />
        <result column="record_state" property="recordState" />
        <result column="ctime" property="ctime" />
        <result column="mtime" property="mtime" />
        <result column="op_group" property="opGroup" />
        <result column="op_user_id" property="opUserId" />
        <result column="op_user" property="opUser" />
        <result column="call_phone" property="callPhone" />
        <result column="pass_complaint_phone" property="passComplaintPhone" />
        <result column="create_user" property="createUser" />
        <result column="pass_complaint_auth" property="passComplaintAuth" />
        <result column="pass_complaint_num" property="passComplaintNum" />
        <result column="order_id" property="orderId" />
        <result column="complaint_type" property="complaintType" />
        <result column="complaint_source" property="complaintSource" />
        <result column="work_order_type" property="workOrderType" />
        <result column="return_order_no" property="returnOrderNo" />
        <result column="dispute_type" property="disputeType" />
        <result column="emptying_km" property="emptyingKm" />
        <result column="src_msg_id" property="srcMsgId" />
        <result column="entrucking_status" property="entruckingStatus" />
        <result column="finish_time" property="finishTime" />
        <result column="work_order_status" property="workOrderStatus" />
        <result column="last_comm_time" property="lastCommTime" />
        <result column="distributer" property="distributer" />
        <result column="division_time" property="divisionTime" />
        <result column="car_rvisit_status" property="carRvisitStatus" />
        <result column="goods_rvisit_status" property="goodsRvisitStatus" />
        <result column="communication_state" property="communicationState" />
        <result column="service_id" property="serviceId" />
        <result column="ques_one_category_name" property="quesOneCategoryName" />
        <result column="ques_two_category_name" property="quesTwoCategoryName" />
        <result column="ques_three_category_name" property="quesThreeCategoryName" />
        <result column="ques_four_category_name" property="quesFourCategoryName" />
        <result column="first_response_time" property="firstResponseTime" />
        <result column="goods_compensate" property="goodsCompensate" />
        <result column="goods_money" property="goodsMoney" />
        <result column="goods_station_money" property="goodsStationMoney" />
        <result column="goods_reason" property="goodsReason" />
        <result column="platform_compensate" property="platformCompensate" />
        <result column="platform_money" property="platformMoney" />
        <result column="platform_reason" property="platformReason" />
        <result column="car_satisfied" property="carSatisfied" />
        <result column="goods_satisfied" property="goodsSatisfied" />
        <result column="excellent_goods" property="excellentGoods" />
        <result column="ts_order_no" property="tsOrderNo" />
        <result column="source_type" property="sourceType" />
        <result column="upgrade_type" property="upgradeType" />
        <result column="complaints_type" property="complaintsType" />
        <result column="record_part" property="recordPart" />
        <result column="client_side" property="clientSide" />
        <result column="car_user_level" property="carUserLevel" />
        <result column="is_personal_service" property="isPersonalService" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, cell_phone, complaint_phone, complaint_auth, record_source, busi_type, busi_id, ques_one_category, ques_two_category, ques_three_category, ques_four_category, ques_content, deal_result, remark, record_state, ctime, mtime, op_group, op_user_id, op_user, call_phone, pass_complaint_phone, create_user, pass_complaint_auth, pass_complaint_num, order_id, complaint_type, complaint_source, work_order_type, return_order_no, dispute_type, emptying_km, src_msg_id, entrucking_status, finish_time, work_order_status, last_comm_time, distributer, division_time, car_rvisit_status, goods_rvisit_status, communication_state, service_id, ques_one_category_name, ques_two_category_name, ques_three_category_name, ques_four_category_name, first_response_time, goods_compensate, goods_money, goods_station_money, goods_reason, platform_compensate, platform_money, platform_reason, car_satisfied, goods_satisfied, excellent_goods, ts_order_no, source_type, upgrade_type, complaints_type, record_part, client_side, car_user_level, is_personal_service
    </sql>

    <select id="getNewWorkOrderIds" resultType="java.lang.Long">
        SELECT id FROM `cs_complaint_record` WHERE op_user_id IS NULL AND work_order_status = 1
    </select>

    <select id="getCountByOpUser" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM `cs_complaint_record` WHERE op_user_id=#{userId} AND work_order_status &lt; 7
    </select>

    <update id="updateOpUser">
        UPDATE `cs_complaint_record` SET `op_user_id`=#{opUserId},`op_user`=#{opUserName},`distributer`='系统',`division_time`=NOW(),`mtime`=NOW() WHERE id=#{workOrderId}
    </update>

</mapper>
