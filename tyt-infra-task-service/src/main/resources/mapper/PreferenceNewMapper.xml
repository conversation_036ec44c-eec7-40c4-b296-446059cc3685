<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.info.mybatis.mapper.PreferenceNewMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.info.mybatis.entity.PreferenceNewDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="car_id" property="carId" />
        <result column="start_provinc" property="startProvinc" />
        <result column="start_city" property="startCity" />
        <result column="start_area" property="startArea" />
        <result column="start_coord_x" property="startCoordX" />
        <result column="start_coord_y" property="startCoordY" />
        <result column="dest_provinc" property="destProvinc" />
        <result column="dest_city" property="destCity" />
        <result column="dest_area" property="destArea" />
        <result column="dest_coord_x" property="destCoordX" />
        <result column="dest_coord_y" property="destCoordY" />
        <result column="begin_weight" property="beginWeight" />
        <result column="end_weight" property="endWeight" />
        <result column="length" property="length" />
        <result column="wide" property="wide" />
        <result column="high" property="high" />
        <result column="ctime" property="ctime" />
        <result column="is_auth_update" property="isAuthUpdate" />
        <result column="status" property="status" />
        <result column="utime" property="utime" />
        <result column="find_good_onoff" property="findGoodOnoff" />
        <result column="preference_car" property="preferenceCar" />
        <result column="start_distance" property="startDistance" />
        <result column="update_onoff_time" property="updateOnoffTime" />
        <result column="current_speed" property="currentSpeed" />
        <result column="current_detail_addr" property="currentDetailAddr" />
        <result column="current_position" property="currentPosition" />
        <result column="current_status" property="currentStatus" />
        <result column="start_point_longitude" property="startPointLongitude" />
        <result column="start_point_latitude" property="startPointLatitude" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, car_id, start_provinc, start_city, start_area, start_coord_x, start_coord_y, dest_provinc, dest_city, dest_area, dest_coord_x, dest_coord_y, begin_weight, end_weight, length, wide, high, ctime, is_auth_update, status, utime, find_good_onoff, preference_car, start_distance, update_onoff_time, current_speed, current_detail_addr, current_position, current_status, start_point_longitude, start_point_latitude
    </sql>
    <update id="updateFindGoodOnOff">
        UPDATE tyt_recommend.tyt_preference_new c
        SET find_good_onoff = 0, update_onoff_time = NOW()
        WHERE EXISTS (SELECT id
                      FROM tyt_user  u
                      WHERE u.id=c.user_id
                        AND end_time &lt;= CONCAT(DATE_FORMAT(NOW(),'%Y-%m-%d'),' 00:00:00'))
          AND find_good_onoff = 1
    </update>

</mapper>
