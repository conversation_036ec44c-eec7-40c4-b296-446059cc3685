<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.car.mybatis.mapper.FeedbackUserPcDispatchMapper">
  <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.car.mybatis.entity.FeedbackUserPcDispatch">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="post_user_id" jdbcType="BIGINT" property="postUserId" />
    <result column="post_user_phone" jdbcType="VARCHAR" property="postUserPhone" />
    <result column="receive_user_id" jdbcType="BIGINT" property="receiveUserId" />
    <result column="receive_user_phone" jdbcType="VARCHAR" property="receiveUserPhone" />
    <result column="del_flag" jdbcType="INTEGER" property="delFlag" />
    <result column="ts_order_id" jdbcType="BIGINT" property="tsOrderId" />
    <result column="ts_id" jdbcType="BIGINT" property="tsId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="feedback_type" jdbcType="INTEGER" property="feedbackType" />
    <result column="comment" jdbcType="VARCHAR" property="comment" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="content_id" jdbcType="VARCHAR" property="contentId" />
  </resultMap>


  <select id="getGoodFeed" resultType="java.lang.Integer">
    select count(*) from feedback_user_pc_dispatch where receive_user_id = #{userId}
                                         and feedback_type = 1  and create_time &gt;= #{date} and create_time &lt;= #{endDate}
  </select>

  <select id="getAllFeed" resultType="java.lang.Integer">
    select count(*) from feedback_user_pc_dispatch where receive_user_id = #{userId}
                                          and create_time &gt;= #{date} and create_time &lt;= #{endDate}
  </select>
</mapper>