<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.goods.refresh.mapper.GoodsRefreshManualMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.goods.refresh.entity.GoodsRefreshManualDO">
        <id column="id" property="id"/>
        <result column="src_msg_id" property="srcMsgId"/>
        <result column="user_id" property="userId"/>
        <result column="refresh_interval" property="refreshInterval"/>
        <result column="total_times" property="totalTimes"/>
        <result column="refresh_times" property="refreshTimes"/>
        <result column="left_times" property="leftTimes"/>
        <result column="item_value" property="itemValue"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
    </resultMap>

    <!-- 查询今天还有刷新次数的货源 -->
    <select id="getTodayUnRefreshRecord" resultMap="BaseResultMap">
        select id, src_msg_id, refresh_interval, total_times, refresh_times
        from tyt_goods_refresh_manual
        where create_time between CURDATE() and NOW()
        and left_times > 0
    </select>

    <select id="getBySrcMsgId" resultMap="BaseResultMap">
        select * from tyt_goods_refresh_manual
        where src_msg_id = #{srcMsgId}
    </select>

</mapper>
