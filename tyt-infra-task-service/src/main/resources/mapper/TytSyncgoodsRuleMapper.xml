<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.ymm.mybatis.TytSyncgoodsRuleMapper">
    <select id="selectByIsComplete" resultType="com.teyuntong.infra.task.service.biz.ymm.entity.TytSyncgoodsRule">
        select *
        from tyt_syncgoods_rule
        where rule_type = #{isComplete,jdbcType=INTEGER}
        and is_delete = 0
        and status = 1
        ORDER BY id DESC
    </select>

    <select id="selectEffectiveRule" resultType="com.teyuntong.infra.task.service.biz.ymm.entity.TytSyncgoodsRule">
        select *
        from tyt_syncgoods_rule
        where status = 1 and is_delete = 0
    </select>

    <select id="selectEnableRuleGroupById" resultType="integer">
        select count(1) from tyt_syncgoods_rule_group where id = #{ruleGroupId} and status = 1 and is_delete = 0
    </select>

</mapper>
