<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.car.mybatis.mapper.CollectInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.car.mybatis.entity.CollectInfoDO">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="ms_id" property="msId"/>
        <result column="type" property="type"/>
        <result column="status" property="status"/>
        <result column="ctime" property="ctime"/>
        <result column="utime" property="utime"/>
    </resultMap>
    <select id="getCollectInfoNbr"
            resultType="com.teyuntong.infra.task.service.biz.user.car.dto.TytCollectInfoDto">
        SELECT MAX(utime) utime, ms_id msId, type type, SUM(ac) ac, SUM(dc) dc
        FROM (SELECT utime,
                     ms_id,
                     TYPE,
                     CASE WHEN STATUS = 0 THEN 1 ELSE 0 END ac,
                     CASE WHEN STATUS = 1 THEN 1 ELSE 0 END dc
              FROM tyt_collect_info
              WHERE utime > #{date}
                and type != 1 AND TYPE IN (2,3,4,5)) d
        GROUP BY TYPE, ms_id
        ORDER BY utime desc
    </select>


</mapper>
