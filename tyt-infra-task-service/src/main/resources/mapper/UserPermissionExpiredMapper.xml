<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.permission.mybatis.mapper.UserPermissionExpiredMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.permission.mybatis.entity.UserPermissionExpiredDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="service_permission_type_id" property="servicePermissionTypeId" />
        <result column="service_permission_type_name" property="servicePermissionTypeName" />
        <result column="expired_before_num" property="expiredBeforeNum" />
        <result column="expired_num" property="expiredNum" />
        <result column="expired_after_num" property="expiredAfterNum" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, service_permission_type_id, service_permission_type_name, expired_before_num, expired_num, expired_after_num, create_time
    </sql>

</mapper>
