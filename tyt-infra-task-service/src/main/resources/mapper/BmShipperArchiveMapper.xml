<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.goods.shipper.mapper.BmShipperArchiveMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.goods.shipper.entity.BmShipperArchiveDO">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="cell_phone" property="cellPhone" />
        <result column="plat_user_id" property="platUserId" />
        <result column="sex" property="sex" />
        <result column="id_card" property="idCard" />
        <result column="sp_wx_no" property="spWxNo" />
        <result column="mtn_wx_no" property="mtnWxNo" />
        <result column="status" property="status" />
        <result column="work_province" property="workProvince" />
        <result column="work_ctiy" property="workCtiy" />
        <result column="work_area" property="workArea" />
        <result column="work_point" property="workPoint" />
        <result column="work_detail" property="workDetail" />
        <result column="home_province" property="homeProvince" />
        <result column="home_city" property="homeCity" />
        <result column="home_area" property="homeArea" />
        <result column="home_point" property="homePoint" />
        <result column="home_detail" property="homeDetail" />
        <result column="business_type" property="businessType" />
        <result column="business_type_oth" property="businessTypeOth" />
        <result column="deliver_type_one" property="deliverTypeOne" />
        <result column="deliver_type_two" property="deliverTypeTwo" />
        <result column="join_yearn" property="joinYearn" />
        <result column="duty" property="duty" />
        <result column="duty_oth" property="dutyOth" />
        <result column="customer_src" property="customerSrc" />
        <result column="customer_src_oth" property="customerSrcOth" />
        <result column="teu_start" property="teuStart" />
        <result column="teu_end" property="teuEnd" />
        <result column="mtn_name" property="mtnName" />
        <result column="mtn_phone" property="mtnPhone" />
        <result column="mtn_id" property="mtnId" />
        <result column="dev_principal" property="devPrincipal" />
        <result column="dev_principal_id" property="devPrincipalId" />
        <result column="call_result" property="callResult" />
        <result column="call_fail_reason" property="callFailReason" />
        <result column="talk_count" property="talkCount" />
        <result column="first_talk_time" property="firstTalkTime" />
        <result column="last_talk_time" property="lastTalkTime" />
        <result column="last_mt_time" property="lastMtTime" />
        <result column="mt_ship_no_today" property="mtShipNoToday" />
        <result column="mt_ship_no_seven" property="mtShipNoSeven" />
        <result column="mt_ship_no_thirty" property="mtShipNoThirty" />
        <result column="mt_ship_no_all" property="mtShipNoAll" />
        <result column="mt_ship_no_lastweek" property="mtShipNoLastweek" />
        <result column="mt_traded_no_today" property="mtTradedNoToday" />
        <result column="mt_traded_no_seven" property="mtTradedNoSeven" />
        <result column="mt_traded_no_thirty" property="mtTradedNoThirty" />
        <result column="mt_traded_no_all" property="mtTradedNoAll" />
        <result column="mt_traded_no_lastweek" property="mtTradedNoLastweek" />
        <result column="mt_ship_no_lastmonth" property="mtShipNoLastmonth" />
        <result column="mt_ship_no_last2month" property="mtShipNoLast2month" />
        <result column="mt_ship_reduction_rate" property="mtShipReductionRate" />
        <result column="plat_ship_no_today" property="platShipNoToday" />
        <result column="plat_ship_no_seven" property="platShipNoSeven" />
        <result column="plat_ship_no_thirty" property="platShipNoThirty" />
        <result column="plat_ship_no_all" property="platShipNoAll" />
        <result column="plat_ship_no_lastweek" property="platShipNoLastweek" />
        <result column="plat_traded_rate" property="platTradedRate" />
        <result column="plat_ship_month_top" property="platShipMonthTop" />
        <result column="plat_ship_month" property="platShipMonth" />
        <result column="department" property="department" />
        <result column="company" property="company" />
        <result column="operator_id" property="operatorId" />
        <result column="operator_name" property="operatorName" />
        <result column="ctime" property="ctime" />
        <result column="mtime" property="mtime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, cell_phone, plat_user_id, sex, id_card, sp_wx_no, mtn_wx_no, status, work_province, work_ctiy, work_area, work_point, work_detail, home_province, home_city, home_area, home_point, home_detail, business_type, business_type_oth, deliver_type_one, deliver_type_two, join_yearn, duty, duty_oth, customer_src, customer_src_oth, teu_start, teu_end, mtn_name, mtn_phone, mtn_id, dev_principal, dev_principal_id, call_result, call_fail_reason, talk_count, first_talk_time, last_talk_time, last_mt_time, mt_ship_no_today, mt_ship_no_seven, mt_ship_no_thirty, mt_ship_no_all, mt_ship_no_lastweek, mt_traded_no_today, mt_traded_no_seven, mt_traded_no_thirty, mt_traded_no_all, mt_traded_no_lastweek, mt_ship_no_lastmonth, mt_ship_no_last2month, mt_ship_reduction_rate, plat_ship_no_today, plat_ship_no_seven, plat_ship_no_thirty, plat_ship_no_all, plat_ship_no_lastweek, plat_traded_rate, plat_ship_month_top, plat_ship_month, department, company, operator_id, operator_name, ctime, mtime
    </sql>

    <update id="updateShipperArchive">
        update bm_shipper_archive
        set plat_ship_no_today = 0, mt_ship_no_today = 0,  mt_traded_no_today = 0
        WHERE mtime >= #{queryDate} AND (plat_ship_no_today != 0 OR mt_ship_no_today != 0 OR mt_traded_no_today != 0)
    </update>


    <update id="updateArchiveResetLastWeek" parameterType="java.util.Date">
        UPDATE bm_shipper_archive
        SET mt_ship_no_lastweek = 0,
            mt_traded_no_lastweek = 0,
            plat_ship_no_lastweek = 0
        WHERE mtime >= #{date}
          AND (mt_ship_no_lastweek != 0
               OR mt_traded_no_lastweek != 0
               OR plat_ship_no_lastweek != 0)
    </update>


    <update id="updateArchiveWithStats">
        UPDATE bm_shipper_archive AS arc
        RIGHT JOIN (
        SELECT
        arc_id,
        SUM(mt_ship_no_today) AS mt_ship_no_lastweek,
        SUM(mt_traded_no_today) AS mt_traded_no_lastweek,
        SUM(plat_ship_no_today) AS plat_ship_no_lastweek
        FROM
        `bm_shipper_stats_day`
        WHERE
        stats_date >= #{startDate}
        AND stats_date <![CDATA[<=]]> #{endDate}
        GROUP BY
        arc_id
        ) AS DAY ON arc.id = DAY.arc_id
        SET
        arc.mt_ship_no_lastweek = DAY.mt_ship_no_lastweek,
        arc.mt_traded_no_lastweek = DAY.mt_traded_no_lastweek,
        arc.plat_ship_no_lastweek = DAY.plat_ship_no_lastweek
    </update>


    <update id="updateShipperArchiveLastMonth">
        UPDATE bm_shipper_archive AS arc
        RIGHT JOIN (
        SELECT
        arc_id,
        SUM(CASE WHEN stats_date >= #{l2StartDate} AND stats_date <![CDATA[<=]]> #{l2EndDate} THEN mt_ship_no_today ELSE 0 END) AS mt_ship_no_last2month,
        SUM(CASE WHEN stats_date >= #{lStartDate} AND stats_date <![CDATA[<=]]> #{lEndDate} THEN mt_ship_no_today ELSE 0 END) AS mt_ship_no_lastmonth
        FROM
        `bm_shipper_stats_day`
        WHERE
        stats_date >= #{l2StartDate}
        AND stats_date <![CDATA[<=]]> #{lEndDate}
        GROUP BY
        arc_id
        ) AS st ON arc.id = st.arc_id
        SET
        arc.mt_ship_no_lastmonth = st.mt_ship_no_lastmonth,
        arc.mt_ship_no_last2month = st.mt_ship_no_last2month,
        arc.mt_ship_reduction_rate = IFNULL(CONVERT((st.mt_ship_no_lastmonth - arc.mt_ship_no_last2month) / arc.mt_ship_no_last2month, DECIMAL(10, 4)) * 100, 100.0000)
    </update>

</mapper>
