<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.goods.dispatch.mapper.SpecialCarContactRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.goods.dispatch.entity.SpecialCarContactRecordDO">
        <id column="id" property="id" />
        <result column="src_msg_id" property="srcMsgId" />
        <result column="user_id" property="userId" />
        <result column="user_name" property="userName" />
        <result column="type" property="type" />
        <result column="contact_status" property="contactStatus" />
        <result column="work_order_status" property="workOrderStatus" />
        <result column="goods_status" property="goodsStatus" />
        <result column="is_change" property="isChange" />
        <result column="contact_role" property="contactRole" />
        <result column="contact_phone" property="contactPhone" />
        <result column="contact_content" property="contactContent" />
        <result column="fail_reason" property="failReason" />
        <result column="change_reason" property="changeReason" />
        <result column="deal_reason" property="dealReason" />
        <result column="not_deal_reason" property="notDealReason" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, src_msg_id, user_id, user_name, type, contact_status, work_order_status, goods_status, is_change, contact_role, contact_phone, contact_content, fail_reason, change_reason, deal_reason, not_deal_reason, remark, create_time
    </sql>

    <select id="countBySrcMsgId" resultType="java.lang.Integer">
        select count(*)
        from tyt_special_car_contact_record
        where src_msg_id = #{srcMsgId}
    </select>

</mapper>
