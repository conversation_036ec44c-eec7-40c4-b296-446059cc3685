<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.info.mybatis.mapper.CsAutomaticAssignMapper">
  <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.info.mybatis.entity.CsAutomaticAssign">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="user_member" jdbcType="INTEGER" property="userMember" />
    <result column="user_identity" jdbcType="VARCHAR" property="userIdentity" />
    <result column="department" jdbcType="VARCHAR" property="department" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="modify_name" jdbcType="VARCHAR" property="modifyName" />
  </resultMap>
  <resultMap id="ListResultMap" type="com.teyuntong.infra.task.service.biz.user.car.dto.UserArchivesDto" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <id column="userId" property="userId" jdbcType="BIGINT" />
    <result column="auth" property="auth" jdbcType="VARCHAR" />
    <result column="department" property="department" jdbcType="VARCHAR" />

  </resultMap>

  <select id="getAllConfig" resultMap="BaseResultMap">
        select id,type,user_member,user_identity,department from cs_automatic_assign where status = 1
  </select>
  <select id="getUserBing" resultMap="ListResultMap">
    select id,real_name as auth,cs_department_id as department  from cs_business_user_bind
    where  is_valid = 1
    and role_id in
    <foreach collection="roleIds.split(',')" item="roleId" open="(" close=")" separator= ",">
        #{roleId}
    </foreach>
    order by id asc
  </select>

</mapper>