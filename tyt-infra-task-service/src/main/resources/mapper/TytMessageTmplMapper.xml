<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.common.tmpl.mapper.TytMessageTmplMapper">

    <select id="getContent" resultType="com.teyuntong.infra.task.service.common.tmpl.entity.MessageTmplDO">
     select content  from tyt_message_tmpl where type=0 and tmpl_key=#{tmplKey} and status=0
     </select>

    <select id="getPushContent" resultType="com.teyuntong.infra.task.service.common.tmpl.entity.MessageTmplDO">
        select content
        from tyt_message_tmpl
        where type = 1 and tmpl_key = #{tmplKey} and status = 0
        order by id desc limit 1
    </select>
</mapper>
