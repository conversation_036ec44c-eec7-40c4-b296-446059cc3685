<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.permission.mybatis.mapper.CreditDeductLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.permission.mybatis.entity.CreditDeductLogDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="user_type" property="userType" />
        <result column="deduct_type" property="deductType" />
        <result column="deduct_score" property="deductScore" />
        <result column="is_push" property="isPush" />
        <result column="ctime" property="ctime" />
        <result column="mtime" property="mtime" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, user_type, deduct_type, deduct_score, is_push, ctime, mtime, remark
    </sql>


    <select id="getListByTime" resultMap="BaseResultMap">
        SELECT t.user_id , t.user_type
        FROM tyt_credit_deduct_log t
        WHERE t.ctime > #{beginTime} AND t.is_push = 0
        GROUP BY user_id, user_type
            LIMIT #{offset}, 1000
    </select>


    <update id="updateStatus">
        UPDATE tyt_credit_deduct_log
        SET is_push = 1
        WHERE ctime > #{beginTime} AND is_push = 0
    </update>

</mapper>
