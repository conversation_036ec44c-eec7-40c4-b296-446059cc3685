<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.market.activity.mybatis.mapper.ConventionOrdersCensusMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.ConventionOrdersCensusDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="activity_id" property="activityId" />
        <result column="round_times" property="roundTimes" />
        <result column="orders_num" property="ordersNum" />
        <result column="final_rank" property="finalRank" />
        <result column="qualify_goods_id" property="qualifyGoodsId" />
        <result column="qualify_goods_name" property="qualifyGoodsName" />
        <result column="rank_goods_id" property="rankGoodsId" />
        <result column="rank_goods_name" property="rankGoodsName" />
        <result column="status" property="status" />
        <result column="ctime" property="ctime" />
        <result column="mtime" property="mtime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, activity_id, round_times, orders_num, final_rank, qualify_goods_id, qualify_goods_name, rank_goods_id, rank_goods_name, status, ctime, mtime
    </sql>

    <select id="getByUserIdActivityId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tyt_convention_orders_census
        where activity_id = #{activityId}
          and round_times = #{roundTimes}
        and id > #{id}
        order by id limit 500
    </select>
    <select id="getByActivityId"  resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tyt_convention_orders_census
        where activity_id = #{activityId}
        and round_times = #{roundTimes}
        and user_id = #{userId}
        limit 1
    </select>

    <select id="getRank4ConventionOrdersCensus" resultType="com.teyuntong.infra.task.service.biz.market.activity.dto.ConventionRank">
        SELECT a.user_id userId,
               a.round_times roundTimes,
               a.orders_num ordersNum,
               @rank:=@rank+1 as rank
        FROM (SELECT c.user_id,c.round_times,c.orders_num FROM tyt_convention_orders_census c
            WHERE c.round_times = #{round} AND c.`status` = 1 AND c.activity_id=#{activityId} ORDER BY c.orders_num DESC,c.last_order_time ASC) a,(SELECT @RANK:=0) t;
    </select>

    <update id="updateStatus">
        UPDATE tyt_convention_orders_census
        SET `final_rank`=#{rank},
            `qualify_goods_id`=#{qualifyGoodsId},
            `qualify_goods_name`=#{qualifyGoodsName},
            `rank_goods_id`=#{rankGoodsId},
            `rank_goods_name`=#{rankGoodsName},
            `status`=2
        WHERE user_id=#{userId}
          AND `round_times`=#{round}
          AND activity_id=#{activityId}
    </update>

</mapper>
