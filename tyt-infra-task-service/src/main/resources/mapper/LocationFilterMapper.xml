<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.config.mybatis.mapper.LocationFilterMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.config.mybatis.entity.LocationFilterDO">
        <id column="id" property="id" />
        <result column="filter_user_id" property="filterUserId" />
        <result column="remark" property="remark" />
        <result column="ctime" property="ctime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, filter_user_id, remark, ctime
    </sql>

</mapper>
