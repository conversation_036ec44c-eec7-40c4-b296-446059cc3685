<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.info.mybatis.mapper.UserSecurityRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.info.mybatis.entity.UserSecurityRecordDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="source_type" property="sourceType" />
        <result column="source_id" property="sourceId" />
        <result column="source_desc" property="sourceDesc" />
        <result column="money_amount" property="moneyAmount" />
        <result column="manager_user_id" property="managerUserId" />
        <result column="expire_time" property="expireTime" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, source_type, source_id, source_desc, money_amount, manager_user_id, expire_time, create_time, modify_time
    </sql>

    <select id="getExpireUserList" resultType="java.lang.Long">
        select distinct(user_id) dis_user_id from tyt_user_security_record
        where expire_time &lt;= #{endDate}
          and expire_time > #{startDate}
          and user_id > #{userId}
        order by user_id
            limit #{pageSize}
    </select>

    <select id="getTotalValidMoney" resultType="java.math.BigDecimal">
        select sum(money_amount) from tyt_user_security_record
        where user_id = #{userId}
          and (
                    expire_time >= #{todayBegin}
                or expire_time is null
            )
    </select>

</mapper>
