<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.upgrade.mybatis.mapper.PcUpgradeTaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.teyuntong.infra.task.service.biz.user.upgrade.mybatis.entity.PcUpgradeTaskDO">
        <id column="id" property="id"/>
        <result column="task_name" property="taskName"/>
        <result column="upgrade_model" property="upgradeModel"/>
        <result column="upgrade_type" property="upgradeType"/>
        <result column="sdk_depend_version" property="sdkDependVersion"/>
        <result column="is_specific_upgrade" property="isSpecificUpgrade"/>
        <result column="upgrade_version" property="upgradeVersion"/>
        <result column="upgrade_start_time" property="upgradeStartTime"/>
        <result column="upgrade_end_time" property="upgradeEndTime"/>
        <result column="upgrage_url" property="upgrageUrl"/>
        <result column="msg" property="msg"/>
        <result column="task_status" property="taskStatus"/>
        <result column="ctime" property="ctime"/>
        <result column="utime" property="utime"/>
        <result column="is_delete" property="isDelete"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , task_name, upgrade_model, upgrade_type, sdk_depend_version, is_specific_upgrade, upgrade_version, upgrade_start_time, upgrade_end_time, upgrage_url, msg, task_status, ctime, utime, is_delete
    </sql>
    <update id="batchUpdateTaskStatus">
        update tyt_pc_upgrade_task set task_status = #{taskStatus} where id in
        <foreach open="(" collection="ids" index="index" item="id" close=")" separator=",">
            #{id}
        </foreach>
    </update>
    <select id="selectValidUpgradeTaskId" resultType="java.lang.Integer">
        select id
        from tyt_pc_upgrade_task
        where upgrade_start_time &lt;= NOW()
          AND upgrade_end_time > NOW()
          AND task_status = 0
          and is_delete = 0
    </select>
    <select id="selectInvalidUpgradeTaskId" resultType="java.lang.Integer">
        select id
        from tyt_pc_upgrade_task
        where upgrade_end_time &lt;= NOW()
          AND task_status IN (0, 1)
          and is_delete = 0
    </select>

</mapper>
