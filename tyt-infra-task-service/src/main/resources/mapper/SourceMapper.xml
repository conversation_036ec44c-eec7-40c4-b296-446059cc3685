<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.config.mybatis.mapper.SourceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.config.mybatis.entity.SourceDO">
        <id column="id" property="id" />
        <result column="group_code" property="groupCode" />
        <result column="group_name" property="groupName" />
        <result column="value" property="value" />
        <result column="name" property="name" />
        <result column="short_name" property="shortName" />
        <result column="remark" property="remark" />
        <result column="sort" property="sort" />
        <result column="parent" property="parent" />
        <result column="status" property="status" />
        <result column="dict_status" property="dictStatus" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, group_code, group_name, value, name, short_name, remark, sort, parent, status, dict_status
    </sql>

    <select id="listSourceDO" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tyt_source where group_code=#{groupCode}
    </select>

</mapper>
