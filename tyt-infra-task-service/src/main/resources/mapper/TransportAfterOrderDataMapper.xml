<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.goods.goodsinfo.mapper.TransportAfterOrderDataMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.teyuntong.infra.task.service.biz.goods.goodsinfo.entity.TransportAfterOrderDataDO">
        <id column="id" property="id"/>
        <result column="src_msg_id" property="srcMsgId"/>
        <result column="user_id" property="userId"/>
        <result column="task_content" property="taskContent"/>
        <result column="good_type_name" property="goodTypeName"/>
        <result column="start_city" property="startCity"/>
        <result column="dest_city" property="destCity"/>
        <result column="distance" property="distance"/>
        <result column="weight" property="weight"/>
        <result column="price" property="price"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, src_msg_id, user_id, task_content, good_type_name, start_city, dest_city, distance, weight, price,
        create_time, modify_time
    </sql>

</mapper>
