<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.trade.infofee.mapper.ThirdOrdersNodePicMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.trade.infofee.entity.ThirdOrdersNodePicDO">
        <id column="id" property="id" />
        <result column="order_id" property="orderId" />
        <result column="load_pic_url" property="loadPicUrl" />
        <result column="un_load_pic_url" property="unLoadPicUrl" />
        <result column="receipt_pic_url" property="receiptPicUrl" />
        <result column="out_limit_certificate_pic_url" property="outLimitCertificatePicUrl" />
        <result column="goods_insurance_pic_url" property="goodsInsurancePicUrl" />
        <result column="risk_appeal_pic_url" property="riskAppealPicUrl" />
        <result column="invoice_service_code" property="invoiceServiceCode" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_id, load_pic_url, un_load_pic_url, receipt_pic_url, out_limit_certificate_pic_url, goods_insurance_pic_url, risk_appeal_pic_url, invoice_service_code, create_time, modify_time
    </sql>
    <select id="getThirdNodePicByOrderId"
            resultType="com.teyuntong.infra.task.service.biz.trade.infofee.entity.ThirdOrdersNodePicDO">
        select <include refid="Base_Column_List"/>
        from tyt_third_orders_node_pic where order_id = #{orderId}
    </select>

</mapper>
