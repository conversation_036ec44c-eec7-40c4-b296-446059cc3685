<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.goods.covergoods.mapper.CoverGoodsBeansConfigUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.teyuntong.infra.task.service.biz.goods.covergoods.entity.CoverGoodsBeansConfigUserDO">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="total_num" property="totalNum"/>
        <result column="used_num" property="usedNum"/>
        <result column="left_num" property="leftNum"/>
        <result column="beans_name" property="beansName"/>
        <result column="validate_time" property="validateTime"/>
        <result column="operate_user_id" property="operateUserId"/>
        <result column="operate_user_name" property="operateUserName"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="beans_status" property="beansStatus"/>
        <result column="dial_config_id" property="dialConfigId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , user_id, total_num, used_num, left_num, beans_name, validate_time, operate_user_id, operate_user_name, create_time, update_time, beans_status, dial_config_id
    </sql>
    <update id="updateCoverGoodsStatus">
        update tyt_cover_goods_beans_config_user
        set beans_status = 3
        where beans_status = 1
          and validate_time &lt;= now();
    </update>

</mapper>
