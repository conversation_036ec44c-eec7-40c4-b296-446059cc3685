<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.info.mybatis.mapper.CsUpdateMaintainerLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.info.mybatis.entity.CsUpdateMaintainerLog">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="origin_maintainer_id" property="originMaintainerId" />
        <result column="origin_maintainer_name" property="originMaintainerName" />
        <result column="now_maintainer_id" property="nowMaintainerId" />
        <result column="now_maintainer_name" property="nowMaintainerName" />
        <result column="updater_id" property="updaterId" />
        <result column="updater_name" property="updaterName" />
        <result column="belong_to" property="belongTo" />
        <result column="maintain_start_time" property="maintainStartTime" />
        <result column="maintain_end_time" property="maintainEndTime" />
        <result column="car_vip_label" property="carVipLabel" />
        <result column="carvip_last_pay_date" property="carvipLastPayDate" />
        <result column="carvip_pay_times" property="carvipPayTimes" />
        <result column="good_vip_label" property="goodVipLabel" />
        <result column="goodvip_last_pay_date" property="goodvipLastPayDate" />
        <result column="goodvip_pay_times" property="goodvipPayTimes" />
        <result column="deliver_goods_label" property="deliverGoodsLabel" />
        <result column="deliver_goods_last_pay_date" property="deliverGoodsLastPayDate" />
        <result column="deliver_goods_pay_times" property="deliverGoodsPayTimes" />
        <result column="ctime" property="ctime" />
        <result column="origin_goods_maintainer_id" property="originGoodsMaintainerId" />
        <result column="origin_goods_maintainer_name" property="originGoodsMaintainerName" />
        <result column="now_goods_maintainer_id" property="nowGoodsMaintainerId" />
        <result column="now_goods_maintainer_name" property="nowGoodsMaintainerName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, origin_maintainer_id, origin_maintainer_name, now_maintainer_id, now_maintainer_name, updater_id, updater_name, belong_to, maintain_start_time, maintain_end_time, car_vip_label, carvip_last_pay_date, carvip_pay_times, good_vip_label, goodvip_last_pay_date, goodvip_pay_times, deliver_goods_label, deliver_goods_last_pay_date, deliver_goods_pay_times, ctime, origin_goods_maintainer_id, origin_goods_maintainer_name, now_goods_maintainer_id, now_goods_maintainer_name
    </sql>

</mapper>
