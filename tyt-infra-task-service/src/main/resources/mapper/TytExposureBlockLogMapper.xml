<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.limit.mybatis.mapper.TytExposureBlockLogMapper">
  <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.limit.mybatis.entity.TytExposureBlockLog">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="exposure_block_id" jdbcType="BIGINT" property="exposureBlockId" />
    <result column="block_status" jdbcType="INTEGER" property="blockStatus" />
    <result column="permanent_block" jdbcType="INTEGER" property="permanentBlock" />
    <result column="operate_user_id" jdbcType="BIGINT" property="operateUserId" />
    <result column="operate_user_name" jdbcType="VARCHAR" property="operateUserName" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="block_begin_time" jdbcType="TIMESTAMP" property="blockBeginTime" />
    <result column="block_end_time" jdbcType="TIMESTAMP" property="blockEndTime" />
  </resultMap>
</mapper>