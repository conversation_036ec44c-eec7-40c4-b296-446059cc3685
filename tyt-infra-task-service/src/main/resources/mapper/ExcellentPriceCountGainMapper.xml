<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.goods.excellent.mapper.ExcellentPriceCountGainMapper">

    <!-- 查询无效数据 -->
    <select id="getInvalidList" resultType="com.teyuntong.infra.task.service.biz.goods.excellent.entity.ExcellentPriceCountGainDO">
        select *
        from tyt_excellent_price_count_gain
        where status = 1
        and id > #{startId}
        and ( used_count >= gain_count or #{today} > expire_date_end )
        limit 100
    </select>

</mapper>
