<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.car.mybatis.mapper.BrowseLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.car.mybatis.entity.BrowseLogDO">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="msg_id" property="msgId"/>
        <result column="phone" property="phone"/>
        <result column="module_type" property="moduleType"/>
        <result column="function_type" property="functionType"/>
        <result column="ctime" property="ctime"/>
        <result column="client_sign" property="clientSign"/>
        <result column="client_version" property="clientVersion"/>
        <result column="details" property="details"/>
        <result column="t1" property="t1"/>
        <result column="t2" property="t2"/>
        <result column="t3" property="t3"/>
        <result column="t4" property="t4"/>
        <result column="t5" property="t5"/>
        <result column="t6" property="t6"/>
        <result column="t7" property="t7"/>
        <result column="t8" property="t8"/>
        <result column="t9" property="t9"/>
        <result column="t10" property="t10"/>
        <result column="t11" property="t11"/>
        <result column="t12" property="t12"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , user_id, msg_id, phone, module_type, function_type, ctime, client_sign, client_version, details, t1, t2, t3, t4, t5, t6, t7, t8, t9, t10, t11, t12
    </sql>
    <select id="getBrowseNbr"
            resultType="com.teyuntong.infra.task.service.biz.user.car.dto.TytBrowseLogDto">
        SELECT module_type moduleType, msg_id msgId, COUNT(*) cc, MAX(id) id
        FROM tyt_browse_log
        WHERE msg_id IS NOT NULL
          AND module_type != 1 AND function_type=2 AND id > #{id}
        GROUP BY module_type, msg_id
        order by id desc
    </select>

</mapper>
