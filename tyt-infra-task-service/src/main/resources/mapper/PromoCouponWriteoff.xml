<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.coupon.mybatis.mapper.PromoCouponWriteoffMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.coupon.mybatis.entity.PromoCouponWriteoff">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="order_id" property="orderId" />
        <result column="order_amount" property="orderAmount" />
        <result column="coupon_id" property="couponId" />
        <result column="coupon_name" property="couponName" />
        <result column="coupon_type_id" property="couponTypeId" />
        <result column="coupon_amount" property="couponAmount" />
        <result column="coupon_status" property="couponStatus" />
        <result column="mtime" property="mtime" />
        <result column="ctime" property="ctime" />
    </resultMap>



    <select id="isSuccessPayForOrderId" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM `tyt_success_account` WHERE order_no = #{orderId}
    </select>

    <update id="updateStatus">
        UPDATE `promo_coupon_writeoff` SET coupon_status = 1,mtime = NOW() WHERE order_id = #{orderId} AND coupon_status=2
    </update>

    <select id="getPromoCouponByParams" resultMap="BaseResultMap">
        SELECT id,order_id,coupon_id FROM `promo_coupon_writeoff` where  user_id = #{payUserId} and coupon_status=#{couponStatus} and order_id=#{orderId} limit 1;
    </select>
</mapper>
