<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.car.mybatis.mapper.SuperiorCarSignMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.car.mybatis.entity.SuperiorCarSignDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="sign_status" property="signStatus" />
        <result column="is_always" property="isAlways" />
        <result column="restrict_num" property="restrictNum" />
        <result column="restrict_start_time" property="restrictStartTime" />
        <result column="restrict_end_time" property="restrictEndTime" />
        <result column="remark" property="remark" />
        <result column="sign_time" property="signTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, sign_status, is_always, restrict_num, restrict_start_time, restrict_end_time, remark, sign_time, update_time
    </sql>

    <select id="selectListByParams" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from tyt_superior_car_sign where sign_status=#{signStatus} and is_always=#{isAlways}
    </select>

    <select id="selectByUserId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from tyt_superior_car_sign where user_id=#{userId} order by id desc limit 1
    </select>

</mapper>
