<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.market.activity.mybatis.mapper.MarketingActivityChargeOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.MarketingActivityChargeOrderDO">
        <id column="id" property="id" />
        <result column="activity_id" property="activityId" />
        <result column="round" property="round" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, activity_id, round, start_time, end_time, create_time, modify_time
    </sql>

    <select id="getByActivityId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
            from marketing_activity_charge_order
        where activity_id = #{activityId}
    </select>
</mapper>
