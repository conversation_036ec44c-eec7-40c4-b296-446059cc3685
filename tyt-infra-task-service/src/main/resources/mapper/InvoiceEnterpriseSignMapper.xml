<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.enterprise.mybatis.InvoiceEnterpriseSignMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.enterprise.pojo.InvoiceEnterpriseSignDO">
        <id column="id" property="id" />
        <result column="enterprise_id" property="enterpriseId" />
        <result column="enterprise_name" property="enterpriseName" />
        <result column="enterprise_credit_code" property="enterpriseCreditCode" />
        <result column="legal_person_name" property="legalPersonName" />
        <result column="legal_person_phone" property="legalPersonPhone" />
        <result column="legal_person_card" property="legalPersonCard" />
        <result column="enable" property="enable" />
        <result column="user_id" property="userId" />
        <result column="user_phone" property="userPhone" />
        <result column="contract_status" property="contractStatus" />
        <result column="contract_blank_url" property="contractBlankUrl" />
        <result column="contract_name" property="contractName" />
        <result column="contract_number" property="contractNumber" />
        <result column="contract_start_time" property="contractStartTime" />
        <result column="contract_end_time" property="contractEndTime" />
        <result column="sign_account_id" property="signAccountId" />
        <result column="sign_seal_url" property="signSealUrl" />
        <result column="sign_flow_id" property="signFlowId" />
        <result column="signer_verify_status" property="signerVerifyStatus" />
        <result column="sign_type" property="signType" />
        <result column="active_auth_status" property="activeAuthStatus" />
        <result column="sign_confirm_status" property="signConfirmStatus" />
        <result column="sign_confirm_time" property="signConfirmTime" />
        <result column="sign_auth_url" property="signAuthUrl" />
        <result column="sign_auth_time" property="signAuthTime" />
        <result column="contract_sign_url" property="contractSignUrl" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
        <result column="sign_service_id" property="signServiceId" />
        <result column="enterprise_flow_id" property="enterpriseFlowId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, enterprise_id, enterprise_name, enterprise_credit_code, legal_person_name, legal_person_phone, legal_person_card, enable, user_id, user_phone, contract_status, contract_blank_url, contract_name, contract_number, contract_start_time, contract_end_time, sign_account_id, sign_seal_url, sign_flow_id, signer_verify_status, sign_type, active_auth_status, sign_confirm_status, sign_confirm_time, sign_auth_url, sign_auth_time, contract_sign_url, create_time, modify_time, sign_service_id, enterprise_flow_id
    </sql>
    <select id="getEnterpriseSignWithEnterpriseId" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from tyt_invoice_enterprise_sign
        where enterprise_id = #{id}
        and enable = 1
    </select>

</mapper>
