<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.enterprise.mybatis.InvoiceTaxRateRuleMapper">

<!--  <select id="getTaxRateFloatOpenList" resultType="com.teyuntong.infra.task.service.biz.enterprise.pojo.TytInvoiceEnterprise">-->
<!--    select id, enterprise_credit_code enterpriseCreditCode, enterprise_tax_rate enterpriseTaxRate,-->
<!--    from tyt_invoice_enterprise-->
<!--    where tax_rate_float = 1-->
<!--    <if test="id != null">-->
<!--      and id > #{id}-->
<!--    </if>-->
<!--    order by id limit 50-->
<!--  </select>-->
</mapper>