<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.car.mybatis.mapper.CarCurrentLocationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.car.mybatis.entity.CarCurrentLocationDO">
        <id column="id" property="id" />
        <result column="car_id" property="carId" />
        <result column="head_city" property="headCity" />
        <result column="head_no" property="headNo" />
        <result column="tail_city" property="tailCity" />
        <result column="tail_no" property="tailNo" />
        <result column="car_owner_name" property="carOwnerName" />
        <result column="car_owner_cellphone" property="carOwnerCellphone" />
        <result column="use_beidou" property="useBeidou" />
        <result column="beidou_status" property="beidouStatus" />
        <result column="follow_car_status" property="followCarStatus" />
        <result column="first_driver_phone" property="firstDriverPhone" />
        <result column="second_car_phone" property="secondCarPhone" />
        <result column="follow_car_phone" property="followCarPhone" />
        <result column="new_location_time" property="newLocationTime" />
        <result column="new_location" property="newLocation" />
        <result column="utime" property="utime" />
        <result column="ctime" property="ctime" />
        <result column="location_longitude" property="locationLongitude" />
        <result column="location_latitude" property="locationLatitude" />
        <result column="dadi_x" property="dadiX" />
        <result column="dadi_y" property="dadiY" />
        <result column="province" property="province" />
        <result column="city" property="city" />
        <result column="area" property="area" />
        <result column="car_owner_id" property="carOwnerId" />
        <result column="need_position" property="needPosition" />
        <result column="locaiton_type" property="locaitonType" />
        <result column="speed" property="speed" />
        <result column="dirve_position" property="dirvePosition" />
        <result column="remark" property="remark" />
        <result column="licence_color" property="licenceColor" />
        <result column="status" property="status" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, car_id, head_city, head_no, tail_city, tail_no, car_owner_name, car_owner_cellphone, use_beidou, beidou_status, follow_car_status, first_driver_phone, second_car_phone, follow_car_phone, new_location_time, new_location, utime, ctime, location_longitude, location_latitude, dadi_x, dadi_y, province, city, area, car_owner_id, need_position, locaiton_type, speed, dirve_position, remark, licence_color, status
    </sql>
    <update id="updateSyncCarLocation">
        UPDATE tyt.`tyt_car_current_location` a
            INNER JOIN tyt_recommend.api_dw_zhiyun_car_location_status b
        ON a.head_city = b.car_head_city AND a.head_no = b.car_head_no
            SET a.new_location_time = b.upload_time,
                a.new_location = b.adr,
                a.location_longitude = b.lon,
                a.location_latitude = b.lat,
                a.province = b.province,
                a.city = b.city,
                a.area = b.country,
                a.speed = b.spd,
                a.dirve_position = b.drc,
                a.licence_color = 2,
                a.`dadi_x`= b.`coord_x`,
                a.`dadi_y` = b.`coord_y`,
                a.`utime` = NOW()
    </update>

</mapper>
