<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.car.mybatis.mapper.CarDetailHeadMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.car.mybatis.entity.CarDetailHeadDO">
        <id column="id" property="id" />
        <result column="car_id" property="carId" />
        <result column="user_id" property="userId" />
        <result column="city" property="city" />
        <result column="car_no" property="carNo" />
        <result column="car_type" property="carType" />
        <result column="owner" property="owner" />
        <result column="address" property="address" />
        <result column="use_nature" property="useNature" />
        <result column="car_brand" property="carBrand" />
        <result column="car_idcode" property="carIdcode" />
        <result column="car_engine_no" property="carEngineNo" />
        <result column="car_register" property="carRegister" />
        <result column="issue_date" property="issueDate" />
        <result column="record_no" property="recordNo" />
        <result column="people" property="people" />
        <result column="total_weight" property="totalWeight" />
        <result column="curb_weight" property="curbWeight" />
        <result column="check_weight" property="checkWeight" />
        <result column="tow_weight" property="towWeight" />
        <result column="scrap_date" property="scrapDate" />
        <result column="test_date" property="testDate" />
        <result column="length" property="length" />
        <result column="width" property="width" />
        <result column="height" property="height" />
        <result column="state" property="state" />
        <result column="head_succ_remark" property="headSuccRemark" />
        <result column="head_belong_type" property="headBelongType" />
        <result column="blong_type" property="blongType" />
        <result column="car_brand_detail" property="carBrandDetail" />
        <result column="check_record" property="checkRecord" />
        <result column="energy_type" property="energyType" />
        <result column="social_credit_code" property="socialCreditCode" />
        <result column="road_card_positive_url" property="roadCardPositiveUrl" />
        <result column="road_card_other_side_url" property="roadCardOtherSideUrl" />
        <result column="road_business_name" property="roadBusinessName" />
        <result column="road_card_car_city" property="roadCardCarCity" />
        <result column="road_card_car_no" property="roadCardCarNo" />
        <result column="road_card_expir_date" property="roadCardExpirDate" />
        <result column="road_license_no_url" property="roadLicenseNoUrl" />
        <result column="road_license_no" property="roadLicenseNo" />
        <result column="road_license_no_expir_start_date" property="roadLicenseNoExpirStartDate" />
        <result column="car_colour" property="carColour" />
        <result column="road_license_no_expir_end_date" property="roadLicenseNoExpirEndDate" />
        <result column="road_transport_certificate_no" property="roadTransportCertificateNo" />
        <result column="road_transport_type" property="roadTransportType" />
        <result column="maximum_axle_load" property="maximumAxleLoad" />
        <result column="axles" property="axles" />
        <result column="head_issue_authority" property="headIssueAuthority" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, car_id, user_id, city, car_no, car_type, owner, address, use_nature, car_brand, car_idcode, car_engine_no, car_register, issue_date, record_no, people, total_weight, curb_weight, check_weight, tow_weight, scrap_date, test_date, length, width, height, state, head_succ_remark, head_belong_type, blong_type, car_brand_detail, check_record, energy_type, social_credit_code, road_card_positive_url, road_card_other_side_url, road_business_name, road_card_car_city, road_card_car_no, road_card_expir_date, road_license_no_url, road_license_no, road_license_no_expir_start_date, car_colour, road_license_no_expir_end_date, road_transport_certificate_no, road_transport_type, maximum_axle_load, axles, head_issue_authority
    </sql>

    <select id="getByCarId" resultMap="BaseResultMap">
        select * from tyt_car_detail_head where car_id = #{carId} limit 1
    </select>

</mapper>
