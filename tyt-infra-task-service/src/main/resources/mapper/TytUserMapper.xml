<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.info.mybatis.mapper.TytUserMapper">
  <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.info.mybatis.entity.User">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="cell_phone" jdbcType="VARCHAR" property="cellPhone" />
    <result column="qq" jdbcType="BIGINT" property="qq" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="true_name" jdbcType="VARCHAR" property="trueName" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="user_type" jdbcType="SMALLINT" property="userType" />
    <result column="black_status" jdbcType="SMALLINT" property="blackStatus" />
    <result column="user_sign" jdbcType="SMALLINT" property="userSign" />
    <result column="home_phone" jdbcType="VARCHAR" property="homePhone" />
    <result column="pc_sign" jdbcType="VARCHAR" property="pcSign" />
    <result column="serve_days" jdbcType="INTEGER" property="serveDays" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="qq_mod_times" jdbcType="SMALLINT" property="qqModTimes" />
    <result column="qq_mod_time" jdbcType="TIMESTAMP" property="qqModTime" />
    <result column="verify_code" jdbcType="VARCHAR" property="verifyCode" />
    <result column="pay_date" jdbcType="TIMESTAMP" property="payDate" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="ticket" jdbcType="VARCHAR" property="ticket" />
    <result column="info_upload_flag" jdbcType="INTEGER" property="infoUploadFlag" />
    <result column="contact_num" jdbcType="INTEGER" property="contactNum" />
    <result column="info_publish_flag" jdbcType="SMALLINT" property="infoPublishFlag" />
    <result column="kill_bill" jdbcType="SMALLINT" property="killBill" />
    <result column="sales" jdbcType="VARCHAR" property="sales" />
    <result column="verify_flag" jdbcType="SMALLINT" property="verifyFlag" />
    <result column="plat_id" jdbcType="SMALLINT" property="platId" />
    <result column="note" jdbcType="VARCHAR" property="note" />
    <result column="phone_open_flag" jdbcType="SMALLINT" property="phoneOpenFlag" />
    <result column="county" jdbcType="VARCHAR" property="county" />
    <result column="renewal_date" jdbcType="TIMESTAMP" property="renewalDate" />
    <result column="renewal_years" jdbcType="SMALLINT" property="renewalYears" />
    <result column="qq_box_flag" jdbcType="SMALLINT" property="qqBoxFlag" />
    <result column="pay_status" jdbcType="SMALLINT" property="payStatus" />
    <result column="phone_serve_days" jdbcType="INTEGER" property="phoneServeDays" />
    <result column="recommender_tel" jdbcType="VARCHAR" property="recommenderTel" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="appoint_time" jdbcType="TIMESTAMP" property="appointTime" />
    <result column="is_car" jdbcType="CHAR" property="isCar" />
    <result column="is_bank" jdbcType="CHAR" property="isBank" />
    <result column="head_url" jdbcType="VARCHAR" property="headUrl" />
    <result column="pay_password" jdbcType="VARCHAR" property="payPassword" />
    <result column="client_sign" jdbcType="SMALLINT" property="clientSign" />
    <result column="is_mock" jdbcType="SMALLINT" property="isMock" />
    <result column="last_time" jdbcType="TIMESTAMP" property="lastTime" />
    <result column="client_version" jdbcType="VARCHAR" property="clientVersion" />
    <result column="os_version" jdbcType="VARCHAR" property="osVersion" />
    <result column="sex" jdbcType="CHAR" property="sex" />
    <result column="deliver_type" jdbcType="CHAR" property="deliverType" />
    <result column="is_enabled" jdbcType="CHAR" property="isEnabled" />
    <result column="source_remark" jdbcType="CHAR" property="sourceRemark" />
    <result column="payment_reason" jdbcType="CHAR" property="paymentReason" />
    <result column="bank" jdbcType="VARCHAR" property="bank" />
    <result column="money" jdbcType="BIGINT" property="money" />
    <result column="pay_number" jdbcType="INTEGER" property="payNumber" />
    <result column="extra_days" jdbcType="INTEGER" property="extraDays" />
    <result column="pay_channel" jdbcType="CHAR" property="payChannel" />
    <result column="verify_photo_sign" jdbcType="SMALLINT" property="verifyPhotoSign" />
    <result column="user_part" jdbcType="INTEGER" property="userPart" />
    <result column="channel" jdbcType="SMALLINT" property="channel" />
    <result column="is_dispatch" jdbcType="INTEGER" property="isDispatch" />
    <result column="c1" jdbcType="VARCHAR" property="c1" />
    <result column="c2" jdbcType="VARCHAR" property="c2" />
    <result column="c3" jdbcType="VARCHAR" property="c3" />
    <result column="user_class" jdbcType="SMALLINT" property="userClass" />
    <result column="identity_type" jdbcType="SMALLINT" property="identityType" />
    <result column="car_user_sign" jdbcType="SMALLINT" property="carUserSign" />
    <result column="goods_user_sign" jdbcType="SMALLINT" property="goodsUserSign" />
    <result column="car_user_name" jdbcType="VARCHAR" property="carUserName" />
    <result column="car_last_login_time" jdbcType="TIMESTAMP" property="carLastLoginTime" />
    <result column="goods_last_login_time" jdbcType="TIMESTAMP" property="goodsLastLoginTime" />
    <result column="register_identity" jdbcType="INTEGER" property="registerIdentity" />
    <result column="selection_identity" jdbcType="INTEGER" property="selectionIdentity" />
    <result column="initial_num" jdbcType="INTEGER" property="initialNum" />
    <result column="initial_car_num" jdbcType="INTEGER" property="initialCarNum" />
  </resultMap>

  <resultMap id="UserInformResultMap" type="com.teyuntong.infra.task.service.biz.market.activity.dto.UserInformBean">
    <id column="id" jdbcType="BIGINT" property="userId" />
    <result column="cell_phone" jdbcType="VARCHAR" property="cellPhone" />
  </resultMap>

  <select id="selectUserById" resultMap="BaseResultMap">
    SELECT * FROM tyt_user WHERE id = #{userId}
  </select>

  <update id="updateUserIsCarType" parameterType="long">
    update tyt_user set is_car = 0, mtime = now() where id = #{userId}
  </update>

  <update id="updateUserLocation">
    UPDATE tyt_user
    SET province = #{province}, city = #{city}
    where id = #{id}
  </update>

    <update id="decreServerDays">
      update tyt_user set serve_days = serve_days - 1,mtime= now() where serve_days > 0 and ctime &lt; #{dateStr}
    </update>

  <update id="decrePhoneServerDays">
    update tyt_user set phone_serve_days = phone_serve_days - 1  where phone_serve_days > 0 and ctime &lt; #{dateStr}
  </update>

  <select id="queryUserLocationCount" resultType="java.lang.Integer">
    SELECT COUNT(*)
    FROM tyt_user
    WHERE cell_phone != "0" AND (province is null OR LENGTH(province) = 0) AND ctime >= #{queryDate}
  </select>
  <select id="queryUserLocationListByPage" resultMap="UserInformResultMap">
    SELECT id,cell_phone
    FROM tyt_user
    WHERE id > #{lastId} AND cell_phone != "0" AND (province is null OR LENGTH(province) = 0) AND ctime >= #{queryDate}
      limit 0,1000
  </select>

  <select id="getNeedInformUsers" resultMap="UserInformResultMap">
    select id, cell_phone from tyt_user where is_mock=0
    <if test="port==1">
      and car_last_login_time BETWEEN #{startTime,jdbcType=VARCHAR} and #{endTime,jdbcType=VARCHAR}
    </if>
    <if test="port==2">
      and goods_last_login_time BETWEEN #{startTime,jdbcType=VARCHAR} and #{endTime,jdbcType=VARCHAR}
    </if>
    <if test="null != blackUsers">
      and id not in (
      <foreach collection="blackUsers" item="item" separator=",">
        #{item}
      </foreach>
      )
    </if>
    <if test="null != blackArea">
      and city not in(
      <foreach collection="blackArea" item="item" separator=",">
        #{item}
      </foreach>
      )
    </if>
     and id &gt; #{maxUserId} order by id asc limit #{pageSize}
  </select>

  <select id="getNeedInformUsersForVip" resultMap="UserInformResultMap">
    select id, cell_phone from tyt_user where is_mock=0
    <if test="null != blackArea">
      and city not in(
      <foreach collection="blackArea" item="item" separator=",">
        #{item}
      </foreach>
      )
    </if>
    and id in (
    <foreach collection="userIds" item="item" separator=",">
      #{item}
    </foreach>
    )
  </select>

  <select id="getUsersByRegister" resultMap="UserInformResultMap">
    select id, cell_phone from tyt_user where is_mock=0
    <if test="port==1">
      and plat_id in(21,31)
    </if>
    <if test="port==2">
      and plat_id in(22,32)
    </if>
    <if test="null != blackUsers">
      and id not in (
      <foreach collection="blackUsers" item="item" separator=",">
        #{item}
      </foreach>
      )
    </if>
    <if test="null != blackArea">
      and city not in(
      <foreach collection="blackArea" item="item" separator=",">
        #{item}
      </foreach>
      )
    </if>
    and ctime BETWEEN #{startTime,jdbcType=VARCHAR} and #{endTime,jdbcType=VARCHAR}
    and id &gt; #{maxUserId} order by id asc limit #{pageSize}
  </select>

  <select id="getLoginUserList" resultMap="BaseResultMap">
    select * from tyt_user
    where is_mock = 0
    and ctime >= #{startTime}
    and ctime &lt;= #{endTime}
    and car_last_login_time >= #{startTime}
    and car_last_login_time &lt;= #{endTime}
    order by id
    limit #{pageSize}
  </select>

  <select id="getByPhone" resultMap="BaseResultMap">
    select *
    from tyt_user
    where cell_phone = #{phone}
    limit 1
  </select>


  <select id="getNoPublishUser" resultType="com.teyuntong.infra.task.service.biz.user.info.mybatis.entity.User">
    select
      tu.id,
      tu.cell_phone cellPhone
    from tyt_user tu
           inner join tyt_user_sub us on tu.id = us.user_id
    where tu.id > #{startUserId}
      and tu.plat_id in (22, 32)
      and tu.ctime &gt;= #{startTime}
      and tu.ctime &lt;= #{endTime}
      and (
          us.publish_num = 0
        or us.publish_num is null
      )
    order by tu.id asc
      limit #{pageSize}
  </select>

  <select id="selectAllDate" resultMap="BaseResultMap">
    select
      tu.id,
      tu.cell_phone cellPhone
    from tyt_user tu

    where tu.id > #{maxId}
      and tu.plat_id in (22, 32)
      and tu.ctime &gt;= #{minuteDate}
      and tu.ctime &lt;= #{date}
      and tu.is_mock = 0
    order by tu.id asc
      limit #{limit}
  </select>

  <select id="getPublishUserIdList" resultType="java.lang.Long">
    select distinct(user_id) disUserId from tyt_transport
    where user_id > #{startUserId}
      and ctime &gt;= #{startTime}
      and ctime &lt;= #{endTime}
    order by user_id asc
      limit #{pageSize}
  </select>
  <select id="getMockUser" resultType="java.lang.Long">
    SELECT u.id
    FROM tyt_user u
    WHERE u.id > #{userId}
      and is_mock = 0
  </select>

  <select id="getIdentityNeedMsgUser" resultMap="BaseResultMap">
    SELECT * FROM tyt_user u WHERE u.ctime between #{startTime} and #{endTime} and u.is_mock=0 and u.verify_photo_sign != 1 limit 100
  </select>

  <select id="selectIdByBlackStatusAndGoodUserSign" resultType="java.lang.Long">
    SELECT id FROM tyt_user WHERE black_status=#{blackStatus} and goods_user_sign= #{goodUserSign}
  </select>

  <select id="getByCtime" resultMap="BaseResultMap">
    select * from tyt_user
    where is_mock = 0
      and ctime &gt;= #{startTime}
      and ctime &lt;= #{endTime}
    order by id
  </select>

  <select id="getNextPageIdList" resultType="java.lang.Long">
    select id from tyt_user
    where id &gt; #{startUserId}
      and mtime &gt; #{startTime}
    order by id asc
      limit 500
  </select>

  <select id="getCreeperUser" resultType="java.lang.Long">
    select newtab.user_id from (
                    select tab.user_id as user_id, tu.true_name as true_name, tab.count as count from (select user_id, count(id) as count
             from tyt_creeper_app_request_log where create_time >= CURDATE() and type = 1 group by user_id order by count desc limit 100
    ) tab inner join tyt_user tu on tab.user_id = tu.id ) newtab where newtab.true_name is null and newtab.count > 50 and newtab.user_id > 650000
  </select>

  <select id="getCreeperUserIsBlack" resultType="integer">
    select count(1)
    from blacklist_user where status = 1 and perpetual = 1 and user_id = #{userId};
  </select>


  <select id = "getByCtimeForNewUser" resultMap="BaseResultMap">
    select id,cell_phone,ctime,verify_photo_sign from tyt_user
    where is_mock = 0
      <if test="userId != null">
        and id &gt; #{userId}
      </if>
    <if test="startTime != null">
      and ctime &gt;= #{startTime}
    </if>
    and ctime &lt;= #{endTime}
    order by id asc limit 1000;
  </select>

   <select id="getByCarLoginTime" resultType="java.lang.Long">
        select id from tyt_user
                  where id &gt; #{startUserId}
                    and car_last_login_time &gt;= #{startTime}
                    and car_last_login_time &lt;= #{endTime}
        order by id asc limit 100
  </select>

  <select id="getLogListById" resultType="java.lang.Long">
    select id
    from tyt_user
    where id &gt; #{id}
    and mtime > '2025-06-03'
    order by id asc limit 1000
  </select>

</mapper>