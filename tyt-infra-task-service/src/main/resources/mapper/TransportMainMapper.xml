<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.goods.goodsinfo.mapper.TransportMainMapper">

    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.goods.goodsinfo.entity.TytTransportMain">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="start_point" property="startPoint" jdbcType="VARCHAR"/>
        <result column="dest_point" property="destPoint" jdbcType="VARCHAR"/>
        <result column="task_content" property="taskContent" jdbcType="VARCHAR"/>
        <result column="tel" property="tel" jdbcType="VARCHAR"/>
        <result column="pub_time" property="pubTime" jdbcType="VARCHAR"/>
        <result column="pub_qq" property="pubQq" jdbcType="BIGINT"/>
        <result column="nick_name" property="nickName" jdbcType="VARCHAR"/>
        <result column="user_show_name" property="userShowName" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="source" property="source" jdbcType="INTEGER"/>
        <result column="ctime" property="ctime" jdbcType="TIMESTAMP"/>
        <result column="mtime" property="mtime" jdbcType="TIMESTAMP"/>
        <result column="upload_cellphone" property="uploadCellphone" jdbcType="VARCHAR"/>
        <result column="resend" property="resend" jdbcType="INTEGER"/>
        <result column="start_coord" property="startCoord" jdbcType="VARCHAR"/>
        <result column="dest_coord" property="destCoord" jdbcType="VARCHAR"/>
        <result column="plat_id" property="platId" jdbcType="INTEGER"/>
        <result column="verify_flag" property="verifyFlag" jdbcType="INTEGER"/>
        <result column="price" property="price" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="price_code" property="priceCode" jdbcType="VARCHAR"/>
        <result column="start_coord_x" property="startCoordXValue" jdbcType="INTEGER"/>
        <result column="start_coord_y" property="startCoordYValue" jdbcType="INTEGER"/>
        <result column="dest_coord_x" property="destCoordXValue" jdbcType="INTEGER"/>
        <result column="dest_coord_y" property="destCoordYValue" jdbcType="INTEGER"/>
        <result column="start_detail_add" property="startDetailAdd" jdbcType="VARCHAR"/>
        <result column="start_longitude" property="startLongitudeValue" jdbcType="INTEGER"/>
        <result column="start_latitude" property="startLatitudeValue" jdbcType="INTEGER"/>
        <result column="dest_detail_add" property="destDetailAdd" jdbcType="VARCHAR"/>
        <result column="dest_longitude" property="destLongitudeValue" jdbcType="INTEGER"/>
        <result column="dest_latitude" property="destLatitudeValue" jdbcType="INTEGER"/>
        <result column="pub_date" property="pubDate" jdbcType="TIMESTAMP"/>
        <result column="goods_code" property="goodsCode" jdbcType="VARCHAR"/>
        <result column="weight_code" property="weightCode" jdbcType="VARCHAR"/>
        <result column="weight" property="weight" jdbcType="VARCHAR"/>
        <result column="length" property="length" jdbcType="VARCHAR"/>
        <result column="wide" property="wide" jdbcType="VARCHAR"/>
        <result column="high" property="high" jdbcType="VARCHAR"/>
        <result column="is_superelevation" property="isSuperelevation" jdbcType="VARCHAR"/>
        <result column="linkman" property="linkman" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="distance" property="distanceValue" jdbcType="INTEGER"/>
        <result column="pub_goods_time" property="pubGoodsTime" jdbcType="TIMESTAMP"/>
        <result column="tel3" property="tel3" jdbcType="VARCHAR"/>
        <result column="tel4" property="tel4" jdbcType="VARCHAR"/>
        <result column="display_type" property="displayType" jdbcType="VARCHAR"/>
        <result column="hash_code" property="hashCode" jdbcType="VARCHAR"/>
        <result column="is_car" property="isCar" jdbcType="VARCHAR"/>
        <result column="user_type" property="userType" jdbcType="INTEGER"/>
        <result column="pc_old_content" property="pcOldContent" jdbcType="VARCHAR"/>
        <result column="resend_counts" property="resendCounts" jdbcType="INTEGER"/>
        <result column="verify_photo_sign" property="verifyPhotoSign" jdbcType="INTEGER"/>
        <result column="user_part" property="userPart" jdbcType="INTEGER"/>
        <result column="start_city" property="startCity" jdbcType="VARCHAR"/>
        <result column="src_msg_id" property="srcMsgId" jdbcType="BIGINT"/>
        <result column="start_provinc" property="startProvinc" jdbcType="VARCHAR"/>
        <result column="start_area" property="startArea" jdbcType="VARCHAR"/>
        <result column="dest_provinc" property="destProvinc" jdbcType="VARCHAR"/>
        <result column="dest_city" property="destCity" jdbcType="VARCHAR"/>
        <result column="dest_area" property="destArea" jdbcType="VARCHAR"/>
        <result column="client_version" property="clientVersion" jdbcType="VARCHAR"/>
        <result column="is_info_fee" property="isInfoFee" jdbcType="VARCHAR"/>
        <result column="info_status" property="infoStatus" jdbcType="VARCHAR"/>
        <result column="ts_order_no" property="tsOrderNo" jdbcType="VARCHAR"/>
        <result column="release_time" property="releaseTime" jdbcType="TIMESTAMP"/>
        <result column="reg_time" property="regTime" jdbcType="TIMESTAMP"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="brand" property="brand" jdbcType="VARCHAR"/>
        <result column="good_type_name" property="goodTypeName" jdbcType="VARCHAR"/>
        <result column="good_number" property="goodNumber" jdbcType="INTEGER"/>
        <result column="is_standard" property="isStandard" jdbcType="INTEGER"/>
        <result column="match_item_id" property="matchItemId" jdbcType="INTEGER"/>
        <result column="android_distance" property="androidDistanceValue" jdbcType="INTEGER"/>
        <result column="ios_distance" property="iosDistanceValue" jdbcType="INTEGER"/>
        <result column="is_display" property="isDisplay" jdbcType="INTEGER"/>
        <result column="refer_length" property="referLength" jdbcType="INTEGER"/>
        <result column="refer_width" property="referWidth" jdbcType="INTEGER"/>
        <result column="refer_height" property="referHeight" jdbcType="INTEGER"/>
        <result column="refer_weight" property="referWeight" jdbcType="INTEGER"/>
        <result column="car_length" property="carLength" jdbcType="VARCHAR"/>
        <result column="loading_time" property="loadingTime" jdbcType="TIMESTAMP"/>
        <result column="begin_unload_time" property="beginUnloadTime" jdbcType="TIMESTAMP"/>
        <result column="unload_time" property="unloadTime" jdbcType="TIMESTAMP"/>
        <result column="car_min_length" property="carMinLength" jdbcType="DECIMAL"/>
        <result column="car_max_length" property="carMaxLength" jdbcType="DECIMAL"/>
        <result column="car_type" property="carType" jdbcType="VARCHAR"/>
        <result column="begin_loading_time" property="beginLoadingTime" jdbcType="TIMESTAMP"/>
        <result column="car_style" property="carStyle" jdbcType="VARCHAR"/>
        <result column="work_plane_min_high" property="workPlaneMinHigh" jdbcType="DECIMAL"/>
        <result column="work_plane_max_high" property="workPlaneMaxHigh" jdbcType="DECIMAL"/>
        <result column="work_plane_min_length" property="workPlaneMinLength" jdbcType="DECIMAL"/>
        <result column="work_plane_max_length" property="workPlaneMaxLength" jdbcType="DECIMAL"/>
        <result column="climb" property="climb" jdbcType="VARCHAR"/>
        <result column="order_number" property="orderNumber" jdbcType="INTEGER"/>
        <result column="evaluate" property="evaluate" jdbcType="INTEGER"/>
        <result column="special_required" property="specialRequired" jdbcType="VARCHAR"/>
        <result column="similarity_code" property="similarityCode" jdbcType="VARCHAR"/>
        <result column="similarity_first_id" property="similarityFirstId" jdbcType="BIGINT"/>
        <result column="similarity_first_info" property="similarityFirstInfo" jdbcType="VARCHAR"/>
        <result column="tyre_exposed_flag" property="tyreExposedFlag" jdbcType="VARCHAR"/>
        <result column="car_length_labels" property="carLengthLabels" jdbcType="VARCHAR"/>
        <result column="shunting_quantity" property="shuntingQuantity" jdbcType="INTEGER"/>
        <result column="first_publish_type" property="firstPublishType" jdbcType="INTEGER"/>
        <result column="publish_type" property="publishType" jdbcType="INTEGER"/>
        <result column="info_fee" property="infoFee" jdbcType="DECIMAL"/>
        <result column="is_delete" property="isDelete" jdbcType="INTEGER"/>
        <result column="exclusive_type" property="exclusiveType" jdbcType="INTEGER"/>
        <result column="total_score" property="totalScore" jdbcType="DECIMAL"/>
        <result column="rank_level" property="rankLevel" jdbcType="INTEGER"/>
        <result column="is_show" property="isShow" jdbcType="INTEGER"/>
        <result column="refund_flag" property="refundFlag" jdbcType="INTEGER"/>
        <result column="source_type" property="sourceType" jdbcType="INTEGER"/>
        <result column="trade_num" property="tradeNum" jdbcType="INTEGER"/>
        <result column="auth_name" property="authName" jdbcType="VARCHAR"/>
        <result column="label_json" property="labelJson" jdbcType="VARCHAR"/>
        <result column="guarantee_goods" property="guaranteeGoods" jdbcType="INTEGER"/>
        <result column="credit_retop" property="creditRetop" jdbcType="INTEGER"/>
        <result column="sort_type" property="sortType" jdbcType="INTEGER"/>
        <result column="priority_recommend_expire_time" property="priorityRecommendExpireTime" jdbcType="TIMESTAMP"/>
        <result column="excellent_goods" property="excellentGoods" jdbcType="INTEGER"/>
        <result column="excellent_goods_two" property="excellentGoodsTwo" jdbcType="INTEGER"/>
        <result column="publish_goods_type" property="publishGoodsType" jdbcType="INTEGER"/>
        <result column="driver_driving" property="driverDriving" jdbcType="INTEGER"/>
        <result column="load_cell_phone" property="loadCellPhone" jdbcType="VARCHAR"/>
        <result column="unload_cell_phone" property="unloadCellPhone" jdbcType="VARCHAR"/>
        <result column="cargo_owner_id" property="cargoOwnerId" jdbcType="BIGINT"/>
        <result column="tec_service_fee" property="tecServiceFee" jdbcType="DECIMAL"/>
        <result column="machine_remark" property="machineRemark" jdbcType="VARCHAR"/>
        <result column="excellent_card_id" property="excellentCardId" jdbcType="BIGINT"/>
        <result column="invoice_transport" property="invoiceTransport" jdbcType="INTEGER"/>
        <result column="additional_price" property="additionalPrice" jdbcType="VARCHAR"/>
        <result column="enterprise_tax_rate" property="enterpriseTaxRate" jdbcType="DECIMAL"/>
    </resultMap>

    <update id="updateTecServiceFeeBySrcMsgId">
        update tyt_transport_main set tec_service_fee = #{tecServiceFee} where src_msg_id = #{srcMsgId}
    </update>

    <update id="saveViewCount">
        REPLACE INTO `tyt_transport_view_record` (`src_msg_id`,`view_count`,`ctime`) VALUES( #{srcMsgId}, #{count}, now())
    </update>

    <update id="updateExcellentGoodsTwo">
        update tyt_transport_main
        set excellent_goods_two = 2
        where id in
        <foreach collection="needProcessIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="selectByCtime" resultMap="BaseResultMap">
        select *
        from tyt_transport_main
        where ctime &gt;= #{startDate} and id > #{lastSrcMagId}
        <if test=" endDate != null">
            and ctime &lt;= #{endDate}
        </if>
        order by id limit #{pageSize}
    </select>

    <select id="selectCountByCtime" resultType="java.lang.Integer">
        select
        count(1)
        from tyt_transport_main
        where ctime &gt;= #{startDate}
        <if test=" endDate != null">
            and ctime &lt;= #{endDate}
        </if>
    </select>

    <select id="selectTransportMainById" resultMap="BaseResultMap">
        select id,
               src_msg_id,
               distance,
               shunting_quantity,
               start_longitude,
               start_latitude,
               dest_longitude,
               dest_latitude,
               ctime,
               status
        from tyt_transport_main where id=#{tsId}
    </select>

    <select id="getAllTransportSrcMsgIdByDateParam" resultType="java.lang.Long">
        SELECT id FROM tyt_transport_main WHERE ctime &gt;= #{startDate}  AND ctime &lt; #{endDate}
    </select>
    <select id="selectOfCreditRetop"
            resultType="com.teyuntong.infra.task.service.biz.goods.goodsinfo.entity.TransportCreditRetopVo">
        select count(1) as refreshCount, sum(resend_counts) as refreshNumber, user_id as userId
        from tyt_transport_main
        where user_id in (select user_id from tyt_transport_main
            where ctime > #{beginTime} and ctime &lt;= #{endTime} and rank_level >= 3 and is_delete = 0 and credit_retop = 2 group by user_id )
        and ctime > #{beginTime}
        and ctime &lt;= #{endTime}
        and rank_level >= 3
        and is_delete = 0
        group by user_id
    </select>

    <select id="selectNotDealByDateRange" resultMap="BaseResultMap">
        select *
        from tyt_transport_main
        where ctime between #{start} and #{end} and status = 1
        limit #{startIndex},#{pageSize}
    </select>

    <select id="selectNotDealByDateRangeV2" resultMap="BaseResultMap">
        select *
        from tyt_transport_main
        where ctime between #{start} and #{end} and status = 1 and distance > #{distanceStart} and distance &lt;= #{distanceEnd}
        and source_type not in (2,4)
        limit #{startIndex},#{pageSize}
    </select>

    <select id="selectUserFirstTransportId" resultType="java.lang.Long">
        select id
        from tyt_transport_main force index(IDX_USERID_CTIME)
        where user_id = #{userId} order by ctime asc limit 1
    </select>

    <select id="selectUserFirstThreeExcellentGoods" resultType="java.lang.Long">
        select id
        from tyt_transport_main force index(IDX_USERID_CTIME)
        where user_id = #{userId}
        order by ctime asc
        limit 3
    </select>
	<select id="selectExcellentGoodsByTimeRange"
	        resultType="com.teyuntong.infra.task.service.biz.goods.goodsinfo.entity.TytTransportMain">
        select id,
               label_json as labelJson
        from tyt_transport_main
        where ctime >= #{start} and ctime &lt;= #{end} and excellent_goods = 1 and id > #{lastMaxId}
        order by id asc limit #{pageSize}
    </select>

    <select id="selectPublishingTransports" resultType="java.lang.Long">
        select id
        from tyt_transport_main
        where status = 1 and ctime > CURDATE() and id in
        <foreach collection="srcMsgIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

	<select id="selectBySimilarityCode" resultType="java.lang.Long">
        select id
        from tyt_transport_main
        where similarity_code = #{similarityCode} and id != #{srcMsgId}
    </select>

    <select id="queryPublishingTransport" resultMap="BaseResultMap">
        select *
        from tyt_transport_main
        where ctime > #{start} and ctime <![CDATA[<]]> #{end}  and status = 1
        order by id
        limit #{startIndex}, #{pageSize}
    </select>

    <select id="selectUserCtimes" resultType="com.teyuntong.infra.task.service.biz.goods.goodsinfo.bean.TransportResultVO">
        SELECT
        user_id userId,
        MIN(ctime) minCtime,
        MAX(ctime) maxCtime
        FROM
        tyt_transport_main
        WHERE
        ctime >= #{startTime} AND ctime<![CDATA[<=]]>#{endTime}
        GROUP BY
        user_id
    </select>

    <select id="listLastDayPublishNo" resultType="com.teyuntong.infra.task.service.biz.goods.goodsinfo.bean.UserOrderStatisticsModel">
        SELECT user_id AS userId, COUNT(*) AS orderNo
        FROM tyt_transport_main
        WHERE ctime >= #{startTime}
        AND ctime <![CDATA[<=]]> #{endTime}
        GROUP BY user_id
    </select>


    <select id="listLastDayGoodsNo" resultType="com.teyuntong.infra.task.service.biz.goods.goodsinfo.bean.UserOrderStatisticsModel">
        SELECT user_id AS userId, COUNT(*) AS orderNo
        FROM tyt_transport_main
        WHERE mtime >= #{startTime}
        AND mtime <![CDATA[<]]> #{endTime}
        AND `status` = 4
        GROUP BY user_id
    </select>

    <select id="queryNeedSystemQuotedTransports" resultMap="BaseResultMap">
        select id,
               user_id,
               nick_name,
               ctime,
               similarity_code,
               start_provinc,
               start_city,
               start_area,
               dest_provinc,
               dest_city,
               dest_area,
               task_content,
               weight,
               length,
               wide,
               high,
               excellent_goods,
               distance,
               good_type_name
        from tyt_transport_main
        where ctime > #{startDate} and id > #{lastId} and status = 1 and (price in ("", "0") or price is null) and invoice_transport = 0 and excellent_goods != 2
        order by id asc
        limit #{pageSize}
    </select>

    <select id="selectTodayMinTransportMainId" resultType="java.lang.Long">
        select id
        from tyt_transport_main
        where ctime > current_date()
        limit 1
    </select>

	<select id="selectCanDispatchNonSpecialCarList" resultMap="BaseResultMap">
        select *
        from tyt_transport_main
        where ctime > current_date() and ctime &lt; #{startTime} and start_city = #{startCity} and dest_city = #{destCity}
        and excellent_goods != 2 and status = 1
        limit #{start}, #{pageSize}
    </select>

    <select id="selectCanDispatchNonSpecialCarList2" resultMap="BaseResultMap">
        select *
        from tyt_transport_main
        where ctime > current_date() and ctime &lt; #{startTime}
          and start_city in (
        <foreach collection="cityList" item="city" separator=",">
            #{city}
        </foreach>
            )
          and excellent_goods != 2 and status = 1
        limit #{start}, #{pageSize}
    </select>

    <select id="getUserLastPublishTime" resultType="java.util.Date">
        select ctime
        from tyt_transport_main
        where user_id = #{userId} and ctime &gt; #{startTime}
        order by ctime desc
        limit 1
    </select>


    <select id="getEarliestGoodCarPriceTransportTime" resultType="java.util.Date">
        select ctime from tyt_transport_main where user_id = #{userId} and excellent_goods_two = 2 limit 1;
    </select>

    <!-- 查询一段时间内相似货源数>1的货主id -->
    <select id="getUserIdIfSimilarityCodeGreaterThanOne" resultType="string">
        SELECT GROUP_CONCAT(DISTINCT m.user_id)
        FROM tyt_transport_main t
        JOIN tyt_transport_main m ON t.similarity_code = m.similarity_code
        WHERE t.ctime BETWEEN #{startTime} AND #{endTime}
        GROUP BY t.similarity_code
        HAVING COUNT(*) > 1
    </select>

    <!-- 统计用户成交率 -->
    <select id="countUserDealRate" resultType="com.teyuntong.infra.task.service.biz.goods.goodsinfo.bean.UserTransportCountBean">
        SELECT
            COUNT(*) AS totalCount,
            COUNT(CASE WHEN status = 4 THEN 1 END) AS dealCount
        FROM tyt_transport_main
        WHERE user_id = #{userId}
        AND ctime BETWEEN #{startTime} AND #{endTime}
    </select>

    <!-- 返回未成交的秒抢货源id -->
    <select id="getValidSeckillGoodsId" resultType="long">
        select m.id
        from tyt_transport_main m
        join tyt_transport_main_extend e on m.id = e.src_msg_id
        where m.ctime between #{startTime} and #{endTime}
        and m.status = 1
        and e.seckill_goods = 1
    </select>
</mapper>
