<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.enterprise.mybatis.InvoiceThirdEnterpriseMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.enterprise.mybatis.entity.InvoiceThirdEnterpriseDO">
        <id column="id" property="id" />
        <result column="enterprise_name" property="enterpriseName" />
        <result column="enterprise_credit_code" property="enterpriseCreditCode" />
        <result column="enterprise_dominant_id" property="enterpriseDominantId" />
        <result column="service_provider_code" property="serviceProviderCode" />
        <result column="service_provider_name" property="serviceProviderName" />
        <result column="dominant_name" property="dominantName" />
        <result column="dominant_short_name" property="dominantShortName" />
        <result column="settlement_principal_id" property="settlementPrincipalId" />
        <result column="admin_name" property="adminName" />
        <result column="admin_phone" property="adminPhone" />
        <result column="finance_contacts_name" property="financeContactsName" />
        <result column="finance_contacts_phone" property="financeContactsPhone" />
        <result column="business_contacts_name" property="businessContactsName" />
        <result column="business_contacts_phone" property="businessContactsPhone" />
        <result column="tax_rate" property="taxRate" />
        <result column="using_flag" property="usingFlag" />
        <result column="sign_status" property="signStatus" />
        <result column="sign_fail_reason" property="signFailReason" />
        <result column="sign_time" property="signTime" />
        <result column="auth_status" property="authStatus" />
        <result column="auth_fail_reason" property="authFailReason" />
        <result column="auth_time" property="authTime" />
        <result column="sign_contract_status" property="signContractStatus" />
        <result column="sign_contract_fail_reason" property="signContractFailReason" />
        <result column="sign_contract_time" property="signContractTime" />
        <result column="open_account_status" property="openAccountStatus" />
        <result column="open_account_fail_reason" property="openAccountFailReason" />
        <result column="open_account_time" property="openAccountTime" />
        <result column="sign_user_id" property="signUserId" />
        <result column="sign_user_phone" property="signUserPhone" />
        <result column="third_user_code" property="thirdUserCode" />
        <result column="third_user_center_company_id" property="thirdUserCenterCompanyId" />
        <result column="contract_first_party_id" property="contractFirstPartyId" />
        <result column="contract_first_party_name" property="contractFirstPartyName" />
        <result column="contract_second_party_id" property="contractSecondPartyId" />
        <result column="contract_second_party_name" property="contractSecondPartyName" />
        <result column="contract_start_date" property="contractStartDate" />
        <result column="contract_end_date" property="contractEndDate" />
        <result column="contract_id" property="contractId" />
        <result column="contract_no" property="contractNo" />
        <result column="contract_url" property="contractUrl" />
        <result column="project_id" property="projectId" />
        <result column="project_name" property="projectName" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <select id="getContractSuccessList"
            resultType="com.teyuntong.infra.task.service.biz.user.enterprise.mybatis.entity.InvoiceThirdEnterpriseDO" parameterType="long">
        select id, auth_status authStatus, sign_contract_status signContractStatus
        from tyt_invoice_third_enterprise
        where sign_contract_status = 2 AND contract_end_date &lt; now()
        <if test="id != null">
            and id > #{id}
        </if>
        order by id limit 50
    </select>

    <select id="getAllSignSuccessList" resultMap="BaseResultMap">
        select *
        from tyt_invoice_third_enterprise
        where sign_status = 2
    </select>

    <select id="getByIdList" resultMap="BaseResultMap">
        select *
        from tyt_invoice_third_enterprise
        where id in
        <foreach collection="idList" open="(" close=")" item="id" separator=",">
            #{id}
        </foreach>
    </select>


    <update id="updateContractStatusFail">
        update tyt_invoice_third_enterprise
        set sign_contract_status = 3,
            sign_contract_fail_reason = '协议到期，请重新签署',
            sign_contract_time = null,
            sign_status = 3,
            sign_fail_reason = '协议到期，请重新签署',
            sign_time = null,
            modify_time = now()
        where id = #{id}
    </update>

    <update id="updateSignStatusFailByCode">
        update tyt_invoice_third_enterprise
        set sign_status = 3,
            sign_fail_reason = '企业认证驳回',
            sign_time = null,
            modify_time = now()
        where enterprise_credit_code = #{creditCode}
          and service_provider_code != 'XHL'
    </update>

    <select id="getThirdEnterpriseByTime"
            resultType="com.teyuntong.infra.task.service.biz.user.enterprise.pojo.CustomSignStatusBean">
        SELECT e.certigier_user_id userId,
               t.sign_status signStatus
        FROM tyt_invoice_third_enterprise t
                 LEFT JOIN tyt_invoice_enterprise e ON t.enterprise_id = e.id
        WHERE t.modify_time &gt; #{startTime} AND t.modify_time &lt;=#{endTime}
    </select>

</mapper>
