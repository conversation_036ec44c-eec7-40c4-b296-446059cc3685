<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.car.mybatis.mapper.ExecRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.car.mybatis.entity.ExecRecordDO">
        <id column="name" property="name" />
        <result column="number" property="number" />
        <result column="utime" property="utime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        name, number, utime
    </sql>
    <update id="updateBrowseNbr">
        update tyt_exec_record
        set number=#{nbr},
            utime=now()
        where name = #{name}
    </update>

    <update id="updateCollectNbr">
        update tyt_exec_record
        set number=#{nbr},
            utime=now()
        where name = #{name}
    </update>

</mapper>
