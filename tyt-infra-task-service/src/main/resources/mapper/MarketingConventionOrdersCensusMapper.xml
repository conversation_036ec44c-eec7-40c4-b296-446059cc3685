<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.market.activity.mybatis.mapper.MarketingConventionOrdersCensusMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.MarketingConventionOrdersCensusDO">
        <id column="id" property="id" />
        <result column="activity_id" property="activityId" />
        <result column="level" property="level" />
        <result column="stage" property="stage" />
        <result column="initial_prize" property="initialPrize" />
        <result column="initial_goods_id" property="initialGoodsId" />
        <result column="one_prize" property="onePrize" />
        <result column="one_goods_id" property="oneGoodsId" />
        <result column="two_prize" property="twoPrize" />
        <result column="two_goods_id" property="twoGoodsId" />
        <result column="three_prize" property="threePrize" />
        <result column="three_goods_id" property="threeGoodsId" />
        <result column="four_prize" property="fourPrize" />
        <result column="four_goods_id" property="fourGoodsId" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, activity_id, level, stage, initial_prize, initial_goods_id, one_prize, one_goods_id, two_prize, two_goods_id, three_prize, three_goods_id, four_prize, four_goods_id, create_time, modify_time
    </sql>

    <select id="getActivityLevel" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from marketing_convention_orders_census
        where activity_id = #{activityId}
        and level = #{rankLevel}
        and stage = #{stage}
        limit 1
    </select>
</mapper>
