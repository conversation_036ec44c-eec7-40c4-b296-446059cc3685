<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.market.activity.mybatis.mapper.AutomaticInformJobMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.AutomaticInformJobDO">
        <id column="id" property="id" />
        <result column="rule_id" property="ruleId" />
        <result column="job_type" property="jobType" />
        <result column="job_content" property="jobContent" />
        <result column="job_title" property="jobTitle" />
        <result column="job_time" property="jobTime" />
        <result column="job_status" property="jobStatus" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, rule_id, job_type, job_content, job_title, job_time, job_status, create_time, modify_time
    </sql>

    <select id="getJobByRuleIdAndTime" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
            from tyt_automatic_inform_job where rule_id=#{ruleId} and job_time=#{hour} and job_status=1
    </select>

</mapper>
