<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.teyuntong.infra.task.service.biz.user.info.mybatis.mapper.CsNewCustomMapper" >
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.info.mybatis.entity.CsNewCustomDO">
        <id column="id" property="id" />
        <result column="custom_name" property="customName" />
        <result column="custom_phone" property="customPhone" />
        <result column="custom_source" property="customSource" />
        <result column="register_status" property="registerStatus" />
        <result column="remark" property="remark" />
        <result column="modify_id" property="modifyId" />
        <result column="modify_name" property="modifyName" />
        <result column="ctime" property="ctime" />
        <result column="utime" property="utime" />
        <result column="status" property="status" />
        <result column="belong_to" property="belongTo" />
        <result column="is_recommend_user" property="isRecommendUser" />
        <result column="recommend_cell_phone" property="recommendCellPhone" />
        <result column="recommend_user_id" property="recommendUserId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, custom_name, custom_phone, custom_source, register_status, remark, modify_id, modify_name, ctime, utime, status, belong_to, is_recommend_user, recommend_cell_phone, recommend_user_id
    </sql>

    <select id="getNewCustomByPhone" resultMap="BaseResultMap">
        select * from cs_new_custom where custom_phone=#{phone} order by id desc limit 1;
    </select>

    <update id="updateCustomRegister">
        UPDATE `cs_new_custom` SET `register_status`=1,utime=now() WHERE `custom_phone`=#{phone}
    </update>
</mapper>