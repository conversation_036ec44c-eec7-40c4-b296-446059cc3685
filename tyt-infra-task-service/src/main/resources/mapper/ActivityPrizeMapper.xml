<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.market.activity.mybatis.mapper.ActivityPrizeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.ActivityPrizeDO">
        <id column="id" property="id" />
        <result column="prize_name" property="prizeName" />
        <result column="url" property="url" />
        <result column="code" property="code" />
        <result column="divide" property="divide" />
        <result column="status" property="status" />
        <result column="remark" property="remark" />
        <result column="operator" property="operator" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="pic_url" property="picUrl" />
        <result column="head_url" property="headUrl" />
        <result column="prize_url" property="prizeUrl" />
        <result column="top_url" property="topUrl" />
        <result column="money_value" property="moneyValue" />
        <result column="increase_money" property="increaseMoney" />
        <result column="good_id" property="goodId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, prize_name, url, code, divide, status, remark, operator, create_time, update_time, pic_url, head_url, prize_url, top_url, money_value, increase_money, good_id
    </sql>

    <select id="getByPrize" resultType="java.lang.Long">
        select good_id
        from tyt_activity_prize
        where id = #{prizeId}
        limit 1
    </select>
    <select id="getActivityGradeBeanByActivityId"
            resultType="com.teyuntong.infra.task.service.biz.market.activity.dto.ActivityGradeBean">
        select activity_id as activityId,grade,order_num as orderNum,stage,prize
        from tyt_activity_grade_prize
        where activity_id = #{activityId}
    </select>

</mapper>
