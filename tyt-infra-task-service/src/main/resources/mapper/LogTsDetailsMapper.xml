<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.goods.goodsinfo.mapper.LogTsDetailsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.goods.goodsinfo.entity.LogTsDetailsDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="ts_id" property="tsId" />
        <result column="client_version" property="clientVersion" />
        <result column="client_sign" property="clientSign" />
        <result column="status" property="status" />
        <result column="ctime" property="ctime" />
        <result column="view_source" property="viewSource" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, ts_id, client_version, client_sign, status, ctime, view_source
    </sql>

    <select id="selectViewUserList" resultType="java.lang.Long">
        select distinct user_id
        from tyt_log_ts_details
        where ctime > current_date and ts_id = #{srcMsgId}
    </select>

</mapper>
