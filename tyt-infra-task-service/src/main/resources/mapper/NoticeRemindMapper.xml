<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.trade.infofee.mapper.NoticeRemindMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.trade.infofee.entity.NoticeRemindDO">
        <id column="id" property="id" />
        <result column="type1" property="type1" />
        <result column="type2" property="type2" />
        <result column="priority" property="priority" />
        <result column="is_popup" property="isPopup" />
        <result column="content" property="content" />
        <result column="msg_id" property="msgId" />
        <result column="production_id" property="productionId" />
        <result column="receive_id" property="receiveId" />
        <result column="ctime" property="ctime" />
        <result column="receive_time" property="receiveTime" />
        <result column="receive_status" property="receiveStatus" />
        <result column="status" property="status" />
        <result column="utime" property="utime" />
        <result column="order_id" property="orderId" />
        <result column="version_type" property="versionType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, type1, type2, priority, is_popup, content, msg_id, production_id, receive_id, ctime, receive_time, receive_status, status, utime, order_id, version_type
    </sql>

</mapper>
