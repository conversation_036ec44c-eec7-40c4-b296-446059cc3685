<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.limit.mybatis.mapper.TytExposureBlockMapper">
  <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.limit.mybatis.entity.TytExposureBlock">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="exposure_permission_num" jdbcType="INTEGER" property="exposurePermissionNum" />
    <result column="block_status" jdbcType="INTEGER" property="blockStatus" />
    <result column="permanent_block" jdbcType="INTEGER" property="permanentBlock" />
    <result column="delete_flag" jdbcType="INTEGER" property="deleteFlag" />
    <result column="operate_user_id" jdbcType="BIGINT" property="operateUserId" />
    <result column="operate_user_name" jdbcType="VARCHAR" property="operateUserName" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="block_begin_time" jdbcType="TIMESTAMP" property="blockBeginTime" />
    <result column="block_end_time" jdbcType="TIMESTAMP" property="blockEndTime" />
    <result column="sms_notify" jdbcType="INTEGER" property="smsNotify" />
  </resultMap>

  <select id="selectList" resultMap="BaseResultMap">
      select *
      from tyt_exposure_block
      where delete_flag = 0
        and id > #{indexId}
      order by id
      limit 100
  </select>

  <select id="selectDelayList" resultMap="BaseResultMap">
    select *
    from tyt_exposure_block
    where block_status = 2
      and delete_flag = 0
      and block_begin_time &lt; now()
      and id > #{indexId}
    order by id
      limit 100
  </select>

  <select id="selectReleaseList" resultMap="BaseResultMap">
    select *
    from tyt_exposure_block
    where block_status = 1
      and delete_flag = 0
      and block_end_time &lt; now()
      and id > #{indexId}
    order by id
      limit 100
  </select>

</mapper>