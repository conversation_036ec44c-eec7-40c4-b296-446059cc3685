<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.permission.mybatis.mapper.ExposurePermissionGainRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="com.teyuntong.infra.task.service.biz.user.permission.mybatis.entity.ExposurePermissionGainRecordDO">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="gain_type" property="gainType"/>
        <result column="ord_num" property="ordNum"/>
        <result column="goods_id" property="goodsId"/>
        <result column="goods_name" property="goodsName"/>
        <result column="expired_time" property="expiredTime"/>
        <result column="total_num" property="totalNum"/>
        <result column="send_type" property="sendType"/>
        <result column="activity_name" property="activityName"/>
        <result column="activity_id" property="activityId"/>
        <result column="remark" property="remark"/>
        <result column="ctime" property="ctime"/>
    </resultMap>
    <select id="selectTransportGiveExposure" resultMap="BaseResultMap">
        SELECT user_id  AS userId,
               (SELECT count(id)
                FROM interests_grant_records
                WHERE user_id = userId
                  AND ctime >= #{startTime,jdbcType=TIMESTAMP}
                  AND ctime &lt;= #{endTime,jdbcType=TIMESTAMP}
                  AND `status` = 0
                  AND enabled = 1
                  AND grant_counts > 0) AS publishNum
        FROM interests_grant_records
        WHERE ctime >= #{startTime,jdbcType=TIMESTAMP}
          AND ctime &lt;= #{endTime,jdbcType=TIMESTAMP}
          AND `status` = 0
          AND enabled = 1
          AND grant_counts > 0
        GROUP BY user_id

    </select>
    <select id="queryExpiredUserIdList" resultType="java.lang.Long">
        select user_id as userId
        from exposure_permission_gain_record
        where expired_time >= #{yesterday}
          and expired_time &lt;= #{today}
        group by user_id
    </select>
    <select id="queryExtraTotalNum" resultType="java.lang.Integer">
        select sum(total_num)
        from exposure_permission_gain_record
        where user_id = #{userId} and expired_time > #{today}
    </select>

    <select id="selectPriceGoodsGiveNum" resultType="com.teyuntong.infra.task.service.biz.user.permission.mybatis.entity.TransportGiveExposureDto">
        SELECT
            user_id AS userId,
            ( SELECT SUM( total_num ) FROM exposure_permission_gain_record WHERE user_id = userId AND gain_type = #{gainType,jdbcType=INTEGER} AND ctime >= #{startTime,jdbcType=TIMESTAMP} AND ctime &lt;= #{endTime,jdbcType=TIMESTAMP} ) AS giveNum
        FROM
            exposure_permission_gain_record
        WHERE
            gain_type = #{gainType}
          AND ctime >= #{startTime}
          AND ctime &lt;= #{endTime}
        GROUP BY
            user_id
    </select>


</mapper>
