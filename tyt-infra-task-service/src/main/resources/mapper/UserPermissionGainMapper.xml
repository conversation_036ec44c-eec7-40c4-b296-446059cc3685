<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.permission.mybatis.mapper.UserPermissionGainMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.permission.mybatis.entity.UserPermissionGainDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="service_permission_type_id" property="servicePermissionTypeId" />
        <result column="service_permission_type_name" property="servicePermissionTypeName" />
        <result column="send_type" property="sendType" />
        <result column="gain_type" property="gainType" />
        <result column="buy_goods_id" property="buyGoodsId" />
        <result column="goods_id" property="goodsId" />
        <result column="goods_name" property="goodsName" />
        <result column="expired_time" property="expiredTime" />
        <result column="gain_count" property="gainCount" />
        <result column="use_count" property="useCount" />
        <result column="activity_id" property="activityId" />
        <result column="activity_name" property="activityName" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, service_permission_type_id, service_permission_type_name, send_type, gain_type, buy_goods_id, goods_id, goods_name, expired_time, gain_count, use_count, activity_id, activity_name, remark, create_time, modify_time
    </sql>

    <select id="getExpirePermissionGainList" resultMap="BaseResultMap">
        SELECT * FROM `tyt_user_permission_gain`
        where
        id > #{markId}
        and gain_type = #{gainType}
        and goods_id in (37, 11)
        and expired_time >= #{startTime}
        and expired_time &lt;= #{endTime}
        and use_count &lt; gain_count
        order by id
        limit #{pageSize}
    </select>
    <select id="queryExpiredUserIdList" resultType="java.lang.Long">
        select user_id userId
        from tyt_user_permission_gain
        where user_id >#{startUserId}
        and service_permission_type_id = #{servicePermissionTypeId}
        and expired_time &lt; CURDATE()
        and expired_time >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
        and use_count &lt; gain_count
        order by user_id
        limit 200
    </select>
    <select id="queryExtraTotalNum" resultType="java.lang.Integer">
        select IFNULL(sum(gain_count-use_count),0) as extraTotalNum
        from tyt_user_permission_gain
        where user_id = #{userId}
          and service_permission_type_id = #{servicePermissionTypeId}
          and expired_time >= CURDATE();
    </select>

</mapper>
