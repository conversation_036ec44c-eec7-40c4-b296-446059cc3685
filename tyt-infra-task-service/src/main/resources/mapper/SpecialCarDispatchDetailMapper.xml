<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.dispatch.mapper.SpecialCarDispatchDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.dispatch.entity.SpecialCarDispatchDetailDO">
        <id column="id" property="id" />
        <result column="dispatch_id" property="dispatchId" />
        <result column="ts_id" property="tsId" />
        <result column="user_id" property="userId" />
        <result column="pay_link_phone" property="payLinkPhone" />
        <result column="driver_id" property="driverId" />
        <result column="location" property="location" />
        <result column="distance" property="distance" />
        <result column="car_info_id" property="carInfoId" />
        <result column="priority" property="priority" />
        <result column="dispatch_status" property="dispatchStatus" />
        <result column="dispatch_error_msg" property="dispatchErrorMsg" />
        <result column="dispatch_time" property="dispatchTime" />
        <result column="accept_status" property="acceptStatus" />
        <result column="accept_time" property="acceptTime" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
        <result column="manual_assign" property="manualAssign" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, dispatch_id, ts_id, user_id, pay_link_phone, driver_id, location, distance, car_info_id, priority, dispatch_status, dispatch_error_msg, dispatch_time, accept_status, accept_time, create_time, modify_time, manual_assign
    </sql>

	<select id="getNextDispatchDetail" resultMap="BaseResultMap">
        select *
        from tyt_special_car_dispatch_detail
        where dispatch_id = #{dispatchId}
        and dispatch_status = 0
        and accept_status = 0
        and manual_assign = 0
        order by priority asc
        limit 1
    </select>

</mapper>
