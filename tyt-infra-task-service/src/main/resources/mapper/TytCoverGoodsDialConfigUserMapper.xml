<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.car.mybatis.mapper.TytCoverGoodsDialConfigUserMapper">
  <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.car.mybatis.entity.TytCoverGoodsDialConfigUser">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="operate_user_id" jdbcType="BIGINT" property="operateUserId" />
    <result column="operate_user_name" jdbcType="VARCHAR" property="operateUserName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="dial_config_id" jdbcType="BIGINT" property="dialConfigId" />
  </resultMap>

  <select id="selectEnabledLastImported" resultMap="BaseResultMap">
      select confUser.update_time
      from tyt_cover_goods_dial_config_user confUser
               left join tyt_cover_goods_dial_config conf on confUser.dial_config_id = conf.id
      where conf.enable = 1
        and confUser.user_id = #{userId}
        and conf.del_flag = 0
        and config_type = 1
      order by confUser.update_time DESC
      limit 1
  </select>



</mapper>