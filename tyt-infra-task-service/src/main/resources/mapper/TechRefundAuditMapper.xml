<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.trade.techfee.mapper.TechRefundAuditMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.trade.techfee.entity.TechRefundAuditDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="user_name" property="userName" />
        <result column="user_cell_phone" property="userCellPhone" />
        <result column="ex_id" property="exId" />
        <result column="apply_time" property="applyTime" />
        <result column="ts_order_no" property="tsOrderNo" />
        <result column="pub_cell_phone" property="pubCellPhone" />
        <result column="pay_cell_phone" property="payCellPhone" />
        <result column="refund_target" property="refundTarget" />
        <result column="tec_service_fee" property="tecServiceFee" />
        <result column="refund_amount" property="refundAmount" />
        <result column="audit_status" property="auditStatus" />
        <result column="audit_stage" property="auditStage" />
        <result column="final_audit_status" property="finalAuditStatus" />
        <result column="remark" property="remark" />
        <result column="audit_user_id" property="auditUserId" />
        <result column="audit_user_name" property="auditUserName" />
        <result column="audit_cell_phone" property="auditCellPhone" />
        <result column="audit_time" property="auditTime" />
        <result column="thirdparty_platform_type" property="thirdpartyPlatformType" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, user_name, user_cell_phone, ex_id, apply_time, ts_order_no, pub_cell_phone, pay_cell_phone, refund_target, tec_service_fee, refund_amount, audit_status, audit_stage, final_audit_status, remark, audit_user_id, audit_user_name, audit_cell_phone, audit_time, thirdparty_platform_type, create_time, modify_time
    </sql>

    <select id="getTechRefundAuditByUserIdAndTsOrderNo" resultMap="BaseResultMap">
        select * from tyt_tech_refund_audit where user_id=#{userId} and ts_order_no=#{tsOrderNo} and final_audit_status in (1,2,3,4) order by id desc limit 1
    </select>
</mapper>
