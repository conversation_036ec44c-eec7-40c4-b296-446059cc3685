<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.limit.mybatis.mapper.SttLimitMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.limit.mybatis.entity.SttLimitDO">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="code" property="code"/>
        <result column="user_sign" property="userSign"/>
        <result column="type" property="type"/>
        <result column="value" property="value"/>
        <result column="warn_number" property="warnNumber"/>
        <result column="ctime" property="ctime"/>
        <result column="utime" property="utime"/>
        <result column="status" property="status"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , name, code, user_sign, type, value, warn_number, ctime, utime, status
    </sql>
    <select id="getSttLimitList"
            resultType="com.teyuntong.infra.task.service.biz.user.limit.mybatis.entity.SttLimitDO">
        select * from tyt_stt_limit t where t.status= 0  order by id
    </select>

</mapper>
