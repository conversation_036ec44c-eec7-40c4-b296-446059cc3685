<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.abtest.mapper.AbtestConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.abtest.entity.AbtestConfigDO">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="code" property="code" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
        <result column="enable" property="enable" />
        <result column="rule_type" property="ruleType" />
        <result column="default_type" property="defaultType" />
        <result column="modify_employee_id" property="modifyEmployeeId" />
        <result column="is_do_import_user" property="isDoImportUser" />
        <result column="create_name" property="createName" />
        <result column="modify_name" property="modifyName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, code, remark, create_time, modify_time, enable, rule_type, default_type, modify_employee_id, is_do_import_user, create_name, modify_name
    </sql>

    <select id="getByIds" resultMap="BaseResultMap">
        select * from tyt_abtest_config where id in
        <foreach collection="idList" open="(" close=")" item="id" separator=",">
            #{id}
        </foreach>
        and enable = 1 and rule_type = 1
    </select>

    <select id="getByCode" resultMap="BaseResultMap">
        select *
        from tyt_abtest_config
        where code = #{code}
    </select>

</mapper>
