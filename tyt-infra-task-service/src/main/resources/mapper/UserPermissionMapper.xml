<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.permission.mybatis.mapper.UserPermissionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.permission.mybatis.entity.UserPermissionDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="service_permission_id" property="servicePermissionId" />
        <result column="permission_sort_type" property="permissionSortType" />
        <result column="sub_type" property="subType" />
        <result column="service_permission_type_id" property="servicePermissionTypeId" />
        <result column="service_permission_type_name" property="servicePermissionTypeName" />
        <result column="priority" property="priority" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="status" property="status" />
        <result column="total_num" property="totalNum" />
        <result column="used_num" property="usedNum" />
        <result column="ctime" property="ctime" />
        <result column="utime" property="utime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, service_permission_id, permission_sort_type, sub_type, service_permission_type_id, service_permission_type_name, priority, start_time, end_time, status, total_num, used_num, ctime, utime
    </sql>

    <select id="getVipExpireUser" resultType="java.lang.Long">
        select user_id from tyt_user_permission where status = 3
        <if test="null != blackUsers">
        and user_id not in (
        <foreach collection="blackUsers" item="item" separator=",">
            #{item}
        </foreach>
            )</if>
        <if test="port == 1">
            and service_permission_type_id='100101'
        </if>
        <if test="port == 2">
            and service_permission_type_id='100201'
        </if>
        and end_time BETWEEN #{startTime,jdbcType=VARCHAR} and #{endTime,jdbcType=VARCHAR}
        and user_id &gt; #{maxUserId} order by user_id asc limit #{pageSize}
    </select>

    <select id="getVipByUserId" resultType="java.lang.Long">
        select user_id from tyt_user_permission where user_id = #{userId}
        <if test="port == 1">
            and service_permission_type_id='100101'
        </if>
        <if test="port == 2">
            and service_permission_type_id='100201'
        </if>
    </select>

    <select id="getPermission" resultMap="BaseResultMap">
        select * from tyt_user_permission
        where user_id = #{userId}
        and service_permission_type_id = #{permissionTypeId}
        and status = 1
    </select>
    <select id="queryInvalidIDList" resultType="java.lang.Long">
        SELECT id
        FROM tyt_user_permission
        where status = 1
          and end_time >= #{yesterday} and end_time &lt;= #{today}
    </select>
    <select id="getExpireUserIds" resultType="java.lang.Long">
        select user_id
        from tyt_user_permission
        where id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
         group by user_id
    </select>
    <select id="getUserIds" resultType="java.lang.Long">
        SELECT `user_id` userId
        FROM `tyt_user_permission`
        WHERE service_permission_type_id = #{permissionTypeId}
          AND `status` = 1
          AND `end_time` > #{startTime}
          AND `end_time` &lt; #{endTime}
    </select>
    <select id="getEffectPermissionByUserIds" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM `tyt_user_permission`
        WHERE service_permission_type_id = #{permissionTypeId}
          AND `status`=1
          AND `end_time`>#{endTime}
          AND `user_id` IN
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
          AND `total_num` > `used_num`
    </select>

    <select id="getVipExpireUsersCount" resultType="java.lang.Integer" parameterType="com.teyuntong.infra.task.service.biz.user.permission.pojo.PermissionBean">
    SELECT count(*) FROM `tyt_user_permission`
                    WHERE `service_permission_id`=#{permissionId}
                    AND `sub_type`= #{permissionType}
                    AND `status`=1
                    AND `start_time` &lt; #{startTime} AND `end_time`between #{startTime} and #{endTime}
    </select>
  <select id="getExpireUsers" parameterType="com.teyuntong.infra.task.service.biz.user.permission.pojo.PermissionBean" resultMap="BaseResultMap">
      SELECT * FROM `tyt_user_permission`
              WHERE `service_permission_id`=#{permissionId} AND `sub_type`=#{permissionType} AND `status`=1 AND `start_time` &lt; #{startTime} AND `end_time`between #{startTime} AND #{endTime}
  </select>

    <select id="getCountExpireUsersNbr"  resultType="java.lang.Integer" parameterType="com.teyuntong.infra.task.service.biz.user.permission.pojo.PermissionBean">
     SELECT count(*) FROM `tyt_user_permission`
           WHERE `service_permission_id`= #{permissionId} AND `permission_sort_type`=2 AND `status`=1 AND `start_time` &lt; #{startTime} AND `end_time`between #{startTime}
           AND #{endTime} AND `used_num` &lt; `total_num` AND user_id  NOT IN
           (SELECT user_id FROM `tyt_user_permission` WHERE `service_permission_id`=#{permissionId} AND `permission_sort_type`=1 AND `status`=1 AND `end_time`between #{startTime}
        AND #{endTime})
    </select>
    <select id="getCountExpireUsers" parameterType="com.teyuntong.infra.task.service.biz.user.permission.pojo.PermissionBean" resultMap="BaseResultMap">
    SELECT * FROM `tyt_user_permission`
        WHERE `service_permission_id`=#{permissionId} AND `permission_sort_type`=2 AND `status`=1 AND `start_time`&lt; #{startTime}
        AND `end_time`between #{startTime} AND #{endTime} AND `used_num` &lt; `total_num` AND user_id NOT IN
           (SELECT user_id FROM `tyt_user_permission` WHERE `service_permission_id`=#{permissionId} AND `permission_sort_type`=1 AND `status`=1 AND `end_time` between #{startTime} AND #{endTime} )
    </select>
    <update id="updateInvalidVipStatus">
        update tyt_user_permission
        set status = 3
        where status = 1
          and end_time >= #{yesterday}
          and end_time &lt;= #{today}
          and service_permission_type_id in ('100101','100201')
    </update>

    <insert id="addUserPermissionLog">
        insert into tyt_user_permission_log (user_id,service_permission_id,permission_sort_type,service_permission_type_id,service_permission_type_name,priority,start_time,end_time,status,total_num,used_num,change_type
                ) select user_id,service_permission_id,permission_sort_type,service_permission_type_id,service_permission_type_name,priority,start_time,end_time,status,total_num,used_num, #{changeType}
                  from tyt_user_permission where id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </insert>

    <select id="getUserPermissionByCondition" resultMap="BaseResultMap">
        select * from tyt_user_permission
        where status = #{userPermissionUsedUpStatus}
          and user_id = #{minUserId}
          and utime &gt;= #{startTime}
          and utime &lt;= #{endTime}
        order by user_id asc limit #{pageSize}
    </select>

    <update id="updateInvalidForSmallMeal">
        update tyt_user_permission
        set status = 2
        where service_permission_type_id = '100103'
        and end_time &lt; NOW()
        and end_time >= DATE_SUB(NOW(), INTERVAL 1 DAY)
        and status = 1
    </update>
</mapper>
