<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.info.mybatis.mapper.CsMaintainerConfigSubMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.info.mybatis.entity.CsMaintainerConfigSubDO">
        <id column="id" property="id" />
        <result column="config_id" property="configId" />
        <result column="register_identity" property="registerIdentity" />
        <result column="participants" property="participants" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, config_id, register_identity, participants, status, create_time, modify_time
    </sql>

    <select id="getByConfigId" resultMap="BaseResultMap">
        select * from cs_maintainer_config_sub where config_id = #{configId} and status = 1
    </select>

</mapper>
