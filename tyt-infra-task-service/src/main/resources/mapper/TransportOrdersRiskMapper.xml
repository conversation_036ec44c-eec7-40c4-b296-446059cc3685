<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.trade.infofee.mapper.TransportOrdersRiskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.trade.infofee.entity.TransportOrdersRiskDO">
        <id column="id" property="id" />
        <result column="marketing_activity_id" property="marketingActivityId" />
        <result column="activity_name" property="activityName" />
        <result column="activity_type" property="activityType" />
        <result column="user_id" property="userId" />
        <result column="user_account" property="userAccount" />
        <result column="order_status" property="orderStatus" />
        <result column="order_number" property="orderNumber" />
        <result column="ts_order_no" property="tsOrderNo" />
        <result column="activity_status" property="activityStatus" />
        <result column="donate_days" property="donateDays" />
        <result column="repeal_days" property="repealDays" />
        <result column="update_user_name" property="updateUserName" />
        <result column="suspect_type" property="suspectType" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="remark" property="remark" />
        <result column="goods_order_id" property="goodsOrderId" />
        <result column="task_flag" property="taskFlag" />
        <result column="car_user_id" property="carUserId" />
        <result column="car_user_account" property="carUserAccount" />
        <result column="user_flag" property="userFlag" />
        <result column="appeal_status" property="appealStatus" />
        <result column="appeal_evidence" property="appealEvidence" />
        <result column="reason" property="reason" />
        <result column="reject_type" property="rejectType" />
        <result column="good_status" property="goodStatus" />
        <result column="car_status" property="carStatus" />
        <result column="before_type" property="beforeType" />
        <result column="pay_end_time" property="payEndTime" />
        <result column="track_decide" property="trackDecide" />
        <result column="excellent_goods" property="excellentGoods" />
        <result column="user_flag_remark" property="userFlagRemark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, marketing_activity_id, activity_name, activity_type, user_id, user_account, order_status, order_number, ts_order_no, activity_status, donate_days, repeal_days, update_user_name, suspect_type, create_time, update_time, remark, goods_order_id, task_flag, car_user_id, car_user_account, user_flag, appeal_status, appeal_evidence, reason, reject_type, good_status, car_status, before_type, pay_end_time, track_decide, excellent_goods, user_flag_remark
    </sql>

    <select id="getHonourOrderCount" resultType="java.lang.Integer">
        select count(*) from tyt_transport_orders_risk where pay_end_time >= #{startTime} and create_time &lt;= #{endTime} and (activity_status = 0 or activity_status = 3) and user_id = #{userId}
    </select>
    
</mapper>
