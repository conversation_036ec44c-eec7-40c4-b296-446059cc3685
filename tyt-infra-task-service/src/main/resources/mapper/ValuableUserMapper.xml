<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.goods.valuable.mapper.ValuableUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.goods.valuable.entity.ValuableUserDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="user_name" property="userName" />
        <result column="op_user_id" property="opUserId" />
        <result column="op_user_name" property="opUserName" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
        <result column="del_status" property="delStatus" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, user_name, op_user_id, op_user_name, create_time, modify_time, del_status
    </sql>

    <select id="selectValuableUserIdList" resultType="java.lang.Long">
        select user_id as userId
        from tyt_valuable_user
        where del_status = 0 and user_id in
        <foreach collection="userIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

</mapper>
