<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.car.mybatis.mapper.ScarJobMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.car.mybatis.entity.ScarJobDO">
        <id column="id" property="id" />
        <result column="sort_id" property="sortId" />
        <result column="title" property="title" />
        <result column="tel_name" property="telName" />
        <result column="telephone" property="telephone" />
        <result column="age" property="age" />
        <result column="years" property="years" />
        <result column="salary_code" property="salaryCode" />
        <result column="salary" property="salary" />
        <result column="device_type_one_code" property="deviceTypeOneCode" />
        <result column="device_type_one" property="deviceTypeOne" />
        <result column="device_type_two_code" property="deviceTypeTwoCode" />
        <result column="device_type_two" property="deviceTypeTwo" />
        <result column="format_code" property="formatCode" />
        <result column="format" property="format" />
        <result column="province_code" property="provinceCode" />
        <result column="province" property="province" />
        <result column="city_code" property="cityCode" />
        <result column="city" property="city" />
        <result column="county_code" property="countyCode" />
        <result column="county" property="county" />
        <result column="remark" property="remark" />
        <result column="publish_time" property="publishTime" />
        <result column="status" property="status" />
        <result column="cell_phone" property="cellPhone" />
        <result column="user_id" property="userId" />
        <result column="md5" property="md5" />
        <result column="read_nbr" property="readNbr" />
        <result column="utime" property="utime" />
        <result column="ctime" property="ctime" />
        <result column="client_sign" property="clientSign" />
        <result column="client_version" property="clientVersion" />
        <result column="resend_counts" property="resendCounts" />
        <result column="browse_nbr" property="browseNbr" />
        <result column="collection_nbr" property="collectionNbr" />
        <result column="display_status" property="displayStatus" />
        <result column="del_status" property="delStatus" />
        <result column="del_user_id" property="delUserId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, sort_id, title, tel_name, telephone, age, years, salary_code, salary, device_type_one_code, device_type_one, device_type_two_code, device_type_two, format_code, format, province_code, province, city_code, city, county_code, county, remark, publish_time, status, cell_phone, user_id, md5, read_nbr, utime, ctime, client_sign, client_version, resend_counts, browse_nbr, collection_nbr, display_status, del_status, del_user_id
    </sql>
    <update id="updateBrowseNbr">
        update tyt_bcar_job
        set browse_nbr=(browse_nbr + #{browseLogDto.cc}),
            utime=now()
        where id = #{browseLogDto.msgId}
    </update>

    <update id="updateCollectNbr">
        update tyt_bcar_recruit
        set collection_nbr=CASE
                               WHEN (collection_nbr + #{collectInfoDto.ac} - #{collectInfoDto.dc}) &lt; 0 THEN 0
                               ELSE (collection_nbr + #{collectInfoDto.ac} - #{collectInfoDto.dc}) END,
            utime=now()
        where id = #{collectInfoDto.msgId}
    </update>


    <select id="selectNeedUpdateList" resultMap="BaseResultMap">
        select *
        from tyt_scar_job
        where status = #{status} and read_nbr = #{readNbr}  and  ctime>= #{start}  and ctime &lt; #{end}
    </select>

</mapper>
