<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.trade.monitor.mapper.MonitorTaskFailRecordMapper">

  <select id="getFailRecordList" resultType="com.teyuntong.infra.task.service.biz.trade.monitor.entity.MonitorTaskFailRecord">
      SELECT
          id id,
          monitor_item monitorItem,
          table_name tableName,
          fail_record_id failRecordId,
          fail_reason failReason,
          ctime ctime,
          mtime,
          status
      FROM
          monitor_task_fail_record
      WHERE
          status = 0
        AND monitor_item = #{monitorItem}
        AND id > #{maxServiceId}
      order by id asc limit 10
  </select>

    <update id="updateProcessedRecord" parameterType="java.lang.Long">
        update monitor_task_fail_record set status = 1 where id=#{id}
    </update>
</mapper>
