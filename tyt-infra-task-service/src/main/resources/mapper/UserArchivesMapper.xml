<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.info.mybatis.mapper.UserArchivesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.info.mybatis.entity.UserArchivesDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="user_nickname" property="userNickname" />
        <result column="user_cell_phone" property="userCellPhone" />
        <result column="user_true_name" property="userTrueName" />
        <result column="identity" property="identity" />
        <result column="register_time" property="registerTime" />
        <result column="identity_auth" property="identityAuth" />
        <result column="car_auth" property="carAuth" />
        <result column="deliver_type_one" property="deliverTypeOne" />
        <result column="last_login_time" property="lastLoginTime" />
        <result column="car_vip_label" property="carVipLabel" />
        <result column="carvip_due_date" property="carvipDueDate" />
        <result column="good_vip_label" property="goodVipLabel" />
        <result column="goodvip_due_date" property="goodvipDueDate" />
        <result column="deliver_goods_label" property="deliverGoodsLabel" />
        <result column="deliver_goods_due_date" property="deliverGoodsDueDate" />
        <result column="carvip_first_pay_date" property="carvipFirstPayDate" />
        <result column="carvip_last_pay_date" property="carvipLastPayDate" />
        <result column="carvip_pay_times" property="carvipPayTimes" />
        <result column="goodvip_first_pay_date" property="goodvipFirstPayDate" />
        <result column="goodvip_last_pay_date" property="goodvipLastPayDate" />
        <result column="goodvip_pay_times" property="goodvipPayTimes" />
        <result column="deliver_goods_first_pay_date" property="deliverGoodsFirstPayDate" />
        <result column="deliver_goods_last_pay_date" property="deliverGoodsLastPayDate" />
        <result column="deliver_goods_pay_times" property="deliverGoodsPayTimes" />
        <result column="fist_send_goods_date" property="fistSendGoodsDate" />
        <result column="last_send_goods_date" property="lastSendGoodsDate" />
        <result column="first_call_phone_date" property="firstCallPhoneDate" />
        <result column="last_call_phone_date" property="lastCallPhoneDate" />
        <result column="ctime" property="ctime" />
        <result column="utime" property="utime" />
        <result column="goods_order_no" property="goodsOrderNo" />
        <result column="car_order_no" property="carOrderNo" />
        <result column="good_goods_order_no" property="goodGoodsOrderNo" />
        <result column="plat_publish_no" property="platPublishNo" />
        <result column="car_favorable_rate" property="carFavorableRate" />
        <result column="goods_favorable_rate" property="goodsFavorableRate" />
        <result column="car_last_login_time" property="carLastLoginTime" />
        <result column="goods_last_login_time" property="goodsLastLoginTime" />
        <result column="client_sign" property="clientSign" />
        <result column="province" property="province" />
        <result column="city" property="city" />
        <result column="agent_publish_count" property="agentPublishCount" />
        <result column="during_count" property="duringCount" />
        <result column="bought_vip" property="boughtVip" />
        <result column="pay_infee_last_time" property="payInfeeLastTime" />
        <result column="plat_id" property="platId" />
    </resultMap>

    <resultMap id="UserInformResultMap" type="com.teyuntong.infra.task.service.biz.market.activity.dto.UserInformBean">
        <id column="user_id" jdbcType="BIGINT" property="userId" />
        <result column="user_cell_phone" jdbcType="VARCHAR" property="cellPhone" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, user_nickname, user_cell_phone, user_true_name, identity, register_time, identity_auth, car_auth, deliver_type_one, last_login_time, car_vip_label, carvip_due_date, good_vip_label, goodvip_due_date, deliver_goods_label, deliver_goods_due_date, carvip_first_pay_date, carvip_last_pay_date, carvip_pay_times, goodvip_first_pay_date, goodvip_last_pay_date, goodvip_pay_times, deliver_goods_first_pay_date, deliver_goods_last_pay_date, deliver_goods_pay_times, fist_send_goods_date, last_send_goods_date, first_call_phone_date, last_call_phone_date, ctime, utime, goods_order_no, car_order_no, good_goods_order_no, plat_publish_no, car_favorable_rate, goods_favorable_rate, car_last_login_time, goods_last_login_time, client_sign, province, city, agent_publish_count, during_count, bought_vip, pay_infee_last_time, plat_id
    </sql>

    <select id="getNeedInformUsers" resultMap="UserInformResultMap">
        select  user_id, user_cell_phone from tyt_user_archives where 1=1
        <if test="port==1">
            and last_call_phone_date BETWEEN #{startTime,jdbcType=VARCHAR} and #{endTime,jdbcType=VARCHAR}
            and pay_infee_last_time BETWEEN #{startTime,jdbcType=VARCHAR} and #{endTime,jdbcType=VARCHAR}
        </if>
        <if test="port==2">
            and last_send_goods_date BETWEEN #{startTime,jdbcType=VARCHAR} and #{endTime,jdbcType=VARCHAR}
        </if>
        <if test="null != blackUsers">
            and user_id not in (
            <foreach collection="blackUsers" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="null != blackArea">
            and city not in(
            <foreach collection="blackArea" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        and user_id &gt; #{maxUserId} order by user_id asc limit #{pageSize}
    </select>

    <select id="getUsersByRegister" resultMap="UserInformResultMap">
        select  user_id, user_cell_phone from tyt_user_archives where
         register_time BETWEEN #{startTime,jdbcType=VARCHAR} and #{endTime,jdbcType=VARCHAR}
        <if test="port==1">
            and plat_id in (21,31) and carvip_pay_times=0
        </if>
        <if test="port==2">
            and plat_id in (22,32) and goodvip_pay_times=0
        </if>
        <if test="null != blackUsers">
            and user_id not in (
            <foreach collection="blackUsers" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="null != blackArea">
            and city not in(
            <foreach collection="blackArea" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        and user_id &gt; #{maxUserId} order by user_id asc limit #{pageSize}
    </select>
    <select id="getByUserId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tyt_user_archives
        where user_id = #{userId}
    </select>

    <update id="updateFistAndLastSendGoodsDate" parameterType="com.teyuntong.infra.task.service.biz.user.info.mybatis.entity.UserArchivesDO">
        UPDATE tyt_user_archives
        SET fist_send_goods_date = (CASE WHEN fist_send_goods_date IS NULL THEN #{fistSendGoodsDate} ELSE fist_send_goods_date END),
            last_send_goods_date = #{lastSendGoodsDate}
        WHERE user_id = #{userId}
    </update>


    <update id="updateCarVipLabel">
        UPDATE tyt_user_archives a
        SET a.car_vip_label = (CASE
        WHEN a.car_vip_label = '免费车体验' THEN '车体验过期'
        WHEN a.car_vip_label = '免费车试用' THEN '车试用过期'
        WHEN a.car_vip_label = '车会员' THEN '车会员过期'
        ELSE a.car_vip_label
        END)
        WHERE a.car_vip_label IN ('免费车体验', '免费车试用', '车会员')
        AND a.carvip_due_date <![CDATA[<]]> #{dueDate}
    </update>


    <update id="updateGoodVipLabel">
        UPDATE tyt_user_archives a
        SET a.good_vip_label = (CASE
        WHEN a.good_vip_label = '免费货体验' THEN '货体验过期'
        WHEN a.good_vip_label = '免费货试用' THEN '货试用过期'
        WHEN a.good_vip_label = '货会员' THEN '货会员过期'
        ELSE a.good_vip_label
        END)
        WHERE a.good_vip_label IN ('免费货体验', '免费货试用', '货会员')
        AND a.goodvip_due_date <![CDATA[<]]> #{dueDate}
    </update>


    <update id="updateDeliverGoodsLabel">
        UPDATE tyt_user_archives a
        SET a.deliver_goods_label = '货次卡过期'
        WHERE a.deliver_goods_label = '货次卡有效'
        AND a.deliver_goods_due_date <![CDATA[<]]> #{dueDate}
    </update>


    <update id="updatePayInfeeLastTime">
        UPDATE tyt_user_archives
        SET pay_infee_last_time = #{payInfeeLastTime}
        WHERE user_id = #{userId}
    </update>

    <update id="updateUserPublishOrderNo">
        UPDATE tyt_user_archives
        SET plat_publish_no = plat_publish_no + #{publishNo},
            car_last_login_time = #{carLastLoginTime},
            goods_last_login_time = #{goodsLastLoginTime}
        WHERE user_id = #{userId}
    </update>

    <update id="updateUserGoodsOrderNo">
        UPDATE tyt_user_archives
        SET goods_order_no = goods_order_no + #{goodsOrderNo},
            car_last_login_time = #{carLastLoginTime},
            goods_last_login_time = #{goodsLastLoginTime}
        WHERE user_id = #{userId}
    </update>


    <update id="updateUserCarOrderNo">
        UPDATE tyt_user_archives
        SET car_order_no = car_order_no + #{carOrderNo},
            car_last_login_time = #{carLastLoginTime},
            goods_last_login_time = #{goodsLastLoginTime}
        WHERE user_id = #{userId}
    </update>


</mapper>
