<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.goods.addresslevel.mapper.GoodsAddressLevelRecordMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , src_msg_id,start_province,start_city,start_district, start_township, start_street,dest_province,dest_city,dest_district, dest_township, dest_street, create_time, modify_time, create_name, modify_name
        , i_g_b_i_result_data, publish_transport_is_show_good_car, start_addr_source, dest_addr_source
        , th_min_price, th_max_price, suggest_price, suggest_min_price, suggest_max_price, fix_price_min, fix_price_max,
        fix_price_fast, client_type, show_good_car_price_transport_tab, automatic_good_car_price_transport_type, good_car_price_transport
        , distance_kilometer, other_fee, meet_commission_rules, commission_transport, publish_type, goods_model_score
    </sql>

    <select id="selectBySrcMsgId"
            resultType="com.teyuntong.infra.task.service.biz.goods.addresslevel.entity.GoodsAddressLevelRecord">
        select
        <include refid="Base_Column_List"/>
        from goods_address_level_record
        where src_msg_id = #{srcMsgId}
    </select>

	<select id="queryGoodsModelScore"
	        resultType="com.teyuntong.infra.task.service.biz.goods.addresslevel.entity.GoodsAddressLevelRecord">
        select src_msg_id as srcMsgId,
               goods_model_score as goodsModelScore
        from goods_address_level_record
        where goods_model_score is not null and src_msg_id in
        <foreach collection="srcMsgIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>
