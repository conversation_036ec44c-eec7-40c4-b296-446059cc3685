<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.car.mybatis.mapper.LotteryRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.car.mybatis.entity.LotteryRecord">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="user_call_phone" property="userCallPhone" />
        <result column="promo_activity_id" property="promoActivityId" />
        <result column="activity_name" property="activityName" />
        <result column="draw_activity_info_id" property="drawActivityInfoId" />
        <result column="prob_coupon_id" property="probCouponId" />
        <result column="prob_coupon_name" property="probCouponName" />
        <result column="award_type" property="awardType" />
        <result column="coupon_amount" property="couponAmount" />
        <result column="expiration_time" property="expirationTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="is_delete" property="isDelete" />
        <result column="is_win" property="isWin" />
        <result column="source" property="source" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, user_call_phone, promo_activity_id, activity_name, draw_activity_info_id, prob_coupon_id, prob_coupon_name, award_type, coupon_amount, expiration_time, create_time, update_time, create_by, update_by, is_delete, is_win, source
    </sql>

</mapper>
