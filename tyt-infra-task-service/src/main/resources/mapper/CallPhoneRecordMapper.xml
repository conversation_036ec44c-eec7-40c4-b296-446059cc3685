<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.car.mybatis.mapper.CallPhoneRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.car.mybatis.entity.CallPhoneRecord">
        <id column="id" property="id" />
        <result column="src_msg_id" property="srcMsgId" />
        <result column="car_user_id" property="carUserId" />
        <result column="car_user_name" property="carUserName" />
        <result column="car_is_vip" property="carIsVip" />
        <result column="path" property="path" />
        <result column="module" property="module" />
        <result column="create_time" property="createTime" />
        <result column="plat_id" property="platId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, src_msg_id, car_user_id, car_user_name, car_is_vip, path, module, create_time, plat_id
    </sql>

</mapper>
