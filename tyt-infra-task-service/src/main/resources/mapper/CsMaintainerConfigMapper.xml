<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.info.mybatis.mapper.CsMaintainerConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.info.mybatis.entity.CsMaintainerConfig">
        <id column="id" property="id" />
        <result column="port" property="port" />
        <result column="config_switch" property="configSwitch" />
        <result column="participants" property="participants" />
        <result column="status" property="status" />
        <result column="operator" property="operator" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, port, config_switch, participants, status, operator, create_time, modify_time
    </sql>

    <select id="getCsMaintainerConfigByPort" resultMap="BaseResultMap">
        select * from cs_maintainer_config where port = #{belongTo} and status = 1 limit 1
    </select>

</mapper>
