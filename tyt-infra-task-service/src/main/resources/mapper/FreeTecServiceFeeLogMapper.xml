<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.goods.goodsfee.mapper.FreeTecServiceFeeLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.goods.goodsfee.entity.FreeTecServiceFeeLogDO">
        <id column="id" property="id" />
        <result column="src_msg_id" property="srcMsgId" />
        <result column="new_transport_user_free" property="newTransportUserFree" />
        <result column="city_free" property="cityFree" />
        <result column="good_car_price_transport_free" property="goodCarPriceTransportFree" />
        <result column="member_time_out_free" property="memberTimeOutFree" />
        <result column="no_member_time_out_free" property="noMemberTimeOutFree" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, src_msg_id, new_transport_user_free, city_free, good_car_price_transport_free, member_time_out_free, no_member_time_out_free, create_time, modify_time
    </sql>

    <select id="queryNeedPushTransportIds" resultType="java.lang.Long">
        select l.src_msg_id
        from tyt_free_tec_service_fee_log l
        left join tyt_transport_main m on l.src_msg_id = m.id
        where l.create_time > current_date() and l.member_time_out_free = 1
        and m.status = 1 and m.ctime > #{start}
        order by l.src_msg_id desc
    </select>

</mapper>
