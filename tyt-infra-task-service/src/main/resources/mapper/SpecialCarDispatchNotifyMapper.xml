<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.dispatch.mapper.SpecialCarDispatchNotifyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.dispatch.entity.SpecialCarDispatchNotifyDO">
        <id column="id" property="id" />
        <result column="dispatch_id" property="dispatchId" />
        <result column="ts_id" property="tsId" />
        <result column="publish_user_type" property="publishUserType" />
        <result column="dispatch_company_id" property="dispatchCompanyId" />
        <result column="dispatch_user_id" property="dispatchUserId" />
        <result column="dispatch_user_name" property="dispatchUserName" />
        <result column="dispatch_cell_phone" property="dispatchCellPhone" />
        <result column="dispatch_ding_talk_phone" property="dispatchDingTalkPhone" />
        <result column="notify_count" property="notifyCount" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, dispatch_id, ts_id, publish_user_type, dispatch_company_id, dispatch_user_id, dispatch_user_name, dispatch_cell_phone, dispatch_ding_talk_phone, notify_count, create_time, modify_time
    </sql>


    <select id="selectByGoodsId" resultMap="BaseResultMap">
        select *
        from tyt_special_car_dispatch_notify
        where ts_id = #{tsId}
        order by create_time desc limit 1
    </select>

    <select id="selectByDispatchId" resultMap="BaseResultMap">
        select *
        from tyt_special_car_dispatch_notify
        where dispatch_id = #{dispatchId}
        order by create_time desc limit 1
    </select>
</mapper>
