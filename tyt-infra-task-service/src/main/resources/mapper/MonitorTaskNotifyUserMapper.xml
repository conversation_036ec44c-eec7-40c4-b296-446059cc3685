<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.trade.monitor.mapper.MonitorTaskNotifyUserMapper">

  <select id="getNotifyUserList" resultType="com.teyuntong.infra.task.service.biz.trade.monitor.entity.MonitorTaskNotifyUser">

      SELECT
          m.id id,
          m.monitor_item monitorItem,
          m.user_id userId,
          m.name name,
          m.phone phone,
          m.email email
      FROM
          monitor_task_notify_user m
      WHERE
          m.monitor_item in (#{monitorItem} , 0 )
        AND m.status = 1
  </select>


</mapper>
