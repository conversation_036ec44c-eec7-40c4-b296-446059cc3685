<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.market.activity.mybatis.mapper.MarketingConventionCarouselMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.MarketingConventionCarouselDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="name" property="name" />
        <result column="exposure_used_count" property="exposureUsedCount" />
        <result column="call_count" property="callCount" />
        <result column="orders_count" property="ordersCount" />
        <result column="orders_fee_count" property="ordersFeeCount" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, name, exposure_used_count, call_count, orders_count, orders_fee_count, create_time, modify_time
    </sql>

</mapper>
