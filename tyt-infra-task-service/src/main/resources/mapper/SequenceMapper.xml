<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.common.sequence.mapper.SequenceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.common.sequence.entity.SequenceDO">
        <id column="name" property="name" />
        <result column="number" property="number" />
        <result column="dates" property="dates" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        name, number, dates
    </sql>

    <update id="addNumber">
        update tyt_sequence
        set number = number + 1
        where name = #{sequenceName}
    </update>

    <select id="selectByName" resultMap="BaseResultMap">
        select *
        from tyt_sequence
        where name = #{sequenceName}
    </select>

</mapper>
