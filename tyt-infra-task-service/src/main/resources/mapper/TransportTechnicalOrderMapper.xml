<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.trade.techfee.mapper.TransportTechnicalOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.trade.techfee.entity.TransportTechnicalOrderDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="pay_user_id" property="payUserId" />
        <result column="order_id" property="orderId" />
        <result column="technical_service_no" property="technicalServiceNo" />
        <result column="technical_service_fee" property="technicalServiceFee" />
        <result column="status" property="status" />
        <result column="refund_status" property="refundStatus" />
        <result column="refund_time" property="refundTime" />
        <result column="refund_arrival_time" property="refundArrivalTime" />
        <result column="refund_err_msg" property="refundErrMsg" />
        <result column="refund_amount" property="refundAmount" />
        <result column="remark" property="remark" />
        <result column="ctime" property="ctime" />
        <result column="mtime" property="mtime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, pay_user_id, order_id, technical_service_no, technical_service_fee, status, refund_status, refund_time, refund_arrival_time, refund_err_msg, refund_amount, remark, ctime, mtime
    </sql>

    <select id="selectByTechnicalServiceNo"
            resultMap="BaseResultMap">
        select * from tyt_transport_technical_order where technical_service_no =#{technicalServiceNo} and refund_status in (1,2)  limit 1
    </select>

</mapper>
