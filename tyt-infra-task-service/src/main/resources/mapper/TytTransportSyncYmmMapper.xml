<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.ymm.mybatis.TytTransportSyncYmmMapper">
  <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.ymm.entity.TytTransportSyncYmm">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="src_msg_id" jdbcType="BIGINT" property="srcMsgId" />
    <result column="cargo_id" jdbcType="BIGINT" property="cargoId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="partner_serial_no" jdbcType="VARCHAR" property="partnerSerialNo" />
    <result column="transport_status" jdbcType="INTEGER" property="transportStatus" />
    <result column="sync_status" jdbcType="INTEGER" property="syncStatus" />
    <result column="sub_code" jdbcType="VARCHAR" property="subCode" />
    <result column="sub_code_msg" jdbcType="VARCHAR" property="subCodeMsg" />
    <result column="tracking_msg" jdbcType="VARCHAR" property="trackingMsg" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
  </resultMap>
  <sql id="Base_Column_List">
    id,src_msg_id,cargo_id,user_id,partner_serial_no,transport_status,sync_status,sub_code,sub_code_msg,tracking_msg,ctime,mtime,is_delete
  </sql>

  <select id="selectBySrcMsgId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from tyt_transport_sync_ymm
    where src_msg_id = #{srcMsgId,jdbcType=BIGINT} and sync_status = 0 and transport_status = 0 and is_delete = 0 order by ctime limit 1
  </select>

  <select id="countByUserId" resultType="int">
    select count(id) from tyt_transport_sync_ymm
    where user_id = #{userId,jdbcType=BIGINT}
      and sync_status = 0
      and publish_type = #{publishType,jdbcType=INTEGER}
      and  ctime BETWEEN CONCAT( CURDATE(), ' 00:00:00' ) AND CONCAT( CURDATE(), ' 23:59:59' )
  </select>

  <select id="countBySyncStatus" resultType="int">
    select count(id) from tyt_transport_sync_ymm where src_msg_id = #{srcMsgId,jdbcType=BIGINT} and sync_status = #{syncStatus,jdbcType=INTEGER} and is_delete = 0
  </select>
</mapper>
