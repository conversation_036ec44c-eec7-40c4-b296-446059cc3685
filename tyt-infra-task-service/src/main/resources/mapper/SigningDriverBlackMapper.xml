<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.driver.mybatis.mapper.SigningDriverBlackMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.driver.mybatis.entity.SigningDriverBlackDO">
        <id column="id" property="id" />
        <result column="cell_phone" property="cellPhone" />
        <result column="user_id" property="userId" />
        <result column="sign_status" property="signStatus" />
        <result column="black_type" property="blackType" />
        <result column="restrict_num" property="restrictNum" />
        <result column="restrict_start_time" property="restrictStartTime" />
        <result column="restrict_end_time" property="restrictEndTime" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, cell_phone, user_id, sign_status, black_type, restrict_num, restrict_start_time, restrict_end_time, remark, create_time, modify_time
    </sql>

    <select id="getExpiredList" resultMap="BaseResultMap">
        SELECT * FROM `tyt_signing_driver_black` WHERE id &gt; #{maxId} and sign_status=2 AND black_type=1 AND restrict_end_time &lt; now() order by id asc limit #{limit};
    </select>

    <update id="updateDriverBlackById">
        update tyt_signing_driver_black set sign_status=1,black_type=0,restrict_num=0,restrict_start_time=null,restrict_end_time=null,modify_time=now() where id = #{id}
    </update>

</mapper>
