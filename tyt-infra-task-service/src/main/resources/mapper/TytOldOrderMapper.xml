<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.trade.infofee.mapper.OldOrderMapper">
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.trade.infofee.entity.OldOrder">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="order_id" jdbcType="VARCHAR" property="orderId"/>
        <result column="thirdparty_platform_type" jdbcType="INTEGER" property="thirdpartyPlatformType"/>
        <result column="thirdparty_platform_order_no" jdbcType="VARCHAR" property="thirdpartyPlatformOrderNo"/>
        <result column="cell_phone" jdbcType="VARCHAR" property="cellPhone"/>
        <result column="pay_status" jdbcType="INTEGER" property="payStatus"/>
        <result column="renewal_years" jdbcType="INTEGER" property="renewalYears"/>
        <result column="unit" jdbcType="VARCHAR" property="unit"/>
        <result column="paymethod" jdbcType="VARCHAR" property="paymethod"/>
        <result column="defaultbank" jdbcType="VARCHAR" property="defaultbank"/>
        <result column="STATUS" jdbcType="INTEGER" property="status"/>
        <result column="ctime" jdbcType="TIMESTAMP" property="ctime"/>
        <result column="mtime" jdbcType="TIMESTAMP" property="mtime"/>
        <result column="total_fee" jdbcType="INTEGER" property="totalFee"/>
        <result column="fee_amount" jdbcType="INTEGER" property="feeAmount"/>
        <result column="plat_id" jdbcType="INTEGER" property="platId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="user_type" jdbcType="VARCHAR" property="userType"/>
        <result column="user_acct_type" jdbcType="VARCHAR" property="userAcctType"/>
        <result column="pay_channel" jdbcType="CHAR" property="payChannel"/>
        <result column="pay_sub_channel" jdbcType="VARCHAR" property="paySubChannel"/>
        <result column="op_type" jdbcType="CHAR" property="opType"/>
        <result column="note" jdbcType="VARCHAR" property="note"/>
        <result column="good_pay_id" jdbcType="INTEGER" property="goodPayId"/>
        <result column="order_num" jdbcType="VARCHAR" property="orderNum"/>
        <result column="thirdparty_order_serial_num" jdbcType="VARCHAR" property="thirdpartyOrderSerialNum"/>
        <result column="pay_order_no" jdbcType="VARCHAR" property="payOrderNo"/>
        <result column="order_type" jdbcType="INTEGER" property="orderType"/>
        <result column="goods_name" jdbcType="VARCHAR" property="goodsName"/>
        <result column="appid" jdbcType="VARCHAR" property="appid"/>
    </resultMap>

    <select id="getOrderByCtime" resultType="com.teyuntong.infra.task.service.biz.trade.infofee.entity.OldOrder">
    SELECT
	too.id AS id,
	too.order_id AS orderId,
	too.user_id AS userId,
	too.total_fee AS totalFee,
	too.order_num AS orderNum
    FROM
	tyt_old_order too
	LEFT JOIN tyt_transport_orders tto ON too.order_id = tto.pay_no
    WHERE
	too.STATUS = 2
	AND too.order_type = 2
	AND tto.id IS NULL
	AND too.ctime >=#{beginTime}
	AND too.note IS NULL
  </select>


    <update id="updateOrderNoteByOrderId">
        UPDATE tyt_old_order SET note=#{note} WHERE order_id=#{orderId}
    </update>

    <update id="updateOrderPayTypeByParam" parameterType="map">
        update tyt_old_order
        set mtime = mtime,
        pay_sub_channel = #{payType}
        where ctime >= #{beginTime}
        and order_id = #{orderId}
        and status = 2
        and pay_sub_channel = 'groupPay'
    </update>


    <select id="getOrderSummary" resultType="com.teyuntong.infra.task.service.biz.trade.infofee.bean.OrderSummaryVO">
        SELECT user_id userId, MAX(mtime) maxMtime
        FROM tyt_old_order
        WHERE mtime >= #{startTime} AND mtime <![CDATA[<=]]> #{endTime}
        AND order_type = 2 AND status = 2
        GROUP BY user_id
    </select>


</mapper>