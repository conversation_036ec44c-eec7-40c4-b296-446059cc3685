<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.info.mybatis.mapper.UserSubMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.info.mybatis.entity.UserSubDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="verify_flag" property="verifyFlag" />
        <result column="send_tpt_type" property="sendTptType" />
        <result column="send_tpt_number" property="sendTptNumber" />
        <result column="maintain_man" property="maintainMan" />
        <result column="cid" property="cid" />
        <result column="client_sign" property="clientSign" />
        <result column="notify_badge" property="notifyBadge" />
        <result column="new_msg_nbr" property="newMsgNbr" />
        <result column="utime" property="utime" />
        <result column="bcar_identity_lables" property="bcarIdentityLables" />
        <result column="scar_identity_lables" property="scarIdentityLables" />
        <result column="audit_bcar_identity_lables" property="auditBcarIdentityLables" />
        <result column="audit_scar_identity_lables" property="auditScarIdentityLables" />
        <result column="my_goods_menu" property="myGoodsMenu" />
        <result column="device_id" property="deviceId" />
        <result column="pocket_pwd" property="pocketPwd" />
        <result column="pocket_pwd_status" property="pocketPwdStatus" />
        <result column="deal_num" property="dealNum" />
        <result column="publish_num" property="publishNum" />
        <result column="user_group" property="userGroup" />
        <result column="bind_cliendid" property="bindCliendid" />
        <result column="bind_status" property="bindStatus" />
        <result column="level2_biging_time" property="level2BigingTime" />
        <result column="car_device_id" property="carDeviceId" />
        <result column="goods_device_id" property="goodsDeviceId" />
        <result column="car_notify_badge" property="carNotifyBadge" />
        <result column="goods_notify_badge" property="goodsNotifyBadge" />
        <result column="car_new_msg_nbr" property="carNewMsgNbr" />
        <result column="goods_new_msg_nbr" property="goodsNewMsgNbr" />
        <result column="car_break_num" property="carBreakNum" />
        <result column="goods_break_num" property="goodsBreakNum" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, verify_flag, send_tpt_type, send_tpt_number, maintain_man, cid, client_sign, notify_badge, new_msg_nbr, utime, bcar_identity_lables, scar_identity_lables, audit_bcar_identity_lables, audit_scar_identity_lables, my_goods_menu, device_id, pocket_pwd, pocket_pwd_status, deal_num, publish_num, user_group, bind_cliendid, bind_status, level2_biging_time, car_device_id, goods_device_id, car_notify_badge, goods_notify_badge, car_new_msg_nbr, goods_new_msg_nbr, car_break_num, goods_break_num
    </sql>

    <update id="updatePastExperienceMass">
        UPDATE tyt_user_sub s
        SET s.user_group = 2
        WHERE s.user_group = 1
          and s.user_id IN ( SELECT id FROM tyt_user u WHERE u.ctime &lt; DATE_SUB(CURDATE(), INTERVAL #{time} DAY) )
    </update>
    <update id="updatePastTryOutMass">
        UPDATE tyt_user_sub s
        SET s.user_group = 4
        WHERE s.user_group = 3
          AND s.level2_biging_time IS NOT NULL AND s.level2_biging_time &lt; DATE_SUB(CURDATE(), INTERVAL #{time} DAY)
    </update>
    <update id="updatePastOfficialMass">
        UPDATE tyt_user_sub s
        SET s.user_group = 6
        WHERE s.user_group = 5
          AND s.user_id IN ( SELECT id FROM tyt_user u WHERE u.end_time &lt; NOW() )
    </update>

</mapper>
