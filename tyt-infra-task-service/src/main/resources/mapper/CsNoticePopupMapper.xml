<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.customerservice.mapper.CsNoticePopupMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.customerservice.entity.CsNoticePopup">
        <id column="id" property="id" />
        <result column="title" property="title" />
        <result column="content" property="content" />
        <result column="production_id" property="productionId" />
        <result column="receive_id" property="receiveId" />
        <result column="receive_time" property="receiveTime" />
        <result column="receive_status" property="receiveStatus" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, title, content, production_id, receive_id, receive_time, receive_status, status, create_time, modify_time
    </sql>

    <insert id="addNotice">
        INSERT INTO `cs_notice_popup` (`title`,`content`,`production_id`,`receive_id`,`receive_time`) VALUES(#{noticePopup.title},#{noticePopup.content},#{noticePopup.productionId},#{noticePopup.receiveId},now())
    </insert>
</mapper>
