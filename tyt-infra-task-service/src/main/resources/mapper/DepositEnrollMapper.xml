<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.car.mybatis.mapper.DepositEnrollMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.car.mybatis.entity.DepositEnrollDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="user_name" property="userName" />
        <result column="cell_phone" property="cellPhone" />
        <result column="vip_status" property="vipStatus" />
        <result column="sign_status" property="signStatus" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
        <result column="require_amount" property="requireAmount" />
        <result column="user_group" property="userGroup" />
        <result column="agree_type" property="agreeType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, user_name, cell_phone, vip_status, sign_status, create_time, modify_time, require_amount, user_group, agree_type
    </sql>

    <select id="selectNoStatus" resultMap="BaseResultMap">
        select *
        from tyt_deposit_enroll
        where id > #{maxId}
        order by id
        limit #{limit}
    </select>

</mapper>
