<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.car.mybatis.mapper.FeedbackUserMapper">
  <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.car.mybatis.entity.FeedbackUser">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="post_user_id" jdbcType="BIGINT" property="postUserId" />
    <result column="post_user_phone" jdbcType="VARCHAR" property="postUserPhone" />
    <result column="post_user_avatar" jdbcType="VARCHAR" property="postUserAvatar" />
    <result column="post_user_type" jdbcType="SMALLINT" property="postUserType" />
    <result column="receive_user_id" jdbcType="BIGINT" property="receiveUserId" />
    <result column="receive_user_phone" jdbcType="VARCHAR" property="receiveUserPhone" />
    <result column="receive_user_avatar" jdbcType="VARCHAR" property="receiveUserAvatar" />
    <result column="receive_user_type" jdbcType="SMALLINT" property="receiveUserType" />
    <result column="del_flag" jdbcType="BIT" property="delFlag" />
    <result column="ts_order_id" jdbcType="BIGINT" property="tsOrderId" />
    <result column="ts_id" jdbcType="BIGINT" property="tsId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="transport_order_pub_user_name" jdbcType="VARCHAR" property="transportOrderPubUserName" />
    <result column="transport_order_pay_user_name" jdbcType="VARCHAR" property="transportOrderPayUserName" />
    <result column="transport_order_start_point" jdbcType="VARCHAR" property="transportOrderStartPoint" />
    <result column="transport_order_dest_point" jdbcType="VARCHAR" property="transportOrderDestPoint" />
    <result column="feedback_type" jdbcType="SMALLINT" property="feedbackType" />
    <result column="feedback_deadline" jdbcType="TIMESTAMP" property="feedbackDeadline" />
    <result column="need_handle_on_deadline" jdbcType="BIT" property="needHandleOnDeadline" />
    <result column="comment" jdbcType="VARCHAR" property="comment" />
    <result column="update_times" jdbcType="INTEGER" property="updateTimes" />
    <result column="receiver_hide_flag" jdbcType="BIT" property="receiverHideFlag" />
    <result column="lose_efficacy_reason" jdbcType="INTEGER" property="loseEfficacyReason" />
  </resultMap>

  <resultMap id="FeeDBackNumMap" type="com.teyuntong.infra.task.service.biz.user.car.pojo.FeedbackNumBean">
    <result column="userId" property="userId" jdbcType="VARCHAR" />
    <result column="num" property="num" jdbcType="INTEGER" />
  </resultMap>

  <select id="getGoodFeed" resultType="java.lang.Integer">
    select count(*) from feedback_user where receive_user_id = #{userId}
           and feedback_type = 1 and post_user_type = 3 and create_time &gt;= #{date} and create_time &lt;= #{endDate}
  </select>

  <select id="getAllFeed" resultType="java.lang.Integer">
    select count(*) from feedback_user where receive_user_id = #{userId}
                                         and  post_user_type = 3 and create_time &gt;= #{date} and create_time &lt;= #{endDate}
  </select>

  <select id="getTimeUserNum" resultMap="FeeDBackNumMap">
    SELECT
      fee.receive_user_id as userId,
      count(*) as num
    FROM
      feedback_user fee
        LEFT JOIN tyt_transport_orders_risk risk ON fee.order_id = risk.order_number
    WHERE
      fee.receive_user_type = 1
      AND fee.del_flag = 0
      AND fee.feedback_type = 1
      AND  fee.update_time &gt;= #{startTime} AND  fee.update_time &lt;= #{endTime}
      AND risk.activity_status IN ( 0, 3 )
    GROUP BY
      fee.receive_user_id
  </select>

</mapper>