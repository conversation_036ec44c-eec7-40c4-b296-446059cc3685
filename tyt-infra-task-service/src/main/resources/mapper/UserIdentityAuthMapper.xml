<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.auth.mybatis.mapper.UserIdentityAuthMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.auth.mybatis.entity.UserIdentityAuth">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="mobile" property="mobile" />
        <result column="identity_status" property="identityStatus" />
        <result column="user_class" property="userClass" />
        <result column="identity_type" property="identityType" />
        <result column="true_name" property="trueName" />
        <result column="sex" property="sex" />
        <result column="id_card" property="idCard" />
        <result column="nation" property="nation" />
        <result column="address" property="address" />
        <result column="info_status" property="infoStatus" />
        <result column="info_failure_reason" property="infoFailureReason" />
        <result column="main_url" property="mainUrl" />
        <result column="main_status" property="mainStatus" />
        <result column="main_failure_reason" property="mainFailureReason" />
        <result column="back_url" property="backUrl" />
        <result column="back_status" property="backStatus" />
        <result column="back_failue_reason" property="backFailueReason" />
        <result column="qualifications_url" property="qualificationsUrl" />
        <result column="enterprise_name" property="enterpriseName" />
        <result column="enterprise_type" property="enterpriseType" />
        <result column="enterprise_amount" property="enterpriseAmount" />
        <result column="enterprise_phone" property="enterprisePhone" />
        <result column="license_url" property="licenseUrl" />
        <result column="license_status" property="licenseStatus" />
        <result column="license_failure_reason" property="licenseFailureReason" />
        <result column="work_type" property="workType" />
        <result column="ctime" property="ctime" />
        <result column="examine_status" property="examineStatus" />
        <result column="examine_time" property="examineTime" />
        <result column="examine_user_id" property="examineUserId" />
        <result column="examine_user_name" property="examineUserName" />
        <result column="utime" property="utime" />
        <result column="data_type" property="dataType" />
        <result column="i_photo_url" property="iPhotoUrl" />
        <result column="i_photo_status" property="iPhotoStatus" />
        <result column="i_photo_failure_reason" property="iPhotoFailureReason" />
        <result column="plat_id" property="platId" />
        <result column="enterprise_auth_license_url" property="enterpriseAuthLicenseUrl" />
        <result column="enterprise_auth_company_name" property="enterpriseAuthCompanyName" />
        <result column="enterprise_auth_credit_code" property="enterpriseAuthCreditCode" />
        <result column="enterprise_auth_status" property="enterpriseAuthStatus" />
        <result column="enterprise_auth_failure_reason" property="enterpriseAuthFailureReason" />
        <result column="enterprise_auth_user_id" property="enterpriseAuthUserId" />
        <result column="enterprise_auth_user_name" property="enterpriseAuthUserName" />
        <result column="enterprise_auth_ctime" property="enterpriseAuthCtime" />
        <result column="enterprise_auth_utime" property="enterpriseAuthUtime" />
        <result column="user_auth_path" property="userAuthPath" />
        <result column="enterprise_auth_path" property="enterpriseAuthPath" />
        <result column="audit_status" property="auditStatus" />
        <result column="real_verify" property="realVerify" />
        <result column="face_verify" property="faceVerify" />
        <result column="id_card_valid_date" property="idCardValidDate" />
        <result column="id_card_long_term" property="idCardLongTerm" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, mobile, identity_status, user_class, identity_type, true_name, sex, id_card, nation, address, info_status, info_failure_reason, main_url, main_status, main_failure_reason, back_url, back_status, back_failue_reason, qualifications_url, enterprise_name, enterprise_type, enterprise_amount, enterprise_phone, license_url, license_status, license_failure_reason, work_type, ctime, examine_status, examine_time, examine_user_id, examine_user_name, utime, data_type, i_photo_url, i_photo_status, i_photo_failure_reason, plat_id, enterprise_auth_license_url, enterprise_auth_company_name, enterprise_auth_credit_code, enterprise_auth_status, enterprise_auth_failure_reason, enterprise_auth_user_id, enterprise_auth_user_name, enterprise_auth_ctime, enterprise_auth_utime, user_auth_path, enterprise_auth_path, audit_status, real_verify, face_verify, id_card_valid_date, id_card_long_term
    </sql>

    <select id="selectUserIdentityAuthDto" resultType="com.teyuntong.infra.task.service.biz.user.auth.pojo.UserIdentityAuthDto">
        SELECT
            tuia.id,
            tuia.plat_id AS platId,
            tuia.user_id AS userId,
            tu.cell_phone AS cellPhone
        FROM
            tyt_user_identity_auth tuia
            LEFT JOIN tyt_user tu ON tuia.user_id = tu.id
        where tuia.identity_status = 1 AND id_card_valid_date BETWEEN  #{betweenDate,jdbcType=VARCHAR} AND #{andDate,jdbcType=VARCHAR} AND id_card_long_term = 0
    </select>
    <select id="getByUserId" resultMap="BaseResultMap">
        select * from tyt_user_identity_auth
        where user_id = #{userId}
        limit 1
    </select>
</mapper>
