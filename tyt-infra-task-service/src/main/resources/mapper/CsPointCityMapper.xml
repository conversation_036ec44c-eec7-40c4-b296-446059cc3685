<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.info.mybatis.mapper.CsPointCityMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.info.mybatis.entity.CsPointCity">
        <id column="id" property="id" />
        <result column="city" property="city" />
        <result column="type" property="type" />
        <result column="ctime" property="ctime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, city, type, ctime
    </sql>

    <select id="getPointCityByCity" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from cs_point_city where city=#{city} limit 1;
    </select>

</mapper>
