<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.trade.infofee.mapper.TransportOrdersInvoiceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.trade.infofee.entity.TransportOrdersInvoiceDO">
        <id column="id" property="id" />
        <result column="start_point" property="startPoint" />
        <result column="dest_point" property="destPoint" />
        <result column="task_content" property="taskContent" />
        <result column="tel" property="tel" />
        <result column="pub_time" property="pubTime" />
        <result column="ctime" property="ctime" />
        <result column="upload_cellphone" property="uploadCellphone" />
        <result column="user_id" property="userId" />
        <result column="pub_user_name" property="pubUserName" />
        <result column="linkman" property="linkman" />
        <result column="tel3" property="tel3" />
        <result column="tel4" property="tel4" />
        <result column="sort_id" property="sortId" />
        <result column="ts_order_no" property="tsOrderNo" />
        <result column="thirdparty_platform_type" property="thirdpartyPlatformType" />
        <result column="thirdparty_platform_order_no" property="thirdpartyPlatformOrderNo" />
        <result column="ts_id" property="tsId" />
        <result column="pay_order_no" property="payOrderNo" />
        <result column="technical_service_no" property="technicalServiceNo" />
        <result column="refund_flag" property="refundFlag" />
        <result column="de_refund_dueDate" property="deRefundDuedate" />
        <result column="delay_refund_status" property="delayRefundStatus" />
        <result column="cancel_status" property="cancelStatus" />
        <result column="cancel_reason" property="cancelReason" />
        <result column="info_fee_type" property="infoFeeType" />
        <result column="pay_user_id" property="payUserId" />
        <result column="pay_user_name" property="payUserName" />
        <result column="pay_cell_phone" property="payCellPhone" />
        <result column="pay_link_phone" property="payLinkPhone" />
        <result column="rob_status" property="robStatus" />
        <result column="pay_status" property="payStatus" />
        <result column="create_time" property="createTime" />
        <result column="create_by" property="createBy" />
        <result column="remark" property="remark" />
        <result column="pay_no" property="payNo" />
        <result column="pay_type" property="payType" />
        <result column="pay_sub_channel" property="paySubChannel" />
        <result column="tec_service_fee" property="tecServiceFee" />
        <result column="pay_amount" property="payAmount" />
        <result column="pay_fee_amount" property="payFeeAmount" />
        <result column="total_order_amount" property="totalOrderAmount" />
        <result column="car_amount" property="carAmount" />
        <result column="goods_amount" property="goodsAmount" />
        <result column="platform_service_amount" property="platformServiceAmount" />
        <result column="car_service_amount" property="carServiceAmount" />
        <result column="handle_ex_time" property="handleExTime" />
        <result column="coupon_amount" property="couponAmount" />
        <result column="carriage_fee" property="carriageFee" />
        <result column="time_limit_identification" property="timeLimitIdentification" />
        <result column="pay_service_charge" property="payServiceCharge" />
        <result column="pay_end_time" property="payEndTime" />
        <result column="refund_amount" property="refundAmount" />
        <result column="refund_time" property="refundTime" />
        <result column="refund_arrival_time" property="refundArrivalTime" />
        <result column="refund_status" property="refundStatus" />
        <result column="refund_reason" property="refundReason" />
        <result column="refund_err_msg" property="refundErrMsg" />
        <result column="agree_time" property="agreeTime" />
        <result column="load_time" property="loadTime" />
        <result column="refuse_time" property="refuseTime" />
        <result column="head_city" property="headCity" />
        <result column="head_no" property="headNo" />
        <result column="tail_city" property="tailCity" />
        <result column="tail_no" property="tailNo" />
        <result column="car_id" property="carId" />
        <result column="mtime" property="mtime" />
        <result column="ex_cancel_status" property="exCancelStatus" />
        <result column="cost_status" property="costStatus" />
        <result column="delay_status" property="delayStatus" />
        <result column="goods_show" property="goodsShow" />
        <result column="car_show" property="carShow" />
        <result column="de_payment_dueDate" property="dePaymentDuedate" />
        <result column="loading_status" property="loadingStatus" />
        <result column="loading_child_status" property="loadingChildStatus" />
        <result column="op_id" property="opId" />
        <result column="op_name" property="opName" />
        <result column="is_deal_car" property="isDealCar" />
        <result column="deal_car_time" property="dealCarTime" />
        <result column="car_replace" property="carReplace" />
        <result column="source_type" property="sourceType" />
        <result column="is_complaint" property="isComplaint" />
        <result column="refund_specific_reason" property="refundSpecificReason" />
        <result column="refund_remark" property="refundRemark" />
        <result column="machine_remark" property="machineRemark" />
        <result column="order_new_status" property="orderNewStatus" />
        <result column="de_load_due_date" property="deLoadDueDate" />
        <result column="delay_load_status" property="delayLoadStatus" />
        <result column="driver_id" property="driverId" />
        <result column="invoice_transport" property="invoiceTransport" />
        <result column="risk_control_status" property="riskControlStatus" />
        <result column="risk_control_time" property="riskControlTime" />
        <result column="freight_status" property="freightStatus" />
        <result column="invoice_service_code" property="invoiceServiceCode" />
        <result column="invoice_issuer_id" property="invoiceIssuerId" />
        <result column="invoice_issuer_code" property="invoiceIssuerCode" />
        <result column="invoice_third_party_id" property="invoiceThirdPartyId" />
        <result column="invoice_third_party_no" property="invoiceThirdPartyNo" />
        <result column="over_limit" property="overLimit" />
        <result column="expect_arrive_time" property="expectArriveTime" />
        <result column="driver_confirm_status" property="driverConfirmStatus" />
        <result column="is_assign_order" property="isAssignOrder" />
        <result column="three_waybill_status" property="threeWaybillStatus" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, start_point, dest_point, task_content, tel, pub_time, ctime, upload_cellphone, user_id, pub_user_name, linkman, tel3, tel4, sort_id, ts_order_no, thirdparty_platform_type, thirdparty_platform_order_no, ts_id, pay_order_no, technical_service_no, refund_flag, de_refund_dueDate, delay_refund_status, cancel_status, cancel_reason, info_fee_type, pay_user_id, pay_user_name, pay_cell_phone, pay_link_phone, rob_status, pay_status, create_time, create_by, remark, pay_no, pay_type, pay_sub_channel, tec_service_fee, pay_amount, pay_fee_amount, total_order_amount, car_amount, goods_amount, platform_service_amount, car_service_amount, handle_ex_time, coupon_amount, carriage_fee, time_limit_identification, pay_service_charge, pay_end_time, refund_amount, refund_time, refund_arrival_time, refund_status, refund_reason, refund_err_msg, agree_time, load_time, refuse_time, head_city, head_no, tail_city, tail_no, car_id, mtime, ex_cancel_status, cost_status, delay_status, goods_show, car_show, de_payment_dueDate, loading_status, loading_child_status, op_id, op_name, is_deal_car, deal_car_time, car_replace, source_type, is_complaint, refund_specific_reason, refund_remark, machine_remark, order_new_status, de_load_due_date, delay_load_status, driver_id, invoice_transport, risk_control_status, risk_control_time, freight_status, invoice_service_code, invoice_issuer_id, invoice_issuer_code, invoice_third_party_id, invoice_third_party_no, over_limit, expect_arrive_time, driver_confirm_status, is_assign_order, three_waybill_status
    </sql>

    <select id="selectUnfinishedOrders" resultType="com.teyuntong.infra.task.service.biz.trade.infofee.entity.TransportOrdersInvoiceDO">
        SELECT *
        FROM tyt_transport_orders_invoice
        WHERE id > #{maxId} and invoice_service_code = 'HBWJ'
          AND three_waybill_status NOT IN ('HAS_FINISH', 'HAS_CANCEL') and mtime >= #{startTime}
        ORDER BY id ASC
            LIMIT #{pageSize}
    </select>

    <update id="updateInvoiceStatusById">
        update tyt_transport_orders_invoice set three_waybill_status = #{threeWaybillStatus},mtime=mtime where id = #{orderId}
    </update>

</mapper>
