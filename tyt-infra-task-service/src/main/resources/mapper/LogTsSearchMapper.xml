<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.goods.goodsinfo.mapper.LogTsSearchMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.goods.goodsinfo.entity.LogTsSearchDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="start_coord" property="startCoord" />
        <result column="start_range" property="startRange" />
        <result column="dest_coord" property="destCoord" />
        <result column="dest_range" property="destRange" />
        <result column="car_id" property="carId" />
        <result column="head_no" property="headNo" />
        <result column="head_city" property="headCity" />
        <result column="client_version" property="clientVersion" />
        <result column="client_sign" property="clientSign" />
        <result column="sort_type" property="sortType" />
        <result column="number_type" property="numberType" />
        <result column="os_version" property="osVersion" />
        <result column="car_length" property="carLength" />
        <result column="car_type" property="carType" />
        <result column="special_required" property="specialRequired" />
        <result column="client_id" property="clientId" />
        <result column="ctime" property="ctime" />
        <result column="start_weight" property="startWeight" />
        <result column="end_weight" property="endWeight" />
        <result column="start_provinc" property="startProvinc" />
        <result column="start_city" property="startCity" />
        <result column="start_area" property="startArea" />
        <result column="dest_provinc" property="destProvinc" />
        <result column="dest_city" property="destCity" />
        <result column="dest_area" property="destArea" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, start_coord, start_range, dest_coord, dest_range, car_id, head_no, head_city, client_version, client_sign, sort_type, number_type, os_version, car_length, car_type, special_required, client_id, ctime, start_weight, end_weight, start_provinc, start_city, start_area, dest_provinc, dest_city, dest_area
    </sql>

	<select id="queryUserByRoute" resultType="java.lang.Long">
        select distinct user_id
        from tyt_log_ts_search
        where start_city = #{startCity} and (dest_city = '' or dest_city = #{destCity})
        and ctime between #{searchStart} and now()
    </select>

    <select id="getUserLastSearch" resultType="com.teyuntong.infra.task.service.biz.goods.goodsinfo.entity.LogTsSearchDO">
        select *
        from tyt_log_ts_search
        where user_id = #{userId}
        order by id desc
        limit 1
    </select>

</mapper>
