<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.car.mybatis.mapper.CarDetailTailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.car.mybatis.entity.CarDetailTailDO">
        <id column="id" property="id" />
        <result column="car_id" property="carId" />
        <result column="user_id" property="userId" />
        <result column="city" property="city" />
        <result column="car_no" property="carNo" />
        <result column="car_type" property="carType" />
        <result column="other_car_type" property="otherCarType" />
        <result column="owner" property="owner" />
        <result column="address" property="address" />
        <result column="use_nature" property="useNature" />
        <result column="car_brand" property="carBrand" />
        <result column="car_idcode" property="carIdcode" />
        <result column="car_engine_no" property="carEngineNo" />
        <result column="car_register" property="carRegister" />
        <result column="issue_date" property="issueDate" />
        <result column="record_no" property="recordNo" />
        <result column="people" property="people" />
        <result column="total_weight" property="totalWeight" />
        <result column="curb_weight" property="curbWeight" />
        <result column="check_weight" property="checkWeight" />
        <result column="tow_weight" property="towWeight" />
        <result column="scrap_date" property="scrapDate" />
        <result column="test_date" property="testDate" />
        <result column="length" property="length" />
        <result column="width" property="width" />
        <result column="height" property="height" />
        <result column="state" property="state" />
        <result column="tail_succ_remark" property="tailSuccRemark" />
        <result column="blong_type" property="blongType" />
        <result column="car_brand_detail" property="carBrandDetail" />
        <result column="check_record" property="checkRecord" />
        <result column="is_pure_flat" property="isPureFlat" />
        <result column="other_pure_flat" property="otherPureFlat" />
        <result column="load_surface_length" property="loadSurfaceLength" />
        <result column="load_surface_height" property="loadSurfaceHeight" />
        <result column="load_surface_tail_length" property="loadSurfaceTailLength" />
        <result column="gooseneck_height" property="gooseneckHeight" />
        <result column="gooseneck_length" property="gooseneckLength" />
        <result column="plate_height" property="plateHeight" />
        <result column="max_payload" property="maxPayload" />
        <result column="is_expose_tyre" property="isExposeTyre" />
        <result column="is_with_wing" property="isWithWing" />
        <result column="is_have_backplate" property="isHaveBackplate" />
        <result column="is_joint_pull" property="isJointPull" />
        <result column="joint_pull_length" property="jointPullLength" />
        <result column="is_joint" property="isJoint" />
        <result column="joint_length" property="jointLength" />
        <result column="temp_licence_expires" property="tempLicenceExpires" />
        <result column="maximum_axle_load" property="maximumAxleLoad" />
        <result column="axles" property="axles" />
        <result column="tail_issue_authority" property="tailIssueAuthority" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, car_id, user_id, city, car_no, car_type, other_car_type, owner, address, use_nature, car_brand, car_idcode, car_engine_no, car_register, issue_date, record_no, people, total_weight, curb_weight, check_weight, tow_weight, scrap_date, test_date, length, width, height, state, tail_succ_remark, blong_type, car_brand_detail, check_record, is_pure_flat, other_pure_flat, load_surface_length, load_surface_height, load_surface_tail_length, gooseneck_height, gooseneck_length, plate_height, max_payload, is_expose_tyre, is_with_wing, is_have_backplate, is_joint_pull, joint_pull_length, is_joint, joint_length, temp_licence_expires, maximum_axle_load, axles, tail_issue_authority
    </sql>

    <select id="getByCarId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from tyt_car_detail_tail
        where car_id = #{carId} limit 1
    </select>

</mapper>
