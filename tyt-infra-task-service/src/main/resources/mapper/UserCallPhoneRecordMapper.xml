<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.info.mybatis.mapper.UserCallPhoneRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.info.mybatis.entity.UserCallPhoneRecordDO">
        <id column="id" property="id" />
        <result column="ts_id" property="tsId" />
        <result column="user_id" property="userId" />
        <result column="ctime" property="ctime" />
        <result column="level" property="level" />
        <result column="path" property="path" />
        <result column="plat_id" property="platId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, ts_id, user_id, ctime, level, path, plat_id
    </sql>

    <resultMap id="UserCallRecordVOResultMap" type="com.teyuntong.infra.task.service.biz.user.info.pojo.UserCallRecordVO">
        <id column="userId" property="userId" />
        <result column="minCtime" property="minCtime" />
        <result column="maxCtime" property="maxCtime" />
    </resultMap>

    <select id="getUserCallRecords" resultMap="UserCallRecordVOResultMap">
        SELECT user_id userId, MIN(ctime) minCtime, MAX(ctime) maxCtime
        FROM tyt_user_call_phone_record
        WHERE ctime >= #{startTime} AND ctime <![CDATA[<=]]> #{endTime}
        GROUP BY user_id
    </select>


    <update id="updateCallPhoneDates">
        UPDATE tyt_user_archives
        SET first_call_phone_date = (CASE WHEN first_call_phone_date IS NULL THEN #{firstCallPhoneDate} ELSE first_call_phone_date END),
            last_call_phone_date = #{lastCallPhoneDate}
        WHERE user_id = #{userId}
    </update>

    <select id="getUserCallRecordsCount" resultType="java.lang.Integer">
        select count(*) from tyt_user_call_phone_record where user_id = #{userId} and ctime >= #{startTime} and ctime <![CDATA[<=]]> #{endTime}
    </select>


    <select id="getUserCallRecordsByTime" resultMap="BaseResultMap">
        SELECT *
        FROM tyt_user_call_phone_record
        WHERE ctime &gt;= #{startTime} AND ctime &lt;= #{endTime}
    </select>



</mapper>
