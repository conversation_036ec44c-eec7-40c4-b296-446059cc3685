<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.info.mybatis.mapper.ApiDataUserCreditInfoTwoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.info.mybatis.entity.ApiDataUserCreditInfoTwoDO">
        <id column="user_id" property="userId" />
        <result column="user_identity_type1" property="userIdentityType1" />
        <result column="is_blacklist_user" property="isBlacklistUser" />
        <result column="fraud_count" property="fraudCount" />
        <result column="transport_information_payment_count" property="transportInformationPaymentCount" />
        <result column="vip_growth_days" property="vipGrowthDays" />
        <result column="transport_145_count" property="transport145Count" />
        <result column="transport_succ_count" property="transportSuccCount" />
        <result column="up_date" property="upDate" />
        <result column="transport_score" property="transportScore" />
        <result column="ts_rank" property="tsRank" />
        <result column="identity_status_score" property="identityStatusScore" />
        <result column="enterprise_auth_status_score" property="enterpriseAuthStatusScore" />
        <result column="trans_score" property="transScore" />
        <result column="boda_score" property="bodaScore" />
        <result column="orders_score" property="ordersScore" />
        <result column="lvyuelv_score" property="lvyuelvScore" />
        <result column="total_score" property="totalScore" />
        <result column="rank_level" property="rankLevel" />
        <result column="last_level_gap" property="lastLevelGap" />
        <result column="car_identity_status_score" property="carIdentityStatusScore" />
        <result column="car_enterprise_auth_status_score" property="carEnterpriseAuthStatusScore" />
        <result column="car_identity_numbers_score" property="carIdentityNumbersScore" />
        <result column="car_history_lvyue_score" property="carHistoryLvyueScore" />
        <result column="car_recent_two_months_lvyue_score" property="carRecentTwoMonthsLvyueScore" />
        <result column="car_total_server_score" property="carTotalServerScore" />
        <result column="car_server_rank_score" property="carServerRankScore" />
        <result column="car_vip_growth_days" property="carVipGrowthDays" />
        <result column="car_transport_information_payment_count" property="carTransportInformationPaymentCount" />
        <result column="last_week_rank_level" property="lastWeekRankLevel" />
        <result column="complain_rate" property="complainRate" />
        <result column="performance_num_score" property="performanceNumScore" />
        <result column="closing_ratio_score" property="closingRatioScore" />
        <result column="cancel_ratio_score" property="cancelRatioScore" />
        <result column="duty_complaint_num_score" property="dutyComplaintNumScore" />
        <result column="task_no_complaint_score" property="taskNoComplaintScore" />
        <result column="task_no_cancel_score" property="taskNoCancelScore" />
        <result column="duplicate_goods_ratio_score" property="duplicateGoodsRatioScore" />
        <result column="user_label_text" property="userLabelText" />
        <result column="user_label_icon" property="userLabelIcon" />
        <result column="previous_issue_performance_num_score" property="previousIssuePerformanceNumScore" />
        <result column="previous_issue_closing_ratio_score" property="previousIssueClosingRatioScore" />
        <result column="previous_issue_cancel_ratio_score" property="previousIssueCancelRatioScore" />
        <result column="previous_issue_duty_complaint_num_score" property="previousIssueDutyComplaintNumScore" />
        <result column="previous_issue_task_no_complaint_score" property="previousIssueTaskNoComplaintScore" />
        <result column="previous_issue_task_no_cancel_score" property="previousIssueTaskNoCancelScore" />
        <result column="previous_issue_duplicate_goods_ratio_score" property="previousIssueDuplicateGoodsRatioScore" />
        <result column="previous_issue_credit_score" property="previousIssueCreditScore" />
        <result column="previous_issue_rank_level" property="previousIssueRankLevel" />
        <result column="car_credit_score" property="carCreditScore" />
        <result column="car_credit_rank_level" property="carCreditRankLevel" />
        <result column="car_credit_rank_leve_type" property="carCreditRankLeveType" />
        <result column="car_rank_num" property="carRankNum" />
        <result column="goods_cur_order_num" property="goodsCurOrderNum" />
        <result column="car_last_level_gap" property="carLastLevelGap" />
        <result column="car_last_month_rank_level" property="carLastMonthRankLevel" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        user_id, user_identity_type1, is_blacklist_user, fraud_count, transport_information_payment_count,
            vip_growth_days, transport_145_count, transport_succ_count, up_date, transport_score, ts_rank,
            identity_status_score, enterprise_auth_status_score, trans_score, boda_score, orders_score,
            lvyuelv_score, total_score, rank_level, last_level_gap, car_identity_status_score, car_enterprise_auth_status_score,
            car_identity_numbers_score, car_history_lvyue_score, car_recent_two_months_lvyue_score, car_total_server_score,
            car_server_rank_score, car_vip_growth_days, car_transport_information_payment_count, last_week_rank_level,
            complain_rate, performance_num_score, closing_ratio_score, cancel_ratio_score, duty_complaint_num_score,
            task_no_complaint_score, task_no_cancel_score, duplicate_goods_ratio_score, user_label_text, user_label_icon,
            previous_issue_performance_num_score, previous_issue_closing_ratio_score, previous_issue_cancel_ratio_score,
            previous_issue_duty_complaint_num_score, previous_issue_task_no_complaint_score, previous_issue_task_no_cancel_score,
            previous_issue_duplicate_goods_ratio_score, previous_issue_credit_score, previous_issue_rank_level,
            car_credit_score, car_credit_rank_level, car_credit_rank_leve_type, car_rank_num, goods_cur_order_num, car_last_level_gap
    </sql>

    <select id="getByUserId" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from api_data_user_credit_info_two
        where user_id = #{userId}
    </select>

</mapper>
