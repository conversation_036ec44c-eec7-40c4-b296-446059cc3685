<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.market.activity.mybatis.mapper.AutomaticInformRuleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.AutomaticInformRuleDO">
        <id column="id" property="id" />
        <result column="rule_name" property="ruleName" />
        <result column="rule_type" property="ruleType" />
        <result column="port" property="port" />
        <result column="effect_start_time" property="effectStartTime" />
        <result column="effect_end_time" property="effectEndTime" />
        <result column="rule_condition" property="ruleCondition" />
        <result column="days_start" property="daysStart" />
        <result column="days_end" property="daysEnd" />
        <result column="black_area" property="blackArea" />
        <result column="black_list_status" property="blackListStatus" />
        <result column="black_list_url" property="blackListUrl" />
        <result column="rule_status" property="ruleStatus" />
        <result column="operator" property="operator" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, rule_name, rule_type, port, effect_start_time, effect_end_time, rule_condition, days_start, days_end, black_area, black_list_status, black_list_url, rule_status, operator, create_time, modify_time
    </sql>

    <select id="getValidRule" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tyt_automatic_inform_rule where rule_status=2 and effect_start_time &lt; NOW() and effect_end_time &gt; NOW();
    </select>

</mapper>
