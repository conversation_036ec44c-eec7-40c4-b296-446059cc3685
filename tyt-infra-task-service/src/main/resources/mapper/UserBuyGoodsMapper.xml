<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.goods.mapper.UserBuyGoodsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.goods.entity.UserBuyGoodsDO">
        <id column="id" property="id" />
        <result column="order_id" property="orderId" />
        <result column="user_id" property="userId" />
        <result column="user_name" property="userName" />
        <result column="cell_phone" property="cellPhone" />
        <result column="goods_id" property="goodsId" />
        <result column="goods_name" property="goodsName" />
        <result column="goods_type" property="goodsType" />
        <result column="goods_status" property="goodsStatus" />
        <result column="original_price" property="originalPrice" />
        <result column="price" property="price" />
        <result column="total_fee" property="totalFee" />
        <result column="fee_amount" property="feeAmount" />
        <result column="orders_status" property="ordersStatus" />
        <result column="pay_channel" property="payChannel" />
        <result column="pay_sub_channel" property="paySubChannel" />
        <result column="ctime" property="ctime" />
        <result column="utime" property="utime" />
        <result column="remark" property="remark" />
        <result column="buy_path" property="buyPath" />
        <result column="begin_time" property="beginTime" />
        <result column="end_time" property="endTime" />
        <result column="refund_user_id" property="refundUserId" />
        <result column="refund_user_name" property="refundUserName" />
        <result column="refund_time" property="refundTime" />
        <result column="refund_arrival_time" property="refundArrivalTime" />
        <result column="refund_err_msg" property="refundErrMsg" />
        <result column="ts_order_no" property="tsOrderNo" />
        <result column="delayed_grant" property="delayedGrant" />
        <result column="delayed_status" property="delayedStatus" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_id, user_id, user_name, cell_phone, goods_id, goods_name, goods_type, goods_status,
            original_price, price, total_fee, fee_amount, orders_status, pay_channel, pay_sub_channel,
            ctime, utime, remark, buy_path, begin_time, end_time, refund_user_id, refund_user_name,
            refund_time, refund_arrival_time, refund_err_msg, ts_order_no, delayed_grant, delayed_status
    </sql>


    <update id="updatePaySubChannel">
        UPDATE tyt_user_buy_goods
        SET pay_sub_channel = #{payType}, utime = utime
        WHERE ctime >= #{orderBeginTime}
        AND order_id = #{orderId}
        AND user_id = #{userId}
        AND orders_status = 2
        AND pay_sub_channel = 'groupPay'
    </update>
    <update id="updateDelayedGoods">
        update tyt_user_buy_goods
        set delayed_status = 3,
            utime = now()
        where user_id = #{userId}
        and goods_type = #{goodsType}
        and delayed_grant = 1
        and delayed_status = 1
    </update>
    <select id="getDelayedGoods" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
            from tyt_user_buy_goods
        where goods_type = 1
        and orders_status = 2
        and delayed_grant = 1
        and delayed_status = 2
        <if test="indexId != null and indexId > 0">
            and id > #{indexId}
        </if>
        order by id
        limit 200
    </select>

    <select id="getEffectGoods" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from tyt_user_buy_goods
        where user_id = #{userId}
        and goods_type = 1
        and orders_status = 2
        and delayed_grant = 1
        and delayed_status = 1
        limit 1
    </select>

    <select id="getGoodsOrderByCondition" resultMap="BaseResultMap">
        select * from tyt_user_buy_goods where utime &gt;= #{startTime} and utime &lt;= #{endTime} and orders_status = #{orderStatus} and price &gt;0 order by user_id,goods_id,utime asc
    </select>

    <select id="getDelayedGoodsCountByUserId" resultType="java.lang.Integer">
        select count(*) from tyt_user_buy_goods
        where user_id = #{userId}
          and goods_type = 1
          and orders_status = 2
          and delayed_grant = 1
          and delayed_status = 2
    </select>
</mapper>
