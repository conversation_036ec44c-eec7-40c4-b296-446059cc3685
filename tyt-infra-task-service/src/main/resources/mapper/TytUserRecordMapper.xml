<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.info.mybatis.mapper.TytUserRecordMapper">
  <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.info.mybatis.entity.TytUserRecord">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>

  <insert id="saveUserRecord">
    insert into tyt_user_record (
        user_id, code, status, create_time, modify_time, remark
    ) values (
        #{userId},
        #{code},
        #{status},
        #{createTime},
        #{modifyTime},
        #{remark}
    ) on duplicate key update
        status = values(status), modify_time = now(), remark = values(remark)
  </insert>


    <select id="getUserRecordList" resultMap="BaseResultMap">
        select * from tyt_user_record
        where code = #{code}
          and status = #{status}
          and user_id in (${userIdStr})
    </select>

</mapper>