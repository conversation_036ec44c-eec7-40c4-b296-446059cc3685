<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.car.mybatis.mapper.SuperiorCarSignBlacklistLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.car.mybatis.entity.SuperiorCarSignBlacklistLogDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="is_always" property="isAlways" />
        <result column="restrict_num" property="restrictNum" />
        <result column="restrict_start_time" property="restrictStartTime" />
        <result column="restrict_end_time" property="restrictEndTime" />
        <result column="remark" property="remark" />
        <result column="operator_name" property="operatorName" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
    </sql>

    <insert id="insertList" parameterType="java.util.List">
        INSERT INTO tyt_superior_car_sign_blacklist_log ( user_id, is_always, restrict_num, restrict_start_time, restrict_end_time, remark, operator_name, create_time, modify_time)
        VALUES
        <foreach item="item" index="index" collection="list" open="(" separator="),(" close=")">
            #{item.userId}, #{item.isAlways}, #{item.restrictNum}, #{item.restrictStartTime}, #{item.restrictEndTime}, #{item.remark}, #{item.operatorName}, #{item.createTime}, #{item.modifyTime}
        </foreach>
    </insert>

</mapper>
