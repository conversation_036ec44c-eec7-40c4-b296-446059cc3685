<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.market.activity.mybatis.mapper.MarketingActivityMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.MarketingActivityDO">
        <id column="id" property="id" />
        <result column="activity_name" property="activityName" />
        <result column="is_need_popup" property="isNeedPopup" />
        <result column="popup_id_one" property="popupIdOne" />
        <result column="popup_id_two" property="popupIdTwo" />
        <result column="activity_scope" property="activityScope" />
        <result column="activity_type" property="activityType" />
        <result column="activity_type_name" property="activityTypeName" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="stat_time" property="statTime" />
        <result column="push_title" property="pushTitle" />
        <result column="push_summary" property="pushSummary" />
        <result column="push_content" property="pushContent" />
        <result column="push_type" property="pushType" />
        <result column="activity_part" property="activityPart" />
        <result column="is_need_show" property="isNeedShow" />
        <result column="activity_content" property="activityContent" />
        <result column="activity_url" property="activityUrl" />
        <result column="status" property="status" />
        <result column="remark" property="remark" />
        <result column="operater" property="operater" />
        <result column="ctime" property="ctime" />
        <result column="mtime" property="mtime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, activity_name, is_need_popup, popup_id_one, popup_id_two, activity_scope, activity_type, activity_type_name, start_time, end_time, stat_time, push_title, push_summary, push_content, push_type, activity_part, is_need_show, activity_content, activity_url, status, remark, operater, ctime, mtime
    </sql>

<!--    <select id="getEndActivity" resultType="com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.MarketingActivityDO">-->
<!--        select  id,-->
<!--                end_time as endTime-->
<!--        from marketing_activity-->
<!--        where end_time &lt; now() and activity_type = 20-->
<!--        order by id desc limit 5-->
<!--    </select>-->

    <select id="getByType" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
            from marketing_activity
            where activity_type = #{activityType}
              and status = 1
              and start_time &lt;= now()
              and end_time >= now()
    </select>
    <select id="getEndActivity" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from marketing_activity
        where end_time &lt; now()
        and activity_type = #{activityType}
        order by id desc
        limit 5
    </select>
</mapper>
