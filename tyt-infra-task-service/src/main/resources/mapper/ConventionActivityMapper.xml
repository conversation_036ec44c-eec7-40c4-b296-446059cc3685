<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.market.activity.mybatis.mapper.ConventionActivityMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.ConventionActivityDO">
        <id column="id" property="id" />
        <result column="activity_id" property="activityId" />
        <result column="user_id" property="userId" />
        <result column="user_cell_phone" property="userCellPhone" />
        <result column="order_num" property="orderNum" />
        <result column="target_prize" property="targetPrize" />
        <result column="prize" property="prize" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
        <result column="last_finish_time" property="lastFinishTime" />
        <result column="receive_flag" property="receiveFlag" />
        <result column="security_prize_id" property="securityPrizeId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, activity_id, user_id, user_cell_phone, order_num, target_prize, prize, create_time, update_time, is_deleted, last_finish_time, receive_flag, security_prize_id
    </sql>

    <select id="getAwardsUser" resultMap="BaseResultMap">
        SELECT id,
               activity_id as activityId,
               user_id as userId,
               user_cell_phone as userCellPhone,
               prize
        FROM tyt_convention_activity
        WHERE  activity_id = #{activityId} and prize > 0
        group by user_id
        order by id desc
    </select>
    <select id="getByActivityId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tyt_convention_activity
        WHERE  activity_id = #{activityId}
          and prize > 0
        group by user_id
        order by id desc
    </select>
    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tyt_convention_activity
        WHERE user_id = #{userId}
        AND activity_id = #{activityId}
        limit 1
    </select>
    <update id="updateConvention">
        UPDATE tyt_convention_activity
        SET order_num = #{orderNum},
            target_prize = #{prize},
            prize = #{prize},
            last_finish_time = now(),
            update_time = now()
        WHERE user_id = #{userId}
          AND activity_id = #{activityId}
    </update>
    <update id="updateConventionNum">
        UPDATE tyt_convention_activity
        SET order_num = #{orderNum},
            last_finish_time = now(),
            update_time = now()
        WHERE user_id =  #{userId}
          AND activity_id = #{activityId}
    </update>

</mapper>
