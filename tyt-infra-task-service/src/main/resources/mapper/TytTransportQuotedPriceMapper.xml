<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.goods.goodsfee.mapper.TytTransportQuotedPriceMapper">
	<resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.goods.goodsfee.entity.TytTransportQuotedPrice">
		<!--
		  WARNING - @mbg.generated
		-->
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="car_id" jdbcType="BIGINT" property="carId" />
		<result column="car_user_name" jdbcType="VARCHAR" property="carUserName" />
		<result column="transport_user_id" jdbcType="BIGINT" property="transportUserId" />
		<result column="transport_user_name" jdbcType="VARCHAR" property="transportUserName" />
		<result column="src_msg_id" jdbcType="BIGINT" property="srcMsgId" />
		<result column="car_quoted_price" jdbcType="INTEGER" property="carQuotedPrice" />
		<result column="transport_quoted_price" jdbcType="INTEGER" property="transportQuotedPrice" />
		<result column="car_is_done" jdbcType="INTEGER" property="carIsDone" />
		<result column="transport_is_done" jdbcType="INTEGER" property="transportIsDone" />
		<result column="final_quoted_price_is_done" jdbcType="INTEGER" property="finalQuotedPriceIsDone" />
		<result column="final_quoted_price" jdbcType="INTEGER" property="finalQuotedPrice" />
		<result column="final_quoted_price_type" jdbcType="INTEGER" property="finalQuotedPriceType" />
		<result column="car_quoted_price_time" jdbcType="TIMESTAMP" property="carQuotedPriceTime" />
		<result column="transport_quoted_price_time" jdbcType="TIMESTAMP" property="transportQuotedPriceTime" />
		<result column="car_quoted_price_times" jdbcType="INTEGER" property="carQuotedPriceTimes" />
		<result column="transport_quoted_price_times" jdbcType="INTEGER" property="transportQuotedPriceTimes" />
		<result column="quoted_type" jdbcType="INTEGER" property="quotedType" />
		<result column="transport_num" jdbcType="INTEGER" property="transportNum" />
	</resultMap>

	<select id="getTransportQuotedPriceFiveMinNoResponsesData" resultMap="BaseResultMap" parameterType="date">
		select id, car_id, car_user_name, transport_user_id, transport_user_name, src_msg_id, car_quoted_price, transport_quoted_price, car_is_done, transport_is_done, final_quoted_price_is_done, final_quoted_price, final_quoted_price_type, car_quoted_price_time, transport_quoted_price_time, car_quoted_price_times, transport_quoted_price_times
		from tyt_transport_quoted_price where final_quoted_price_is_done = 0 and car_quoted_price_times > transport_quoted_price_times and car_quoted_price_time between #{beginTime} and #{endTime} limit 200
	</select>

	<select id="getQuotedPriceRecord"
	        resultType="com.teyuntong.infra.task.service.biz.goods.goodsfee.entity.TytTransportQuotedPrice">
		select id,
		       car_quoted_price as carQuotedPrice
		from tyt_transport_quoted_price
		where src_msg_id = #{srcMsgId} and car_id != -1
		order by id desc
		limit 1
	</select>

	<select id="querySystemQuotedList" resultType="java.lang.Long">
		select src_msg_id
		from tyt_transport_quoted_price
		where quoted_type = 1 and src_msg_id in
		<foreach collection="srcMsgIdList" separator="," open="(" close=")" item="item">
			#{item}
		</foreach>
	</select>

</mapper>