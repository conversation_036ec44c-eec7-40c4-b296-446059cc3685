<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.market.activity.mybatis.mapper.ConventionActivityPrizeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.ConventionActivityPrizeDO">
        <id column="id" property="id" />
        <result column="activity_id" property="activityId" />
        <result column="min_nums" property="minNums" />
        <result column="max_nums" property="maxNums" />
        <result column="goods_id" property="goodsId" />
        <result column="goods_name" property="goodsName" />
        <result column="type" property="type" />
        <result column="ctime" property="ctime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, activity_id, min_nums, max_nums, goods_id, goods_name, type, ctime
    </sql>

    <select id="getByActivityId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
            from convention_activity_prize where activity_id=#{activityId} and type=#{type}
    </select>

</mapper>
