<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.info.mybatis.mapper.DwsNewIdentityTwoDataMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.info.mybatis.entity.DwsNewIdentityTwoDataDO">
        <id column="id" property="id" />
        <result column="cal_dt" property="calDt" />
        <result column="user_id" property="userId" />
        <result column="type" property="type" />
        <result column="main_name" property="mainName" />
        <result column="user_follow" property="userFollow" />
        <result column="auth_status" property="authStatus" />
        <result column="ctime" property="ctime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, cal_dt, user_id, type, main_name, user_follow, auth_status, ctime
    </sql>
    <select id="getByUserId"
            resultType="com.teyuntong.infra.task.service.biz.user.info.mybatis.entity.DwsNewIdentityTwoDataDO">
        select <include refid="Base_Column_List"/>
        from dws_new_identity_two_data
        where user_id = #{userId}
    </select>

</mapper>
