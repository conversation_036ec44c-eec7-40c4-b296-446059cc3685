<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.trade.infofee.mapper.RiskTrackBaseDataMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.trade.infofee.entity.RiskTrackBaseData">
        <id column="id" property="id" />
        <result column="order_id" property="orderId" />
        <result column="goods_distance" property="goodsDistance" />
        <result column="goods_start_longitude" property="goodsStartLongitude" />
        <result column="goods_start_latitude" property="goodsStartLatitude" />
        <result column="actual_start_upload_type" property="actualStartUploadType" />
        <result column="into_start_longitude" property="intoStartLongitude" />
        <result column="into_start_latitude" property="intoStartLatitude" />
        <result column="into_start_distance" property="intoStartDistance" />
        <result column="into_loading_time" property="intoLoadingTime" />
        <result column="leave_start_longitude" property="leaveStartLongitude" />
        <result column="leave_start_latitude" property="leaveStartLatitude" />
        <result column="leave_start_distance" property="leaveStartDistance" />
        <result column="leave_loading_time" property="leaveLoadingTime" />
        <result column="leave_start_confirm" property="leaveStartConfirm" />
        <result column="actual_start_longitude" property="actualStartLongitude" />
        <result column="actual_start_latitude" property="actualStartLatitude" />
        <result column="actual_start_distance" property="actualStartDistance" />
        <result column="actual_loading_time" property="actualLoadingTime" />
        <result column="actual_start_confirm" property="actualStartConfirm" />
        <result column="goods_dest_longitude" property="goodsDestLongitude" />
        <result column="goods_dest_latitude" property="goodsDestLatitude" />
        <result column="actual_dest_upload_type" property="actualDestUploadType" />
        <result column="into_dest_longitude" property="intoDestLongitude" />
        <result column="into_dest_latitude" property="intoDestLatitude" />
        <result column="into_dest_distance" property="intoDestDistance" />
        <result column="into_unload_time" property="intoUnloadTime" />
        <result column="leave_dest_longitude" property="leaveDestLongitude" />
        <result column="leave_dest_latitude" property="leaveDestLatitude" />
        <result column="leave_dest_distance" property="leaveDestDistance" />
        <result column="leave_unload_time" property="leaveUnloadTime" />
        <result column="leave_dest_confirm" property="leaveDestConfirm" />
        <result column="actual_dest_longitude" property="actualDestLongitude" />
        <result column="actual_dest_latitude" property="actualDestLatitude" />
        <result column="actual_dest_distance" property="actualDestDistance" />
        <result column="actual_unload_time" property="actualUnloadTime" />
        <result column="actual_dest_confirm" property="actualDestConfirm" />
        <result column="risk_control_status" property="riskControlStatus" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_id, goods_distance, goods_start_longitude, goods_start_latitude,actual_start_upload_type,
        into_start_longitude,into_start_latitude,into_start_distance,into_loading_time,
        leave_start_longitude,leave_start_latitude,leave_start_distance,leave_loading_time, leave_start_confirm,
        actual_start_longitude, actual_start_latitude, actual_start_distance, actual_loading_time, actual_start_confirm,
        goods_dest_longitude, goods_dest_latitude, actual_dest_upload_type,
        into_dest_longitude, into_dest_latitude, into_dest_distance, into_unload_time,
        leave_dest_longitude ,leave_dest_latitude, leave_dest_distance, leave_unload_time,leave_dest_confirm,
        actual_dest_longitude, actual_dest_latitude, actual_dest_distance, actual_unload_time, actual_dest_confirm,
        risk_control_status, create_time, update_time
    </sql>

    <select id="getRiskTrackBaseDataByOrderId" resultType="com.teyuntong.infra.task.service.biz.trade.infofee.entity.RiskTrackBaseData">
        select
            id id,
            order_id orderId,
            goods_start_longitude goodsStartLongitude,
            goods_start_latitude goodsStartLatitude,
            actual_start_longitude actualStartLongitude,
            actual_start_latitude actualStartLatitude,
            actual_start_confirm actualStartConfirm,
            actual_dest_confirm actualDestConfirm,
            actual_loading_time actualLoadingTime,
            actual_start_distance actualStartDistance,
            into_loading_time intoLoadingTime,
            leave_loading_time leaveLoadingTime,
            into_unload_time intoUnloadTime,
            leave_unload_time actualUnloadTime,
            create_time createTime,
            update_time updateTime,
                risk_control_status riskControlStatus
        from risk_track_base_data
        where order_id = #{orderId}
        order by create_time desc limit 1
    </select>

    <select id="getRiskTrackNeedDestConfirm" resultType="com.teyuntong.infra.task.service.biz.trade.infofee.entity.RiskTrackBaseData">
        select
        tbd.id id,
        tbd.order_id orderId,
        tbd.goods_dest_longitude goodsDestLongitude,
        tbd.goods_dest_latitude goodsDestLatitude,
        tbd.actual_dest_confirm actualDestConfirm,
        tbd.actual_unload_time actualUnloadTime,
        tbd.actual_dest_distance actualDestDistance,
        tbd.into_loading_time intoLoadingTime,
        tbd.leave_loading_time leaveLoadingTime,
        tbd.into_unload_time intoUnloadTime,
        tbd.leave_unload_time leaveUnloadTime,
        tbd.risk_control_status riskControlStatus
        from risk_track_base_data  tbd left join tyt_transport_orders tto on tbd.order_id=tto.id
        where tbd.actual_start_distance>0 and tbd.actual_start_distance&lt;=#{distanceFence}
        and tbd.actual_dest_confirm=0  and  tbd.create_time >=  DATE_SUB(now(), INTERVAL 1 MONTH) and tto.order_new_status in (15,20)
    </select>

    <update id="updateRiskControlStatus">
        update risk_track_base_data set risk_control_status = #{status},update_time = now() where id = #{id}
    </update>

    <select id="getNeedAutoPickGoodsOrder" resultType="com.teyuntong.infra.task.service.biz.trade.infofee.entity.RiskTrackBaseData">
        select
        tbd.order_id orderId,
        tbd.actual_start_longitude actualStartLongitude,
        tbd.actual_start_latitude actualStartLatitude,
        tbd.actual_start_confirm actualStartConfirm,
        tbd.actual_loading_time actualLoadingTime,
        tbd.actual_start_distance actualStartDistance
        from risk_track_base_data tbd left join tyt_transport_orders tto on tbd.order_id=tto.id
        left join tyt_transport_order_snapshot tos on tto.id=tos.order_id
        where tbd.actual_start_confirm=1 and tbd.actual_start_distance&lt;=#{distanceFence} and  tbd.update_time >=  DATE_SUB(now(), INTERVAL 1 HOUR) and tto.order_new_status=15 and tos.invoice_goods_source='ORDINARY'
    </select>


    <select id="getNeedAutoUnLoadOrder" resultType="com.teyuntong.infra.task.service.biz.trade.infofee.entity.RiskTrackBaseData">
        select
            tbd.order_id orderId,
            tbd.actual_dest_longitude actualDestLongitude,
            tbd.actual_dest_latitude actualDestLatitude,
            tbd.actual_dest_confirm actualDestConfirm,
            tbd.actual_unload_time actualUnloadTime,
            tbd.actual_dest_distance actualDestDistance
        from risk_track_base_data tbd left join tyt_transport_orders tto on tbd.order_id=tto.id
                                      left join tyt_transport_order_snapshot tos on tto.id=tos.order_id
        where tbd.actual_dest_confirm=1 and tbd.actual_dest_distance&lt;=#{distanceFence} and  tbd.update_time >=  DATE_SUB(now(), INTERVAL 1 HOUR) and tto.order_new_status=20 and tto.invoice_service_code='HBWJ' and tos.invoice_goods_source='ORDINARY'
    </select>




    <select id="getRiskTrackNeedLeaveStartConfirm"
            resultType="com.teyuntong.infra.task.service.biz.trade.infofee.entity.RiskTrackBaseData">
        select
        tbd.id id,
        tbd.order_id orderId,
        tbd.goods_start_longitude goodsStartLongitude,
        tbd.goods_start_latitude goodsStartLatitude,
        tbd.goods_dest_longitude goodsDestLongitude,
        tbd.goods_dest_latitude goodsDestLatitude
        from risk_track_base_data  tbd left join tyt_transport_orders tto on tbd.order_id=tto.id
        where tbd.actual_start_confirm=1 and tbd.leave_start_confirm=0 and tbd.actual_dest_confirm=0 and  tbd.update_time >=  DATE_SUB(now(),INTERVAL 2 day) and tto.order_new_status in (15,20)
    </select>

    <select id="getRiskTrackNeedLeaveDestConfirm"
            resultType="com.teyuntong.infra.task.service.biz.trade.infofee.entity.RiskTrackBaseData">
        select
        tbd.id id,
        tbd.order_id orderId,
        tbd.goods_dest_longitude goodsDestLongitude,
        tbd.goods_dest_latitude goodsDestLatitude
        from risk_track_base_data  tbd left join tyt_transport_orders tto on tbd.order_id=tto.id
        where tbd.actual_dest_confirm=1  and tbd.leave_dest_confirm=0  and  tbd.update_time >=  DATE_SUB(now(),INTERVAL 2 day) and tto.order_new_status in (15,20)
    </select>


</mapper>
