<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.config.mybatis.mapper.TytConfigMapper">
  <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.config.mybatis.entity.TytConfig">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="value" jdbcType="VARCHAR" property="value" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="parent_type" jdbcType="VARCHAR" property="parentType" />
    <result column="sub_type" jdbcType="VARCHAR" property="subType" />
  </resultMap>

  <select id="getStringDataByConfigName" resultType="string">
    select value
    from tyt_config where name = #{configName}
  </select>

  <select id="getList" resultMap="BaseResultMap">
    select * from tyt_config
  </select>

  <select id="selectByName" resultMap="BaseResultMap">
    select *
    from tyt_config
    where name = #{name}
  </select>
</mapper>