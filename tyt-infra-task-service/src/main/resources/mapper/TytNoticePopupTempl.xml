<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.coupon.mybatis.mapper.TytNoticePopupTemplMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.coupon.mybatis.entity.TytNoticePopupTempl">
        <id column="id" property="id" />
        <result column="type1" property="type1" />
        <result column="type2" property="type2" />
        <result column="remark" property="remark" />
        <result column="style" property="style" />
        <result column="title" property="title" />
        <result column="title_color" property="titleColor" />
        <result column="master_content" property="masterContent" />
        <result column="master_content_color" property="masterContentColor" />
        <result column="left_button_content" property="leftButtonContent" />
        <result column="left_button_link" property="leftButtonLink" />
        <result column="left_button_type" property="leftButtonType" />
        <result column="left_button_backcolor" property="leftButtonBackcolor" />
        <result column="left_button_content_color" property="leftButtonContentColor" />
        <result column="right_button_content" property="rightButtonContent" />
        <result column="right_button_type" property="rightButtonType" />
        <result column="right_button_link" property="rightButtonLink" />
        <result column="right_button_backcolor" property="rightButtonBackcolor" />
        <result column="right_button_content_color" property="rightButtonContentColor" />
        <result column="guide_content" property="guideContent" />
        <result column="guide_content_type" property="guideContentType" />
        <result column="guide_content_color" property="guideContentColor" />
        <result column="guide_content_backcolor" property="guideContentBackcolor" />
        <result column="guide_content_link" property="guideContentLink" />
        <result column="guide_button_content" property="guideButtonContent" />
        <result column="guide_button_content_color" property="guideButtonContentColor" />
        <result column="guide_button_backcolor" property="guideButtonBackcolor" />
        <result column="guide_button_type" property="guideButtonType" />
        <result column="guide_button_link" property="guideButtonLink" />
        <result column="picture_addr" property="pictureAddr" />
        <result column="status" property="status" />
        <result column="ctime" property="ctime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, type1, type2, remark, style, title, title_color, master_content, master_content_color, left_button_content, left_button_link, left_button_type, left_button_backcolor, left_button_content_color, right_button_content, right_button_type, right_button_link, right_button_backcolor, right_button_content_color, guide_content, guide_content_type, guide_content_color, guide_content_backcolor, guide_content_link, guide_button_content, guide_button_content_color, guide_button_backcolor, guide_button_type, guide_button_link, picture_addr, status, ctime
    </sql>

    <select id="getByType" resultMap="BaseResultMap">
        select * from tyt_notice_popup_templ WHERE type1 = #{type1} AND type2 = #{type2}
    </select>
</mapper>
