<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.market.activity.mybatis.mapper.MarketingActivityUserTargetNumMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.MarketingActivityUserTargetNumDO">
        <id column="id" property="id" />
        <result column="activity_id" property="activityId" />
        <result column="activity_user_id" property="activityUserId" />
        <result column="one_grade" property="oneGrade" />
        <result column="one_target_num" property="oneTargetNum" />
        <result column="two_grade" property="twoGrade" />
        <result column="two_target_num" property="twoTargetNum" />
        <result column="three_grade" property="threeGrade" />
        <result column="three_target_num" property="threeTargetNum" />
        <result column="four_grade" property="fourGrade" />
        <result column="four_target_num" property="fourTargetNum" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
        <result column="one_prize_num"  property="onePrizeNum" />
        <result column="two_prize_num"  property="twoPrizeNum" />
        <result column="three_prize_num"  property="threePrizeNum" />
        <result column="four_prize_num"  property="fourPrizeNum" />
        <result column="reward_prize_num"  property="rewardPrizeNum" />
        <result column="reward_extra_prize_num"  property="rewardExtraPrizeNum" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, activity_id, activity_user_id, one_grade, one_target_num, two_grade,
            two_target_num, three_grade, three_target_num, four_grade, four_target_num,
            create_time, modify_time, one_prize_num, two_prize_num, three_prize_num, four_prize_num,
            reward_prize_num, reward_extra_prize_num
    </sql>
    <select id="getByActivityId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
            from marketing_activity_user_target_num
        where activity_id = #{activityId}
        and id > #{id}
        order by id limit 500
    </select>


    <select id="getNumByuserId" resultType="java.lang.Integer">
        select SUM(one_prize_num + two_prize_num + three_prize_num + four_prize_num + reward_prize_num + reward_extra_prize_num) AS total_sales
        from marketing_activity_user_target_num where activity_id = #{activityId} and activity_user_id = #{userId}
    </select>

    <select id="getByActivityUserId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from marketing_activity_user_target_num where activity_id = #{activityId} and activity_user_id = #{userId}
    </select>
    <select id="getActivityUser"
            resultType="com.teyuntong.infra.task.service.biz.market.activity.dto.ActivityGradeBean">
        select user_id as userId,user_grade as grade
        from marketing_activity_user
        where  user_id in
        <foreach item="userId" collection="userIds" open="(" separator="," close=")">
            #{userId}
        </foreach>
          and activity_id = #{activityId}
          and is_delete = 1
          and user_grade is not null
    </select>

</mapper>
