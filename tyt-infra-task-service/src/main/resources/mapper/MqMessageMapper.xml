<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.common.mq.mybatis.mapper.MqMessageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.common.mq.mybatis.entity.MqMessageDO">
        <id column="id" property="id"/>
        <result column="message_serial_num" property="messageSerialNum"/>
        <result column="message_content" property="messageContent"/>
        <result column="deal_status" property="dealStatus"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="message_type" property="messageType"/>
        <result column="send_nbr" property="sendNbr"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, message_serial_num, message_content, deal_status, create_time, update_time, message_type, send_nbr
    </sql>

    <select id="getMqMessageByStatus" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tyt_mq_message tmm
        where tmm.deal_status = #{mqStatus}
        and tmm.create_time &gt;= #{startTime}
        and tmm.create_time &lt;= #{endTime}
    </select>

    <update id="updateMqMessageSendNbr">
        update tyt_mq_message set send_nbr=send_nbr+1 where message_serial_num=#{messageSerailNum}
    </update>

    <update id="updateMqMessageStatus">
        update tyt_mq_message set deal_status=#{mqMessageStatus} WHERE message_serial_num=#{messageSerailNum}
    </update>

    <select id="getMqMessageByStatusForDate"
            resultType="com.teyuntong.infra.task.service.common.mq.mybatis.entity.MqMessageDO">
        SELECT tmm.`create_time` AS 'createTime', tmm.`deal_status` AS 'dealStatus', tmm.`id`, tmm.`message_content` AS
        'messageContent', tmm.`message_serial_num` AS 'messageSerialNum', tmm.`message_type` AS 'messageType',
        tmm.`update_time` AS 'updateTime' FROM tyt_mq_message tmm WHERE tmm.`deal_status`=#{dealStatus} and
        tmm.create_time >= #{beginTime} and tmm.create_time &lt;= #{endTime}
    </select>

    <update id="updateDealFailMqMessage">
        UPDATE `tyt_mq_message` SET `deal_status`=3,`send_nbr`=2,`update_time`=NOW() WHERE message_type=#{messageType}
        AND deal_status = 4 AND create_time>#{beginTime} AND create_time &lt; #{endTime}
    </update>

</mapper>
