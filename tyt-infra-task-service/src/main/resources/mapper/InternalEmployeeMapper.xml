<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.trade.infofee.mapper.InternalEmployeeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.trade.infofee.entity.InternalEmployeeDO">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="login_phone_no" property="loginPhoneNo" />
        <result column="password" property="password" />
        <result column="real_name" property="realName" />
        <result column="email" property="email" />
        <result column="institution_id" property="institutionId" />
        <result column="department_id" property="departmentId" />
        <result column="up_leader_name" property="upLeaderName" />
        <result column="change_leader_name" property="changeLeaderName" />
        <result column="is_valid" property="isValid" />
        <result column="last_login_time" property="lastLoginTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="last_updater" property="lastUpdater" />
        <result column="position_id" property="positionId" />
        <result column="online_status" property="onlineStatus" />
        <result column="receive_order_status" property="receiveOrderStatus" />
        <result column="data_institution_type" property="dataInstitutionType" />
        <result column="work_phone" property="workPhone" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, login_phone_no, password, real_name, email, institution_id, department_id, up_leader_name, change_leader_name, is_valid, last_login_time, create_time, update_time, last_updater, position_id, online_status, receive_order_status, data_institution_type, work_phone
    </sql>

</mapper>
