<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.pay.mapper.PayRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.pay.entity.PayRecordDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="order_id" property="orderId" />
        <result column="pay_time" property="payTime" />
        <result column="pay_type" property="payType" />
        <result column="phoneNum" property="phoneNum" />
        <result column="years" property="years" />
        <result column="terminal" property="terminal" />
        <result column="pay_channel" property="payChannel" />
        <result column="deal_status" property="dealStatus" />
        <result column="comm_result" property="commResult" />
        <result column="remark" property="remark" />
        <result column="dealer" property="dealer" />
        <result column="deal_time" property="dealTime" />
        <result column="ctime" property="ctime" />
        <result column="utime" property="utime" />
        <result column="status" property="status" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, order_id, pay_time, pay_type, phoneNum, years, terminal, pay_channel, deal_status, comm_result, remark, dealer, deal_time, ctime, utime, status
    </sql>

</mapper>
