<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.customerservice.mapper.CsPollOrderConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.customerservice.entity.CsPollOrderConfig">
        <id column="id" property="id" />
        <result column="poll_order_switch" property="pollOrderSwitch" />
        <result column="people_upper_limit" property="peopleUpperLimit" />
        <result column="limit_start_time" property="limitStartTime" />
        <result column="limit_end_time" property="limitEndTime" />
        <result column="poll_order_user_list" property="pollOrderUserList" />
        <result column="operate_user_id" property="operateUserId" />
        <result column="operate_user_name" property="operateUserName" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, poll_order_switch, people_upper_limit, limit_start_time, limit_end_time, poll_order_user_list, operate_user_id, operate_user_name, create_time, modify_time
    </sql>

    <select id="getConfig" resultMap="BaseResultMap">
        select * from cs_poll_order_config order by id desc limit 1
    </select>

</mapper>
