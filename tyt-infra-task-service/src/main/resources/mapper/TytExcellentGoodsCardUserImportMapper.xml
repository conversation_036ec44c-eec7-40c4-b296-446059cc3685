<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.permission.mybatis.mapper.TytExcellentGoodsCardUserImportMapper">
  <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.permission.mybatis.entity.TytExcellentGoodsCardUserImport">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="config_id" jdbcType="BIGINT" property="configId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="first_refresh_times" jdbcType="INTEGER" property="firstRefreshTimes" />
    <result column="first_refresh_interval" jdbcType="INTEGER" property="firstRefreshInterval" />
    <result column="second_refresh_times" jdbcType="INTEGER" property="secondRefreshTimes" />
    <result column="second_refresh_interval" jdbcType="INTEGER" property="secondRefreshInterval" />
    <result column="plan_grant_num" jdbcType="INTEGER" property="planGrantNum" />
    <result column="reality_grant_num" jdbcType="INTEGER" property="realityGrantNum" />
    <result column="valid_date_begin" jdbcType="TIMESTAMP" property="validDateBegin" />
    <result column="valid_date_end" jdbcType="TIMESTAMP" property="validDateEnd" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="create_user_id" jdbcType="BIGINT" property="createUserId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="update_user_id" jdbcType="BIGINT" property="updateUserId" />
  </resultMap>

  <select id="getYesterdayNewExcellentGoodsCardUserIdCount" resultType="java.lang.Integer">
    select count(distinct user_id) from tyt_excellent_goods_card_user_import
    where type = 3
      and update_time &gt;= #{yesterdayBeginTime}
      and update_time &lt; #{todayBeginTime}
    order by user_id
  </select>

  <select id="getYesterdayNewExcellentGoodsCardUserIdListPerPage" resultType="java.lang.Long">
    select distinct user_id from tyt_excellent_goods_card_user_import
      where user_id &gt; #{lastUserId} and type = 3
        and update_time &gt;= #{yesterdayBeginTime}
        and update_time &lt; #{todayBeginTime}
      order by user_id limit #{pageSize}
  </select>

  <select id="getYesterdayNewExcellentGoodsCardByUserId" resultType="java.lang.Long">
    select id from tyt_excellent_goods_card_user_import
    where user_id =  #{userId} and type = 3
      and update_time &gt;= #{yesterdayBeginTime}
      and update_time &lt; #{todayBeginTime}
  </select>

  <select id="getNearExpiredExcellentGoodsCardUserIdCount" resultType="java.lang.Integer">
    select count(distinct user_id) from tyt_excellent_goods_card_user_import
    where type = 3
      and valid_date_begin &lt;= #{todayEndTime}
      and valid_date_end &gt;= #{todayEndTime}
      and valid_date_end &lt;= #{expiredEndTime}
  </select>

  <select id="getExcellentGoodsCardNearExpiredUserIdListPerPage" resultType="java.lang.Long">
    select distinct user_id from tyt_excellent_goods_card_user_import
    where user_id &gt; #{lastUserId} and type = 3
      and valid_date_begin &lt;= #{todayEndTime}
      and valid_date_end &gt;= #{todayEndTime}
      and valid_date_end &lt;= #{expiredEndTime}
    order by user_id limit #{pageSize}
  </select>

  <select id="getNearExpiredExcellentGoodsCardByUserId" resultType="java.lang.Long">
    select id from tyt_excellent_goods_card_user_import
    where user_id = #{userId} and type = 3
      and valid_date_begin &lt;= #{todayEndTime}
      and valid_date_end &gt;= #{todayEndTime}
      and valid_date_end &lt;= #{expiredEndTime}
  </select>

</mapper>