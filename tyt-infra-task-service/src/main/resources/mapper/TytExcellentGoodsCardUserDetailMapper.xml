<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.permission.mybatis.mapper.TytExcellentGoodsCardUserDetailMapper">
  <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.permission.mybatis.entity.TytExcellentGoodsCardUserDetail">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="config_id" jdbcType="BIGINT" property="configId" />
    <result column="import_id" jdbcType="BIGINT" property="importId" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="first_refresh_times" jdbcType="INTEGER" property="firstRefreshTimes" />
    <result column="first_refresh_interval" jdbcType="INTEGER" property="firstRefreshInterval" />
    <result column="second_refresh_times" jdbcType="INTEGER" property="secondRefreshTimes" />
    <result column="second_refresh_interval" jdbcType="INTEGER" property="secondRefreshInterval" />
    <result column="valid_date_begin" jdbcType="TIMESTAMP" property="validDateBegin" />
    <result column="valid_date_end" jdbcType="TIMESTAMP" property="validDateEnd" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="create_user_id" jdbcType="BIGINT" property="createUserId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="update_user_id" jdbcType="BIGINT" property="updateUserId" />
  </resultMap>

  <select id="getCanUseMinRefreshIntervalInDetailRefreshIntervalByImportIdsAndUserId" resultType="com.teyuntong.infra.task.service.biz.user.permission.mybatis.entity.CanUseMinRefreshIntervalInDetailRefreshIntervalVo">
    select min(first_refresh_interval) as minFirstRefreshInterval, min(second_refresh_interval) as minSecondRefreshInterval
    from tyt_excellent_goods_card_user_detail
    where user_id = #{userId}
      and import_id in (
        <foreach collection="tytExcellentGoodsCardUserImportId" item="item" separator=",">
          #{item}
        </foreach>
      )
      and type in (1, 2)
  </select>

</mapper>