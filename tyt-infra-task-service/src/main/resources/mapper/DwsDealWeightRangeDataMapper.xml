<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.goods.goodsinfo.mapper.DwsDealWeightRangeDataMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.goods.goodsinfo.entity.DwsDealWeightRangeData">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="max_weight" property="maxWeight" />
        <result column="min_weight" property="minWeight" />
        <result column="ctime" property="ctime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, max_weight, min_weight, ctime
    </sql>

    <select id="queryByUserId" resultMap="BaseResultMap">
        select *
        from dws_deal_weight_range_data
        where user_id = #{userId} limit 1
    </select>

</mapper>
