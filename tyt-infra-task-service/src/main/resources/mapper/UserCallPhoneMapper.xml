<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.goods.callphone.mapper.UserCallPhoneMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.goods.callphone.entity.UserCallPhoneDO">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="ts_id" property="tsId"/>
        <result column="create_time" property="createTime"/>
        <result column="client_version" property="clientVersion"/>
        <result column="client_sign" property="clientSign"/>
        <result column="mudule_type" property="muduleType"/>
        <result column="action_type" property="actionType"/>
        <result column="path" property="path"/>
    </resultMap>


    <select id="countBySrcMsgId" resultType="java.lang.Integer">
        select count(1)
        from tyt_user_call_phone
        where ts_id = #{srcMsgId}
    </select>

</mapper>
