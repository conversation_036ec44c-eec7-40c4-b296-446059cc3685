<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.goods.goodsinfo.mapper.TransportBackendMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.goods.goodsinfo.entity.TransportBackendDO">
        <id column="id" property="id" />
        <result column="src_msg_id" property="srcMsgId" />
        <result column="start_point" property="startPoint" />
        <result column="dest_point" property="destPoint" />
        <result column="task_content" property="taskContent" />
        <result column="tel" property="tel" />
        <result column="status" property="status" />
        <result column="order_status" property="orderStatus" />
        <result column="is_precise_push" property="isPrecisePush" />
        <result column="source" property="source" />
        <result column="ctime" property="ctime" />
        <result column="mtime" property="mtime" />
        <result column="price" property="price" />
        <result column="user_id" property="userId" />
        <result column="applets_user_id" property="appletsUserId" />
        <result column="receiver_user_id" property="receiverUserId" />
        <result column="receiver_phone" property="receiverPhone" />
        <result column="receiving_time" property="receivingTime" />
        <result column="carriage_time" property="carriageTime" />
        <result column="complete_time" property="completeTime" />
        <result column="cancel_time" property="cancelTime" />
        <result column="receiver_show_name" property="receiverShowName" />
        <result column="hash_code" property="hashCode" />
        <result column="start_coord_x" property="startCoordX" />
        <result column="start_coord_y" property="startCoordY" />
        <result column="dest_coord_x" property="destCoordX" />
        <result column="dest_coord_y" property="destCoordY" />
        <result column="start_longitude" property="startLongitude" />
        <result column="start_latitude" property="startLatitude" />
        <result column="dest_longitude" property="destLongitude" />
        <result column="dest_latitude" property="destLatitude" />
        <result column="start_detail_add" property="startDetailAdd" />
        <result column="dest_detail_add" property="destDetailAdd" />
        <result column="weight" property="weight" />
        <result column="length" property="length" />
        <result column="wide" property="wide" />
        <result column="high" property="high" />
        <result column="remark" property="remark" />
        <result column="tel3" property="tel3" />
        <result column="tel4" property="tel4" />
        <result column="start_city" property="startCity" />
        <result column="start_provinc" property="startProvinc" />
        <result column="start_area" property="startArea" />
        <result column="dest_provinc" property="destProvinc" />
        <result column="dest_city" property="destCity" />
        <result column="dest_area" property="destArea" />
        <result column="type" property="type" />
        <result column="brand" property="brand" />
        <result column="good_type_name" property="goodTypeName" />
        <result column="loading_time" property="loadingTime" />
        <result column="unload_time" property="unloadTime" />
        <result column="car_min_length" property="carMinLength" />
        <result column="car_max_length" property="carMaxLength" />
        <result column="car_type" property="carType" />
        <result column="car_style" property="carStyle" />
        <result column="work_plane_min_high" property="workPlaneMinHigh" />
        <result column="work_plane_max_high" property="workPlaneMaxHigh" />
        <result column="work_plane_min_length" property="workPlaneMinLength" />
        <result column="work_plane_max_length" property="workPlaneMaxLength" />
        <result column="climb" property="climb" />
        <result column="batch" property="batch" />
        <result column="is_valid" property="isValid" />
        <result column="push_type" property="pushType" />
        <result column="wx_template_id" property="wxTemplateId" />
        <result column="wx_is_push" property="wxIsPush" />
        <result column="order_no" property="orderNo" />
        <result column="cancel_confirm" property="cancelConfirm" />
        <result column="find_car_type" property="findCarType" />
        <result column="addr_matching_status" property="addrMatchingStatus" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, src_msg_id, start_point, dest_point, task_content, tel, status, order_status, is_precise_push, source, ctime, mtime, price, user_id, applets_user_id, receiver_user_id, receiver_phone, receiving_time, carriage_time, complete_time, cancel_time, receiver_show_name, hash_code, start_coord_x, start_coord_y, dest_coord_x, dest_coord_y, start_longitude, start_latitude, dest_longitude, dest_latitude, start_detail_add, dest_detail_add, weight, length, wide, high, remark, tel3, tel4, start_city, start_provinc, start_area, dest_provinc, dest_city, dest_area, type, brand, good_type_name, loading_time, unload_time, car_min_length, car_max_length, car_type, car_style, work_plane_min_high, work_plane_max_high, work_plane_min_length, work_plane_max_length, climb, batch, is_valid, push_type, wx_template_id, wx_is_push, order_no, cancel_confirm, find_car_type, addr_matching_status
    </sql>

    <update id="updateStatusToAccept">
        update tyt_transport_backend
        set status = 3, order_status = 33, mtime = NOW(), cancel_time = NOW()
        where order_status in (30, 31)
    </update>

    <update id="updateStatusToCancel">
        update tyt_transport_backend
        set status = 2, order_status = 21, mtime = NOW(), cancel_time = NOW(), cancel_confirm = 1
        where order_status = 10
    </update>

</mapper>
