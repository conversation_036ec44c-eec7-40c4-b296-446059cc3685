<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.teyuntong.infra.task.service.biz.user.limit.mybatis.mapper.DepositBlockMapper">
    <!--Date now = new Date();
    Example example = new Example(DepositBlock.class);
    Example.Criteria criteria = example.createCriteria();
    criteria.andEqualTo("blockStatus", BlockStatusEnum.BLOCK.getValue());
    criteria.andLessThanOrEqualTo("blockEndTime", now);
    criteria.andGreaterThan("blockEndTime", org.apache.commons.lang3.time.DateUtils.addDays(now, -1));

    example.selectProperties("id");
 private Long userId;

    /**
     * 黑名单状态(0 未封禁 1 封禁 2 延迟封禁 )
     */
    private Integer blockStatus;

    /**
     * 封禁类型(1 运营类 2 违约类)
     */
    private Integer blockType;

    /**
     * 是否永久封禁(0 不是 1 是)
     */
    private Boolean permanentBlock;

    /**
     * 是否删除(0未删除，1已删除)
     */
    private Boolean delFlag;

    /**
     * 操作人ID
     */
    private Long operateUserId;

    /**
     * 操作人姓名
     */
    private String operateUserName;

    /**
     * 封禁原因
     */
    private String reason;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 封禁开始时间
     */
    private Date blockBeginTime;

    /**
     * 封禁结束时间
     */
    private Date blockEndTime;

    /**
     * 是否短信通知(0 不通知 1通知)
     */
    private Boolean smsNotify;
    depositBlockMapper.selectByMap()-->
    <select id="queryByCondition" resultType="com.teyuntong.infra.task.service.biz.user.limit.mybatis.entity.DepositBlock" >
        select id id,block_end_time blockEndTime,block_begin_time blockBeginTime,
        version version,permanent_block permanentBlock,block_type blockType,block_status blockStatus,user_id userId
        from tyt_deposit_block
        where block_status = 1 and block_end_time between (SELECT DATE_SUB(CURDATE(), INTERVAL 1 DAY) AS previous_day)  and now()
    </select>
</mapper>