<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.teyuntong.infra.task.service.biz.goods.cdr.mapper.TytAxbCallRecordLogMapper">
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.goods.cdr.entity.TytAxbCallRecordLog">
        <id column="id" property="id" />
        <result column="bill_duration" property="billDuration" />
        <result column="record_url" property="recordUrl" />
        <result column="call_id" property="callId" />
        <result column="sub_id" property="subId" />
        <result column="tel_a" property="telA" />
        <result column="tel_b" property="telB" />
        <result column="tel_x" property="telX" />
        <result column="start_time" property="startTime" />
        <result column="transport_type" property="transportType" />
        <result column="commission_transport" property="commissionTransport" />
        <result column="create_time" property="createTime" />
        <result column="src_msg_id" property="srcMsgId" />
        <result column="transport_user_id" property="transportUserId" />
        <result column="price" property="price" />
        <result column="info_fee" property="infoFee" />
        <result column="tec_service_fee" property="tecServiceFee" />
        <result column="refund_flag" property="refundFlag" />
        <result column="publish_type" property="publishType" />
        <result column="distance" property="distance" />
        <result column="order_id" property="orderId" />
        <result column="car_user_id" property="carUserId" />
        <result column="no_bill_risk" property="noBillRisk" />
    </resultMap>

    <select id="selectByCallId" parameterType="String" resultType="integer">
        SELECT count(1)
        FROM tyt_axb_call_record_log
        WHERE call_id = #{callId}
    </select>

    <select id="getAllCDRCountByTimeParam" resultType="java.lang.Integer">
        SELECT count(1)
        FROM tyt_axb_call_record_log
        WHERE start_time &gt;= #{start} and start_time &lt; #{end}
    </select>

    <select id="getCDRDataByTime" resultMap="BaseResultMap">
        SELECT *
        FROM tyt_axb_call_record_log
        WHERE start_time &gt;= #{start} and start_time &lt; #{end} and id > #{lastProcessedId} order by id
    </select>

    <select id="getAsrTextByCallId" resultType="java.lang.String">
        select text_content
        from tyt_axb_asr_text where call_id = #{callId}
    </select>

    <select id="getAllCommissionSensitiveWords" resultType="java.lang.String">
        select value from tyt_commission_sensitive_words_config where value is not null and value != ''
    </select>

    <insert id="insertTytAxbCallRecordLog" parameterType="com.teyuntong.infra.task.service.biz.goods.cdr.entity.TytAxbCallRecordLog">
        INSERT INTO tyt_axb_call_record_log
        (bill_duration, record_url, call_id, sub_id, tel_a, tel_b, tel_x, start_time, transport_type, commission_transport, create_time, src_msg_id
        , transport_user_id, price, info_fee, tec_service_fee, refund_flag, publish_type, distance, order_id, car_user_id, no_bill_risk, release_cause)
        VALUES
            (#{billDuration}, #{recordUrl}, #{callId}, #{subId}, #{telA}, #{telB}, #{telX}, #{startTime}, #{transportType}, #{commissionTransport}, now(), #{srcMsgId}
            , #{transportUserId}, #{price}, #{infoFee}, #{tecServiceFee}, #{refundFlag}, #{publishType}, #{distance}, #{orderId}, #{carUserId}, 0, #{releaseCause})
    </insert>

    <insert id="replaceAxbCallRecordRisk">
        REPLACE INTO tyt_axb_call_record_risk (call_id, risk_content, create_time)
            VALUE (#{callId}, #{riskReason}, now());
    </insert>

    <update id="updateTytAxbCallRecordLog" parameterType="com.teyuntong.infra.task.service.biz.goods.cdr.entity.TytAxbCallRecordLog">
        UPDATE tyt_axb_call_record_log
        SET
            bill_duration = #{billDuration},
            record_url = #{recordUrl},
            sub_id = #{subId},
            tel_a = #{telA},
            tel_b = #{telB},
            tel_x = #{telX},
            start_time = #{startTime},
            transport_type = #{transportType},
            commission_transport = #{commissionTransport},
            create_time = #{createTime},
            src_msg_id = #{srcMsgId},
            transport_user_id = #{transportUserId},
            price = #{price},
            info_fee = #{infoFee},
            tec_service_fee = #{tecServiceFee},
            refund_flag = #{refundFlag},
            publish_type = #{publishType},
            distance = #{distance},
            order_id = #{orderId},
            car_user_id = #{carUserId},
            release_cause = #{releaseCause}
        WHERE call_id = #{callId}
    </update>

    <update id="updateOrderIdByCDRId">
        UPDATE tyt_axb_call_record_log
        SET
            order_id = #{orderId}
        WHERE id = #{cdrId}
    </update>

    <update id="updateNoBillRiskByCDRId">
        UPDATE tyt_axb_call_record_log
        SET
            no_bill_risk = #{noBillRisk}
        WHERE id = #{cdrId}
    </update>

    <delete id="deleteAxbCallRecordRisk">
        delete from tyt_axb_call_record_risk where call_id = #{callId}
    </delete>

    <select id="getTransportUserNoConnectCount" resultType="java.lang.Integer">
        select count(1)
        from tyt_axb_call_record_log where transport_user_id = #{transportUserId} and (bill_duration is null or bill_duration = 0) and start_time &gt;= #{startDate} and start_time &lt; #{endDate}
    </select>
</mapper>
