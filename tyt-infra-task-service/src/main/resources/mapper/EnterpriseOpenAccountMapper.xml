<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.enterprise.mybatis.EnterpriseOpenAccountMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.enterprise.mybatis.entity.EnterpriseOpenAccountDO">
        <id column="id" property="id" />
        <result column="enterprise_id" property="enterpriseId" />
        <result column="enterprise_name" property="enterpriseName" />
        <result column="enterprise_credit_code" property="enterpriseCreditCode" />
        <result column="parent_account_no" property="parentAccountNo" />
        <result column="sub_account_no" property="subAccountNo" />
        <result column="member_id" property="memberId" />
        <result column="enterprise_matron_id" property="enterpriseMatronId" />
        <result column="enterprise_matron_phone" property="enterpriseMatronPhone" />
        <result column="ctime" property="ctime" />
        <result column="mtime" property="mtime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, enterprise_id, enterprise_name, enterprise_credit_code,parent_account_no,sub_account_no, member_id, enterprise_matron_id, enterprise_matron_phone, ctime, mtime
    </sql>

    <select id="getEnterpriseOpenAccount" resultType="com.teyuntong.infra.task.service.biz.user.enterprise.mybatis.entity.EnterpriseOpenAccountDO">
        SELECT
            tie.id AS enterpriseId,
            tie.enterprise_name AS enterpriseName,
            tie.enterprise_credit_code AS enterpriseCreditCode,
            toa.member_id AS memberId
        FROM
            tyt_enterprise_open_account toa
                LEFT JOIN tyt_invoice_enterprise tie ON toa.enterprise_credit_code = tie.enterprise_credit_code
        WHERE
            tie.manager_flag = 1 order by tie.id desc
    </select>

    <select id="getEnterpriseOpenAccountByParam" resultType="com.teyuntong.infra.task.service.biz.user.enterprise.mybatis.entity.EnterpriseOpenAccountDO">
        SELECT
        tie.id AS enterpriseId,
        tie.enterprise_name AS enterpriseName,
        tie.enterprise_credit_code AS enterpriseCreditCode,
        toa.member_id AS memberId
        FROM
        tyt_enterprise_open_account toa
        LEFT JOIN tyt_invoice_enterprise tie ON toa.enterprise_credit_code = tie.enterprise_credit_code
        WHERE
        tie.manager_flag = 1 and toa.enterprise_credit_code in(
        <foreach collection="enterpriseCreditCodes" item="item" separator=",">
            #{item}
        </foreach>
        )
    </select>

</mapper>
