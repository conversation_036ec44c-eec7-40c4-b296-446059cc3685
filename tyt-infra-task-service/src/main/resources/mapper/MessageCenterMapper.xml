<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.message.center.mapper.MessageCenterMapper">


    <select id="selectDeliverFail"
            resultType="com.teyuntong.infra.task.service.biz.message.center.entity.MessageCenterMqDeliverFailDO">
        select *
        from tyt_message_center_mq_deliver_fail
        where create_time >=  DATE_SUB(NOW(), INTERVAL 30 MINUTE)
          and deliver_status = 1
    </select>

    <update id="updateDeliverFail" parameterType="com.teyuntong.infra.task.service.biz.message.center.entity.MessageCenterMqDeliverFailDO">
        update tyt_message_center_mq_deliver_fail
        set modify_time = NOW()
        <if test="sendNbr != null">
            ,send_nbr = #{sendNbr}
        </if>
        <if test="deliverStatus != null">
            ,deliver_status = #{deliverStatus}
        </if>
        where id = #{id}
    </update>




</mapper>
