<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.permission.mybatis.mapper.ExposurePermissionUsedRecordMapper">
  <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.permission.mybatis.entity.ExposurePermissionUsedRecord">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="src_msg_id" jdbcType="BIGINT" property="srcMsgId" />
    <result column="start_point" jdbcType="VARCHAR" property="startPoint" />
    <result column="dest_point" jdbcType="VARCHAR" property="destPoint" />
    <result column="task_content" jdbcType="VARCHAR" property="taskContent" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
  </resultMap>

    <select id="selectUserUsed" resultType="java.lang.Long">
      select id from exposure_permission_used_record
      where user_id = #{userId}
        limit 1
    </select>

  <select id="getUsedCount" resultType="com.teyuntong.infra.task.service.biz.market.activity.dto.ExposureUsedCountBean">
    select user_id as userId, count(1) as usedCount
    from exposure_permission_used_record
    where ctime between #{startTime} and #{endTime}
    group by user_id order by 2 desc limit 100;
  </select>

</mapper>