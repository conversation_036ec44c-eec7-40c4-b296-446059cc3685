<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.ymm.mybatis.TytSyncgoodsUserMapper">

    <select id="selectAll" resultType="com.teyuntong.infra.task.service.biz.ymm.entity.TytSyncgoodsUser">
        select user_id, rule_group_id from tyt_syncgoods_user
        where is_delete = 0
    </select>

    <select id="selectByUserId" resultType="com.teyuntong.infra.task.service.biz.ymm.entity.TytSyncgoodsUser">
        select user_id, rule_group_id from tyt_syncgoods_user
        where is_delete = 0 and user_id = #{userId,jdbcType=BIGINT}
    </select>

    <select id="selectUserempowerSyncgoodByUserId" resultType="integer">
        select count(1) from tyt_userempower_syncgoods
        where is_delete = 0 and status = 0 and user_id = #{userId,jdbcType=BIGINT}
    </select>

    <select id="selectEmpowerUserGoods" resultType="com.teyuntong.infra.task.service.biz.goods.goodsinfo.entity.TytTransport">
        SELECT tt.start_point           as startPoint,
               tt.dest_point            as destPoint,
               tt.task_content          as taskContent,
               tt.tel,
               tt.pub_time              as pubTime,
               tt.pub_qq                as pubQQ,
               tt.nick_name             as nickName,
               tt.user_show_name        as userShowName,
               tt.status,
               tt.source,
               tt.ctime,
               tt.mtime,
               tt.upload_cellphone      as uploadCellPhone,
               tt.resend,
               tt.start_coord           as startCoord,
               tt.dest_coord            as destCoord,
               tt.plat_id               as platId,
               tt.verify_flag           as verifyFlag,
               tt.price,
               tt.user_id               as userId,
               tt.price_code            as priceCode,
               tt.start_coord_x         as startCoordXValue,
               tt.start_coord_y         as startCoordYValue,
               tt.dest_coord_x          as destCoordXValue,
               tt.dest_coord_y          as destCoordYValue,
               tt.start_detail_add      as startDetailAdd,
               tt.start_longitude       as startLongitudeValue,
               tt.start_latitude        as startLatitudeValue,
               tt.dest_detail_add       as destDetailAdd,
               tt.dest_longitude        as destLongitudeValue,
               tt.dest_latitude         as destLatitudeValue,
               tt.pub_date              as pubDate,
               tt.goods_code            as goodsCode,
               tt.weight_code           as weightCode,
               tt.weight,
               tt.length,
               tt.wide,
               tt.high,
               tt.is_superelevation     as isSuperelevation,
               tt.linkman,
               tt.remark,
               tt.distance              as distanceValue,
               tt.pub_goods_time        as pubGoodsTime,
               tt.tel3,
               tt.tel4,
               tt.display_type          as displayType,
               tt.hash_code             as hashCode,
               tt.is_car                as isCar,
               tt.user_type             as userType,
               tt.pc_old_content        as pcOldContent,
               tt.resend_counts         as resendCounts,
               tt.verify_photo_sign     as verifyPhotoSign,
               tt.user_part             as userPart,
               tt.start_city            as startCity,
               tt.src_msg_id            as srcMsgId,
               tt.start_provinc         as startProvinc,
               tt.start_area            as startArea,
               tt.dest_provinc          as destProvinc,
               tt.dest_city             as destCity,
               tt.dest_area             as destArea,
               tt.client_version        as clientVersion,
               tt.is_info_fee           as isInfoFee,
               tt.info_status           as infoStatus,
               tt.ts_order_no           as tsOrderNo,
               tt.release_time          as releaseTime,
               tt.reg_time              as regTime,
               tt.type,
               tt.brand,
               tt.good_type_name        as goodTypeName,
               tt.good_number           as goodNumber,
               tt.is_standard           as isStandard,
               tt.match_item_id         as matchItemId,
               tt.android_distance      as androidDistance,
               tt.ios_distance          as iosDistance,
               tt.is_display            as isDisplay,
               tt.refer_length          as referLength,
               tt.refer_width           as referWidth,
               tt.refer_height          as referHeight,
               tt.refer_weight          as referWeight,
               tt.car_length            as carLength,
               tt.loading_time          as loadingTime,
               tt.begin_unload_time     as beginUnloadTime,
               tt.unload_time           as unloadTime,
               tt.car_min_length        as carMinLength,
               tt.car_max_length        as carMaxLength,
               tt.car_type              as carType,
               tt.begin_loading_time    as beginLoadingTime,
               tt.car_style             as carStyle,
               tt.work_plane_min_high   as workPlaneMinHigh,
               tt.work_plane_max_high   as workPlaneMaxHigh,
               tt.work_plane_min_length as workPlaneMinLength,
               tt.work_plane_max_length as workPlaneMaxLength,
               tt.climb,
               tt.evaluate,
               tt.special_required      as specialRequired,
               tt.similarity_code       as similarityCode,
               tt.similarity_first_id   as similarityFirstId,
               tt.similarity_first_info as similarityFirstInfo,
               tt.tyre_exposed_flag     as tyreExposedFlag,
               tt.car_length_labels     as carLengthLabels,
               tt.shunting_quantity     as shuntingQuantity,
               tt.first_publish_type    as firstPublishType,
               tt.publish_type          as publishType,
               tt.info_fee              as infoFee,
               tt.exclusive_type        as exclusiveType,
               tt.total_score           as totalScore,
               tt.rank_level            as rankLevel,
               tt.machine_remark        as machineRemark,
               tt.refund_flag           as refundFlag,
               tt.excellent_goods       as excellentGoods
        FROM tyt_userempower_syncgoods tus
        INNER JOIN tyt_transport tt ON tus.user_id = tt.user_id
        INNER JOIN tyt_transport_main_extend me ON me.src_msg_id = tt.src_msg_id
        LEFT JOIN tyt_transport_sync_ymm ttsy on ttsy.src_msg_id =tt.src_msg_id AND ttsy.sync_status = 0 AND ttsy.transport_status =0
        WHERE tus.STATUS = 0
          AND tus.is_delete = 0
          AND tt.STATUS = 1
          AND tt.source = 0
          AND tt.is_display = 1
          AND tt.start_point IS NOT NULL
          AND tt.start_detail_add IS NOT NULL
          AND tt.dest_point IS NOT NULL
          AND tt.task_content IS NOT NULL
          AND tt.weight IS NOT NULL
          AND tt.info_fee IS NOT NULL
          AND tt.info_fee >=20
          AND tt.refund_flag IS NOT NULL
          AND tt.publish_type IS NOT NULL
          AND tt.start_longitude IS NOT NULL
          AND tt.start_latitude IS NOT NULL
          AND tt.dest_longitude IS NOT NULL
          AND tt.dest_latitude IS NOT NULL
          AND tt.tel IS NOT NULL
          AND tt.shunting_quantity = 1
          AND tt.mtime >= #{beginTime,jdbcType=TIMESTAMP}
          AND ttsy.id is null
         <!--6350 过滤集团货源不进行逆向同步 -->
          AND tt.source_type != 4
         <!--6550 开票货源不可进行同步 -->
          AND tt.invoice_transport != 1
         <!--6650 秒抢货源不同步YMM -->
          AND me.seckill_goods != 1
        GROUP BY tt.src_msg_id
    </select>
</mapper>
