<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.info.mybatis.mapper.UserSecurityAccountMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.info.mybatis.entity.UserSecurityAccountDO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="security_money" property="securityMoney" />
        <result column="total_security_get" property="totalSecurityGet" />
        <result column="total_security_use" property="totalSecurityUse" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, security_money, total_security_get, total_security_use, create_time, modify_time
    </sql>

    <update id="updateExpireSecurity">
        update tyt_user_security_account
        set security_money = security_money - #{expireMoney},
            total_security_use = total_security_use + #{expireMoney},
            modify_time = now()
        where user_id = #{userId}
    </update>

</mapper>
