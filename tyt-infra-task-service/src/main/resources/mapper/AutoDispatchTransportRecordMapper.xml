<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.dispatch.mapper.AutoDispatchTransportRecordMapper">


	<select id="countDispatchedBySrcMsgId" resultType="java.lang.Integer">
        select count(*)
        from tyt_auto_dispatch_transport_record
        where src_msg_id = #{srcMsgId} and dispatch_status = 1
    </select>

	<select id="countBySrcMsgIdList" resultType="java.lang.Integer">
		select count(*)
		from tyt_auto_dispatch_transport_record
		where src_msg_id in
		<foreach collection="srcMsgIds" item="item" open="(" separator="," close=")">
			#{item}
		</foreach>
	</select>

</mapper>
