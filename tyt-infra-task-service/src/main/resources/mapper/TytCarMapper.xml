<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.infra.task.service.biz.user.car.mybatis.mapper.TytCarMapper">
  <resultMap id="BaseResultMap" type="com.teyuntong.infra.task.service.biz.user.car.mybatis.entity.TytCar">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="is_dispatch" jdbcType="CHAR" property="isDispatch" />
    <result column="driver_id" jdbcType="BIGINT" property="driverId" />
    <result column="plat_id" jdbcType="VARCHAR" property="platId" />
    <result column="type_code" jdbcType="VARCHAR" property="typeCode" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="head_city" jdbcType="VARCHAR" property="headCity" />
    <result column="head_no" jdbcType="VARCHAR" property="headNo" />
    <result column="tail_city" jdbcType="VARCHAR" property="tailCity" />
    <result column="tail_no" jdbcType="VARCHAR" property="tailNo" />
    <result column="length_code" jdbcType="VARCHAR" property="lengthCode" />
    <result column="length" jdbcType="VARCHAR" property="length" />
    <result column="carry_code" jdbcType="VARCHAR" property="carryCode" />
    <result column="carry" jdbcType="VARCHAR" property="carry" />
    <result column="head_driving_url" jdbcType="VARCHAR" property="headDrivingUrl" />
    <result column="head_driving_subpage_url" jdbcType="VARCHAR" property="headDrivingSubpageUrl" />
    <result column="head_transport_homepage_url" jdbcType="VARCHAR" property="headTransportHomepageUrl" />
    <result column="head_transport_subpage_url" jdbcType="VARCHAR" property="headTransportSubpageUrl" />
    <result column="tail_driving_url" jdbcType="VARCHAR" property="tailDrivingUrl" />
    <result column="tail_driving_subpage_url" jdbcType="VARCHAR" property="tailDrivingSubpageUrl" />
    <result column="tail_driving_other_side_url" jdbcType="VARCHAR" property="tailDrivingOtherSideUrl" />
    <result column="tail_transport_homepage_url" jdbcType="VARCHAR" property="tailTransportHomepageUrl" />
    <result column="tail_transport_subpage_url" jdbcType="VARCHAR" property="tailTransportSubpageUrl" />
    <result column="insurance_code" jdbcType="VARCHAR" property="insuranceCode" />
    <result column="insurance" jdbcType="VARCHAR" property="insurance" />
    <result column="expire_time" jdbcType="TIMESTAMP" property="expireTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="auth" jdbcType="CHAR" property="auth" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="head_photo_url" jdbcType="VARCHAR" property="headPhotoUrl" />
    <result column="failure_reason" jdbcType="VARCHAR" property="failureReason" />
    <result column="car_name" jdbcType="VARCHAR" property="carName" />
    <result column="sex" jdbcType="CHAR" property="sex" />
    <result column="card" jdbcType="VARCHAR" property="card" />
    <result column="head_name" jdbcType="VARCHAR" property="headName" />
    <result column="head_phone" jdbcType="VARCHAR" property="headPhone" />
    <result column="head_brand" jdbcType="VARCHAR" property="headBrand" />
    <result column="tail_name" jdbcType="VARCHAR" property="tailName" />
    <result column="tail_phone" jdbcType="VARCHAR" property="tailPhone" />
    <result column="tail_brand" jdbcType="VARCHAR" property="tailBrand" />
    <result column="has_ladder" jdbcType="TINYINT" property="hasLadder" />
    <result column="gps_url" jdbcType="VARCHAR" property="gpsUrl" />
    <result column="gps_name" jdbcType="VARCHAR" property="gpsName" />
    <result column="gps_pwd" jdbcType="VARCHAR" property="gpsPwd" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="head_auth_status" jdbcType="SMALLINT" property="headAuthStatus" />
    <result column="head_failure_reason" jdbcType="VARCHAR" property="headFailureReason" />
    <result column="tail_failure_reason" jdbcType="VARCHAR" property="tailFailureReason" />
    <result column="find_good_onoff" jdbcType="VARCHAR" property="findGoodOnoff" />
    <result column="tail_auth_status" jdbcType="SMALLINT" property="tailAuthStatus" />
    <result column="sort" jdbcType="BIGINT" property="sort" />
    <result column="examine_time" jdbcType="TIMESTAMP" property="examineTime" />
    <result column="examine_name" jdbcType="VARCHAR" property="examineName" />
    <result column="examine_empl_id" jdbcType="BIGINT" property="examineEmplId" />
    <result column="update_reason" jdbcType="VARCHAR" property="updateReason" />
    <result column="update_type" jdbcType="VARCHAR" property="updateType" />
    <result column="delete_reason" jdbcType="VARCHAR" property="deleteReason" />
    <result column="auth_type" jdbcType="INTEGER" property="authType" />
    <result column="car_type" jdbcType="TINYINT" property="carType" />
    <result column="car_degree" jdbcType="TINYINT" property="carDegree" />
    <result column="car_status" jdbcType="TINYINT" property="carStatus" />
    <result column="is_open_position" jdbcType="TINYINT" property="isOpenPosition" />
    <result column="driver_user_id" jdbcType="BIGINT" property="driverUserId" />
    <result column="driver_name" jdbcType="VARCHAR" property="driverName" />
    <result column="driver_phone" jdbcType="VARCHAR" property="driverPhone" />
    <result column="secondary_driver_user_id" jdbcType="BIGINT" property="secondaryDriverUserId" />
    <result column="secondary_driver_name" jdbcType="VARCHAR" property="secondaryDriverName" />
    <result column="secondary_driver_phone" jdbcType="VARCHAR" property="secondaryDriverPhone" />
    <result column="follow_driver_phone" jdbcType="VARCHAR" property="followDriverPhone" />
    <result column="car_head_type" jdbcType="TINYINT" property="carHeadType" />
    <result column="horse_power" jdbcType="INTEGER" property="horsePower" />
    <result column="driving_form" jdbcType="VARCHAR" property="drivingForm" />
    <result column="emission_standard" jdbcType="VARCHAR" property="emissionStandard" />
    <result column="maintainer_name" jdbcType="VARCHAR" property="maintainerName" />
    <result column="road_card_status" jdbcType="CHAR" property="roadCardStatus" />
    <result column="road_license_status" jdbcType="CHAR" property="roadLicenseStatus" />
    <result column="road_card_fail_reason" jdbcType="VARCHAR" property="roadCardFailReason" />
    <result column="road_license_fail_reason" jdbcType="VARCHAR" property="roadLicenseFailReason" />
    <result column="source" jdbcType="INTEGER" property="source" />
    <result column="auth_path" jdbcType="VARCHAR" property="authPath" />
    <result column="head_driving_expired_time" jdbcType="TIMESTAMP" property="headDrivingExpiredTime" />
    <result column="head_transport_no" jdbcType="VARCHAR" property="headTransportNo" />
    <result column="head_license_plate_color" jdbcType="VARCHAR" property="headLicensePlateColor" />
    <result column="head_vehicle_energy_type" jdbcType="VARCHAR" property="headVehicleEnergyType" />
    <result column="head_tonnage" jdbcType="INTEGER" property="headTonnage" />
    <result column="head_transport_expired_time" jdbcType="TIMESTAMP" property="headTransportExpiredTime" />
    <result column="head_transport_auth_status" jdbcType="INTEGER" property="headTransportAuthStatus" />
    <result column="head_transport_fail_reason" jdbcType="VARCHAR" property="headTransportFailReason" />
    <result column="tail_transport_no" jdbcType="VARCHAR" property="tailTransportNo" />
    <result column="tail_driving_expired_time" jdbcType="TIMESTAMP" property="tailDrivingExpiredTime" />
    <result column="tail_transport_expired_time" jdbcType="TIMESTAMP" property="tailTransportExpiredTime" />
    <result column="tail_transport_auth_status" jdbcType="INTEGER" property="tailTransportAuthStatus" />
    <result column="tail_transport_fail_reason" jdbcType="VARCHAR" property="tailTransportFailReason" />
    <result column="head_business_license_no" jdbcType="VARCHAR" property="headBusinessLicenseNo" />
    <result column="tail_business_license_no" jdbcType="VARCHAR" property="tailBusinessLicenseNo" />
    <result column="is_invoice" jdbcType="VARCHAR" property="isInvoice" />
    <result column="head_car_no" jdbcType="VARCHAR" property="headCarNo" />
    <result column="head_vehicle_type" jdbcType="VARCHAR" property="headVehicleType" />
    <result column="head_business_scope" jdbcType="VARCHAR" property="headBusinessScope" />
    <result column="head_issue_date" jdbcType="VARCHAR" property="headIssueDate" />
    <result column="tail_car_no" jdbcType="VARCHAR" property="tailCarNo" />
    <result column="tail_vehicle_type" jdbcType="VARCHAR" property="tailVehicleType" />
    <result column="tail_business_scope" jdbcType="VARCHAR" property="tailBusinessScope" />
    <result column="tail_issue_date" jdbcType="VARCHAR" property="tailIssueDate" />
    <result column="third_party_require" jdbcType="VARCHAR" property="thirdPartyRequire" />
    <result column="xhl_party_require" jdbcType="INTEGER" property="xhlPartyRequire" />
  </resultMap>

  <select id="getCarExpiredCount" resultType="java.lang.Integer">
    select
      count(1)
    from
      tyt_car
    where
      auth in (0, 1, 3) and is_delete = 1
      and (
      head_driving_expired_time is not null
        or tail_driving_expired_time is not null
      )
  </select>

  <select id="getCarExpiredList" resultMap="BaseResultMap">
    select
      id,
      user_id,
      head_city,
      head_no,
      tail_city,
      tail_no,
      car_type,
      head_driving_expired_time,
      tail_driving_expired_time,
      head_transport_expired_time,
      tail_transport_expired_time,
      auth,
      head_auth_status,
      tail_auth_status,
      head_transport_auth_status,
      tail_transport_auth_status,
      is_invoice,
      third_party_require,
      xhl_party_require
    from
      tyt_car
    where
      id > #{lastCarId}
      and auth in (0, 1, 3) and is_delete = 1
      and (
      head_driving_expired_time is not null
        or tail_driving_expired_time is not null
      ) order by id limit #{pageSize}
  </select>

  <select id="getCarCount" resultType="java.lang.Integer">
    SELECT count(1) FROM tyt_car c WHERE c.id != #{carId} and c.user_id = #{userId} and c.auth = #{auth}
  </select>

  <update id="updateAuthToFailByCarId">
    update tyt_car set
    <if test="headDrivingAuth or tailDrivingAuth">
      auth = 2,
    </if>
    <if test="headDrivingAuth">
      head_auth_status = 2, head_failure_reason = '车头认证信息无效',
    </if>
    <if test="headTransportAuth">
      head_transport_auth_status = 2, head_transport_fail_reason = '车头道路运输证信息无效',
    </if>

    <if test="tailDrivingAuth">
      tail_auth_status = 2, tail_failure_reason = '挂车认证信息无效',
    </if>
    <if test="tailTransportAuth">
      tail_transport_auth_status = 2, tail_transport_fail_reason = '挂车道路运输证信息无效',
    </if>
    update_time = now()
    where id = #{carId}
  </update>


  <update id="updateCarInvoice">
    update tyt_car set is_invoice = 2,
    update_time = now()
    where id = #{carId}
  </update>

  <select id="getCarOcrList" resultType="com.teyuntong.infra.task.service.biz.user.car.pojo.CarInfoForOcrBean">
    SELECT car.id carId,
           car.head_driving_url headDrivingUrl,
           car.head_driving_expired_time headDrivingExpiredTime,
           head.head_issue_authority headIssueAuthority,
           car.tail_driving_url tailDrivingUrl,
           car.tail_driving_expired_time tailDrivingExpiredTime,
           tail. tail_issue_authority tailIssueAuthority
    FROM tyt_car car LEFT JOIN tyt_car_detail_head head ON car.id=head.car_id
        LEFT JOIN tyt_car_detail_tail tail ON car.id=tail.car_id
    WHERE car.id &gt; #{maxId} and auth=1 and is_delete=1 and car.create_time &gt;'2023-08-01' order by car.id asc limit 100
  </select>

  <update id="updateHeadIssueAuthority">
    update tyt_car_detail_head set head_issue_authority = #{issueUnit} where car_id = #{carId}
  </update>

  <update id="updateTailIssueAuthority">
    update tyt_car_detail_tail set tail_issue_authority = #{issueUnit} where car_id = #{carId}
  </update>

  <update id="updateTime">
    update tyt_car set update_time=now()
    <if test="car.headDrivingExpiredTime!=null">
      ,head_driving_expired_time=#{car.headDrivingExpiredTime}
    </if>
    <if test="car.headDrivingExpiredTime!=null">
      ,tail_driving_expired_time=#{car.tailDrivingExpiredTime}
    </if>
    where id = #{car.carId}
  </update>

  <select id="getCarList" resultMap="BaseResultMap">
    select * from tyt_car where id &gt; #{maxId} order by id asc limit 50
  </select>

  <select id="getAuthCarList" resultMap="BaseResultMap">
    select * from tyt_car where id &gt; #{maxId} and auth=1 and is_delete = 1 order by id asc limit 50
  </select>

  <update id="updateIsInvoice">
    update tyt_car set is_invoice = #{selfStatus} ,third_party_require=#{thirdPartyStatus} where id = #{id}
  </update>

  <update id="updateCarInvoiceStatus">
    update tyt_car set xhl_party_require=#{xhlPartyStatus} where id = #{id}
  </update>

    <update id="updateFindGoodOnOff">
      UPDATE tyt_car c
      SET find_good_onoff =0
      WHERE EXISTS
          (SELECT id
           FROM tyt_user  u
           WHERE u.id=c.user_id
             AND " + " end_time &lt;= CONCAT(DATE_FORMAT(NOW(),'%Y-%m-%d'),' 00:00:00') )
        AND find_good_onoff=1
    </update>

  <update id="updateAutoToManual">
    UPDATE tyt_recommend.tyt_preference_new tpn
    SET tpn.is_auth_update = #{isAuthUpdate}, tpn.utime = NOW()
    WHERE tpn.is_auth_update = 1 AND tpn.status = 0
    OR EXISTS (
    SELECT tccl.id
    FROM tyt.tyt_car_current_location tccl
    WHERE tccl.car_id = tpn.car_id AND tccl.new_location_time &lt;= #{changeAutoLocationDate}
    )
    OR EXISTS (
    SELECT tc.id
    FROM tyt.tyt_car tc
    WHERE tc.id = tpn.car_id AND (tc.auth != 1 OR tc.is_delete != 1)
    )
    <if test="userIdsList != null and userIdsList.size() > 0">
      OR tpn.user_id IN
      <foreach item="userId" collection="userIdsList" open="(" separator="," close=")">
        #{userId}
      </foreach>

    </if>
  </update>

  <update id="updateManualToAuto">
    UPDATE tyt_recommend.tyt_preference_new tpn
    SET tpn.is_auth_update = #{isAuthUpdate}, tpn.utime = NOW()
    WHERE tpn.is_auth_update = 2 AND tpn.status = 0
    AND EXISTS (
    SELECT tccl.id
    FROM tyt.tyt_car_current_location tccl
    WHERE tccl.car_id = tpn.car_id AND tccl.new_location_time > #{changeAutoLocationDate}
    )
    AND EXISTS (
    SELECT tc.id
    FROM tyt.tyt_car tc
    WHERE tc.id = tpn.car_id AND tc.auth = 1 AND tc.is_delete = 1
    )
    <if test="userIdsList != null and userIdsList.size() > 0">
      AND tpn.user_id NOT IN
      <foreach item="userId" collection="userIdsList" open="(" separator="," close=")">
        #{userId}
      </foreach>
    </if>
  </update>

  <update id="updateFindGoodOnOffStatus">
    UPDATE tyt_recommend.tyt_preference_new tpn
    SET tpn.find_good_onoff = #{findGoodOnoff},
        tpn.utime           = NOW()
    WHERE tpn.find_good_onoff = 0
      AND tpn.update_onoff_time IS NOT NULL
      AND tpn.is_auth_update = #{isAuthUpdate}
      AND tpn.status = #{status}
      AND DATE_FORMAT(tpn.update_onoff_time, '%Y-%m-%d') != DATE_FORMAT(NOW(), '%Y-%m-%d')
        AND EXISTS (
            SELECT tu.id
            FROM tyt.tyt_user tu
            WHERE tu.id = tpn.user_id
            AND tu.end_time >= CONCAT(DATE_FORMAT(NOW(), '%Y-%m-%d'), ' 00:00:00')
        )
        AND EXISTS (
            SELECT tc.id
            FROM tyt.tyt_car tc
            WHERE tc.id = tpn.car_id
            AND tc.auth = #{auth}
      AND tc.is_delete = #{isDelete}
      )
  </update>

  <update id="updateStartLocation">
    UPDATE tyt_recommend.tyt_preference_new tpn
      INNER JOIN tyt.tyt_car_current_location tccl
    ON tpn.car_id = tccl.car_id
      SET tpn.start_provinc = tccl.province,
        tpn.start_city = tccl.city,
        tpn.start_area = tccl.area,
        tpn.start_coord_x = tccl.dadi_x,
        tpn.start_coord_y = tccl.dadi_y,
        tpn.utime = NOW()
    WHERE tpn.is_auth_update = #{isAuthUpdate}
      AND tpn.status = #{status}
      AND tccl.status = #{locationStatus}
  </update>

</mapper>