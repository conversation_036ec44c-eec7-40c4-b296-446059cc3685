package com.teyuntong.infra.task.service.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.teyuntong.infra.task.service.common.constant.TytDepConstant;
import com.teyuntong.infra.task.service.common.exception.TytException;
import com.teyuntong.infra.task.service.common.mq.pojo.ResponseEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;

import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * plat 接口签名类
 * 该类只在对接plat 等老项目时使用
 *
 * <AUTHOR>
 * @date 2022/11/12 10:43
 */
@Slf4j
public class TytSignUtil {

    /**
     * 移除map中的value空值
     *
     * @param paramMap
     * @return
     */
    private static void removeNullValue(Map<String, String> paramMap) {
        if(MapUtils.isEmpty(paramMap)){
            return;
        }

        Set<String> set = paramMap.keySet();

        for (Iterator iterator = set.iterator(); iterator.hasNext(); ) {
            Object obj = iterator.next();
            Object value = paramMap.get(obj);
            if (value == null || "".equals(value)) {
                iterator.remove();
            }
        }
    }

    /**
     * 除去数组中的空值和签名参数
     *
     * @param paramMap 签名参数组
     * @return 去掉空值与签名参数后的新签名参数组
     */
    private static void removeSign(Map<String, String> paramMap) {
        if(MapUtils.isEmpty(paramMap)){
            return;
        }

        Set<String> set = paramMap.keySet();

        for (Iterator<String> iterator = set.iterator(); iterator.hasNext(); ) {
            String key = iterator.next();
            String value = paramMap.get(key);

            if (StringUtils.isEmpty(value) || key.equalsIgnoreCase("sign")
                    || key.equalsIgnoreCase("signType")) {
                iterator.remove();
            }
        }
    }

    /**
     * 把数组所有元素排序，并按照“参数=参数值”的模式用“&”字符拼接成字符串
     *
     * @param paramMap 需要排序并参与字符拼接的参数组
     * @return 拼接后字符串
     */
    private static String joinParamMap(Map<String, String> paramMap) {
        removeNullValue(paramMap); // 过滤

        List<String> keys = new ArrayList<String>(paramMap.keySet());
        Collections.sort(keys);

        String prestr = "";

        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            String value = paramMap.get(key);

            if (i == keys.size() - 1) {//拼接时，不包括最后一个&字符
                prestr = prestr + key + "=" + value;
            } else {
                prestr = prestr + key + "=" + value + "&";
            }
        }

        return prestr;
    }

    public static String getSignContent(Map<String, String> paramMap, String sortJsonBody) {
        if(sortJsonBody == null){
            sortJsonBody = "";
        }

        removeSign(paramMap);

        //获取待签名字符串
        String signContent = joinParamMap(paramMap);

        signContent = signContent + sortJsonBody + TytDepConstant.sign_private_key;

        return signContent;
    }

    /**
     * 获取请求的签名内容
     * @param paramMap
     * @return
     */
    public String getSignContent(Map<String, String> paramMap) {
        String signContent = getSignContent(paramMap, "");
        return signContent;
    }

    /**
     * 获取签名内容
     * @param httpRequest
     * @return
     */
    public static String getSignContent(HttpServletRequest httpRequest) {

        TreeMap<String, String> paramMap = RequestTools.getParameterMap(httpRequest);

        //如果是json传参，校验body
        String contentType = httpRequest.getContentType();

        String sortBody = "";

        if (StringUtils.isNotEmpty(contentType) && contentType.contains(MediaType.APPLICATION_JSON_VALUE)) {
            String requestBodyStr = RequestTools.getRequestBodyStr(httpRequest, StandardCharsets.UTF_8);
            TreeMap treeMapBody = JSON.parseObject(requestBodyStr, TreeMap.class, Feature.SortFeidFastMatch);

            sortBody = JSON.toJSONString(treeMapBody, SerializerFeature.MapSortField);
        }

        String signContent = getSignContent(paramMap, sortBody);

        return signContent;
    }

    /**
     * 获取请求携带的签名
     * @param httpRequest
     * @return
     */
    public static String getReqSign(HttpServletRequest httpRequest) {
        String sign = httpRequest.getHeader(RequestTools.SIGN);

        if(StringUtils.isBlank(sign)){
            //为了兼容客户端从parameter 取
            sign = httpRequest.getParameter(RequestTools.SIGN);
        }

        return sign;
    }

    /**
     * 根据参数请求生成md5
     * @return
     */
    public static String createSignMd5(HttpServletRequest httpRequest){
        String signContent = getSignContent(httpRequest);

        String signMd5 = DigestUtils.md5Hex(signContent);
        return signMd5;
    }

    /**
     * 根据参数请求生成md5
     * @return
     */
    public static String createSignMd5(Map<String, String> paramMap, String sortJsonBody){

        String signContent = getSignContent(paramMap, sortJsonBody);

        String signMd5 = DigestUtils.md5Hex(signContent);
        return signMd5;
    }

    public static boolean verifySign(HttpServletRequest httpRequest) {

        boolean verifyResult = false;

        String signType = "MD5";
        String reqSign = getReqSign(httpRequest);
        String signMd5 = createSignMd5(httpRequest);

        //获得签名验证结果
        boolean signResult = false;
        if ("MD5".equalsIgnoreCase(signType)) {
            verifyResult = reqSign.equals(signMd5);
        } else if ("RSA".equals(signType)) {
            throw TytException.createException(ResponseEnum.sys_error.info("Not supported yet!~"));
        }
        return verifyResult;
    }

}
