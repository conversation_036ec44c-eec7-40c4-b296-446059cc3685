package com.teyuntong.infra.task.service.utils;

import cn.hutool.json.JSONUtil;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.bean.TransportLabelJson;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.entity.TytTransport;
import com.teyuntong.infra.task.service.common.enums.PublishGoodsTypeEnum;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * 货源模块工具类
 *
 * <AUTHOR>
 * @since 2024/12/02 14:07
 */
public class TransportUtil {

    /**
     * 判断是否有价，货源表price是字符串。
     * <pre>
     *  TransportUtil.hasPrice(null)  = false
     *  TransportUtil.hasPrice("")    = false
     *  TransportUtil.hasPrice(" ")   = false
     *  TransportUtil.hasPrice("0")   = false
     *  TransportUtil.hasPrice("0.0") = false
     * </pre>
     */
    public static boolean hasPrice(String price) {
        if (StringUtils.isBlank(price)) {
            return false;
        }
        return new BigDecimal(price).compareTo(BigDecimal.ZERO) != 0;
    }

    /**
     * 判断是否有重量，不为空且>0
     */
    public static boolean hasWeight(String weight) {
        if (StringUtils.isBlank(weight)) {
            return false;
        }
        return new BigDecimal(weight).compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * 判断是否有距离，不为空且>0
     */
    public static boolean hasDistance(String distance) {
        if(StringUtils.isBlank(distance)) {
            return false;
        }
        return new BigDecimal(distance).compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * 货源长宽高如果为空或=0或=1返回true
     */
    public static boolean isInvalidSize(String size) {
        if (StringUtils.isBlank(size)) {
            return true;
        }
        BigDecimal num = new BigDecimal(size);
        return num.compareTo(BigDecimal.ZERO) == 0 || num.compareTo(BigDecimal.ONE) == 0;
    }

    /**
     * 是否为有效的长宽高，长宽高不能为空，不能为0，不能为1，<=上限值
     *
     * @param size       长宽高字符串
     * @param upperLimit 上限
     */
    public static boolean isValidSize(String size, String upperLimit) {
        if (StringUtils.isNotBlank(size)) {
            BigDecimal num = new BigDecimal(size);
            return num.compareTo(BigDecimal.ZERO) != 0
                    && num.compareTo(BigDecimal.ONE) != 0
                    && num.compareTo(new BigDecimal(upperLimit)) <= 0;
        }
        return false;
    }

    /**
     * 长宽高重都有且都不为1或0
     */
    public static boolean isValidSize(TytTransport transport) {
        return !isInvalidSize(transport.getWeight())
                && !isInvalidSize(transport.getLength())
                && !isInvalidSize(transport.getWide())
                && !isInvalidSize(transport.getHigh());
    }

    /**
     * 获取货源标签json
     */
    public static TransportLabelJson getLabelJson(TytTransport transport) {
        TransportLabelJson labelJson = new TransportLabelJson();
        if (transport != null && transport.getLabelJson() != null) {
            labelJson = JSONUtil.toBean(transport.getLabelJson(), TransportLabelJson.class);
        }
        return labelJson;
    }

    /**
     * 无价货源判断
     */
    public static boolean nonPrice(String price) {
        return !TransportUtil.hasPrice(price);
    }

    /**
     * 根据价格判断新版优车档位: fixPriceMin < fixPriceFast < fixPriceMax
     *
     * @param price        运费
     * @param fixPriceMin  特惠优车价
     * @param fixPriceMax  极速优车价
     * @param fixPriceFast 快速优车价
     */
    public static PublishGoodsTypeEnum judgeExcellentGoodsLevel(String price, Integer fixPriceMin, Integer fixPriceMax, Integer fixPriceFast) {
        if (nonPrice(price) || fixPriceMin == null || fixPriceMax == null || fixPriceFast == null) {
            return null;
        }
        int intPrice = Integer.parseInt(price);
        if (intPrice >= fixPriceMax) {
            return PublishGoodsTypeEnum.SUPER_QUICK_EXCELLENT_GOODS;
        } else if (intPrice >= fixPriceFast) {
            return PublishGoodsTypeEnum.QUICK_EXCELLENT_GOODS;
        } else if (intPrice >= fixPriceMin) {
            return PublishGoodsTypeEnum.EXCELLENT_GOODS;
        } else {
            return null;
        }
    }


}
