package com.teyuntong.infra.task.service.biz.dispatch.enums;

/**
 * 派单状态枚举类
 *
 * <AUTHOR>
 * @since 2024/6/13 14:16
 */
public enum DispatchStatusEnum {

    WAIT_DISPATCH(0,"未派单"),
    SUCCESS_DISPATCH(1,"派单成功"),
    FAIL_DISPATCH(2,"派单失败");

    private int code;

    private final String desc;

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    DispatchStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
