package com.teyuntong.infra.task.service.biz.market.activity.service.impl;

import com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.MarketingConventionCarouselDO;
import com.teyuntong.infra.task.service.biz.market.activity.mybatis.mapper.MarketingConventionCarouselMapper;
import com.teyuntong.infra.task.service.biz.market.activity.service.MarketingConventionCarouselService;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import lombok.RequiredArgsConstructor;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-20
 */

@Service
@Slf4j
@RequiredArgsConstructor
public class MarketingConventionCarouselServiceImpl implements MarketingConventionCarouselService {

    private final MarketingConventionCarouselMapper marketingConventionCarouselMapper;

    @Override
    public void saveCarousel(MarketingConventionCarouselDO carouselDO) {
        marketingConventionCarouselMapper.insert(carouselDO);
    }
}
