package com.teyuntong.infra.task.service.common.sequence.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 序列表
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-10-08
 */
@Getter
@Setter
@TableName("tyt_sequence")
public class SequenceDO {

    /**
     * 名称
     */
    @TableId("name")
    private String name;

    /**
     * 序列值
     */
    private Long number;

    /**
     * 日期
     */
    private String dates;
}
