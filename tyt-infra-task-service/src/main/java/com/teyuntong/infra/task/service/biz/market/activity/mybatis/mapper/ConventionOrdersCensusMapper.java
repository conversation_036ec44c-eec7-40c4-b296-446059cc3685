package com.teyuntong.infra.task.service.biz.market.activity.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.infra.task.service.biz.market.activity.dto.ConventionRank;
import com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.ConventionOrdersCensusDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 履约活动统计表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-22
 */
@Mapper
public interface ConventionOrdersCensusMapper extends BaseMapper<ConventionOrdersCensusDO> {

    List<ConventionOrdersCensusDO> getByUserIdActivityId(@Param("activityId") Long activityId,
                                                         @Param("roundTimes") Integer roundTimes,
                                                         @Param("id") Long id);
    ConventionOrdersCensusDO getByActivityId(@Param("activityId") Long activityId,
                                             @Param("userId") Long userId,
                                             @Param("roundTimes") Integer roundTimes);

    List<ConventionRank> getRank4ConventionOrdersCensus(@Param("round") Integer round, @Param("activityId") Long activityId);

    void updateStatus(@Param("rank") Integer rank,
                      @Param("qualifyGoodsId") long qualifyGoodsId,
                      @Param("qualifyGoodsName") String qualifyGoodsName,
                      @Param("rankGoodsId") long rankGoodsId,
                      @Param("rankGoodsName") String rankGoodsName,
                      @Param("userId") Long userId,
                      @Param("round") int round,
                      @Param("activityId") long activityId);
}
