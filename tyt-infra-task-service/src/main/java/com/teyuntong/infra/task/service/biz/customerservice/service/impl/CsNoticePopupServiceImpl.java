package com.teyuntong.infra.task.service.biz.customerservice.service.impl;

import com.teyuntong.infra.task.service.biz.customerservice.entity.CsNoticePopup;
import com.teyuntong.infra.task.service.biz.customerservice.entity.CsNoticePopupTmpl;
import com.teyuntong.infra.task.service.biz.customerservice.mapper.CsNoticePopupMapper;
import com.teyuntong.infra.task.service.biz.customerservice.mapper.CsNoticePopupTmplMapper;
import com.teyuntong.infra.task.service.biz.customerservice.service.CsNoticePopupService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import lombok.RequiredArgsConstructor;

/**
 * <p>
 * 客服系统弹窗通知模板 服务实现类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-09-29
 */

@Service
@Slf4j
@RequiredArgsConstructor
public class CsNoticePopupServiceImpl implements CsNoticePopupService {
    @Autowired
    private CsNoticePopupMapper csNoticePopupMapper;

    @Autowired
    private CsNoticePopupTmplMapper csNoticePopupTmplMapper;

    @Override
    public CsNoticePopupTmpl getNoticeTmpl(String code) {
        return csNoticePopupTmplMapper.selectByCode(code);
    }

    @Override
    public void saveCsNotice(Long opUserId, CsNoticePopupTmpl noticeTmpl) {
        CsNoticePopup notice = new CsNoticePopup();
        notice.setContent(noticeTmpl.getContent());
        notice.setProductionId(0L);
        if (StringUtils.isNotBlank(noticeTmpl.getTitle())) {
            notice.setTitle(noticeTmpl.getTitle());
        }
        notice.setReceiveId(opUserId);
        csNoticePopupMapper.addNotice(notice);
    }
}
