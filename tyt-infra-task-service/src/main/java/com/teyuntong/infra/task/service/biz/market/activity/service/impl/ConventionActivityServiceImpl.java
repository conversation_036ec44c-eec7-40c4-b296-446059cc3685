package com.teyuntong.infra.task.service.biz.market.activity.service.impl;

import com.teyuntong.infra.task.service.biz.market.activity.dto.ActivityGradeBean;
import com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.ConventionActivityDO;
import com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.MarketingActivityDO;
import com.teyuntong.infra.task.service.biz.market.activity.mybatis.mapper.ConventionActivityMapper;
import com.teyuntong.infra.task.service.biz.market.activity.mybatis.mapper.MarketingActivityUserTargetNumMapper;
import com.teyuntong.infra.task.service.biz.market.activity.service.ActivityPrizeService;
import com.teyuntong.infra.task.service.biz.market.activity.service.ConventionActivityService;
import com.teyuntong.infra.task.service.biz.market.activity.service.MarketingActivityService;
import com.teyuntong.infra.task.service.biz.user.car.pojo.FeedbackNumBean;
import com.teyuntong.infra.task.service.biz.user.permission.service.UserPermissionService;
import com.teyuntong.infra.task.service.utils.TimeUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 履约活动表 服务实现类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-09-30
 */

@Service
@Slf4j
@RequiredArgsConstructor
public class ConventionActivityServiceImpl implements ConventionActivityService {
    private MarketingActivityService marketingActivityService;

    private final ConventionActivityMapper conventionActivityMapper;

    private final UserPermissionService userPermissionService;

    private final MarketingActivityUserTargetNumMapper marketingActivityUserTargetNumMapper;

    private final ActivityPrizeService activityPrizeService;

    /**
     * 车方评价活动结束后发送权益
     * @param
     * @return void
     */
    @Override
    public void giveUserEquity() {
        log.info("定时任务开始执行已结束活动权益发送......");
        long startTime = System.currentTimeMillis();
        //获取前一天日期
        String time = TimeUtil.yesterDayToString();
        List<MarketingActivityDO> activityIds = marketingActivityService.getEndActivity();
        if (CollectionUtils.isEmpty(activityIds)) {
            log.info("暂无未发奖的车方评价活动...");
            return;
        }
        try {
            for (MarketingActivityDO activityId : activityIds) {
                String endTime = TimeUtil.formatDate(activityId.getEndTime());
                if (!endTime.equals(time)) {
                    log.info("车方评价活动已过发奖日期");
                    continue;
                }
                //查询已获奖用户信息
                List<ConventionActivityDO> tytConventionActivities = conventionActivityMapper.getAwardsUser(activityId.getId());
                if (CollectionUtils.isEmpty(tytConventionActivities)) {
                    log.info("未查询到已中奖用户信息");
                    continue;
                }
                //发送权益
                userPermissionService.giveUserEquity(tytConventionActivities);

            }
        } catch (Exception e) {
            log.error("车方评价活动权益发放失败", e);
        }
        log.info("定时任务执行已结束活动权益发送结束，耗时:{}", System.currentTimeMillis() - startTime);
    }

    @Override
    public List<ConventionActivityDO> getByActivityId(Long activityId) {
        return conventionActivityMapper.getByActivityId(activityId);
    }

    @Override
    public void updateEvaluateNum(List<FeedbackNumBean> feedbackNumBeans, Long activityId) {
        //获取参与活动用户
        List<Long> userIds = feedbackNumBeans.stream().map(FeedbackNumBean::getUserId).collect(Collectors.toList());
        List<ActivityGradeBean> markUsers = marketingActivityUserTargetNumMapper.getActivityUser(userIds, activityId);
        if (CollectionUtils.isEmpty(markUsers)) {
            log.info("update evaluate num, no activity user");
            return;
        }
        //获取活动用户等级
        List<ActivityGradeBean> grades = activityPrizeService.getActivityGradeBeanByActivityId(activityId);
        Map<Integer, List<ActivityGradeBean>> activityUserGrade = grades.stream().collect(Collectors.groupingBy(ActivityGradeBean::getGrade));

        Map<Long, Integer> collect = feedbackNumBeans.stream().collect(Collectors.toMap(FeedbackNumBean::getUserId, FeedbackNumBean::getNum));
        //数据更新
        updateConventionActivity(markUsers, collect, activityUserGrade, activityId);
    }

    private void updateConventionActivity(List<ActivityGradeBean> markUsers, Map<Long, Integer> collect, Map<Integer, List<ActivityGradeBean>> activityUserGrade, Long activityId) {
        //循环活动用户处理数据
        for (ActivityGradeBean user : markUsers) {
            //用户单量
            Integer num = collect.get(user.getUserId());
            //用户等级对应目标单量
            List<ActivityGradeBean> activityGradeBeans = activityUserGrade.get(user.getGrade());
            log.info("current user:【{}】 order num:【{}】",user.getUserId(),num);
            //查询当前用户的奖品数据
            ConventionActivityDO tytConventionActivity = conventionActivityMapper.selectByUserId(user.getUserId(), activityId);
            for (ActivityGradeBean bean : activityGradeBeans) {
                if(null != tytConventionActivity){
                    //单量大于等级规定单量并且不等于奖品表单量，更新获奖数据表
                    if ( num >= bean.getOrderNum()) {
                        conventionActivityMapper.updateConvention(activityId, num, bean.getPrize(), user.getUserId());
                    }else{
                        log.info("current user:【{}】 order num:【{}】, unfinished",user.getUserId(),num);
                        conventionActivityMapper.updateConventionNum(activityId, num, user.getUserId());
                    }
                }
            }
        }
    }
}
