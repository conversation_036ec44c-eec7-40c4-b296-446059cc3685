package com.teyuntong.infra.task.service.remote.user.service;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.user.service.client.user.service.UserRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * 用户Remote
 *
 * <AUTHOR>
 * @since 2024-10-09 10:47:47
 */
@FeignClient(name = "tyt-user-service", path = "user", contextId = "userRpcService", fallbackFactory = UserRemoteService.UserRemoteServiceFallback.class)
public interface UserRemoteService extends UserRpcService {

    @Component
    class UserRemoteServiceFallback extends LogAndReturnNullRemoteFallbackFactory<UserRemoteService> {

        public UserRemoteServiceFallback() {
            super(true, UserRemoteService.class);
        }
    }
}