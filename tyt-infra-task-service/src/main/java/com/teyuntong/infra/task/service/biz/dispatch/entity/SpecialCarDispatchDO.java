package com.teyuntong.infra.task.service.biz.dispatch.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <p>
 * 专车派单表
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-09-19
 */
@Getter
@Setter
@TableName("tyt_special_car_dispatch")
public class SpecialCarDispatchDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 货源ID
     */
    private Long tsId;

    /**
     * 发货用户类型：1-调度发货，2-普通用户发货
     */
    private Integer publishUserType;

    /**
     * 调度人员ID
     */
    private Long dispatchUserId;

    /**
     * 调度人员姓名
     */
    private String dispatchUserName;

    /**
     * 调度人员手机号
     */
    private String dispatchCellPhone;

    /**
     * 调度人员钉钉手机号
     */
    private String dispatchDingTalkPhone;

    /**
     * 首次派单时间
     */
    private Date firstDispatchTime;

    /**
     * 最新派单时间
     */
    private Date lastDispatchTime;

    /**
     * 车主总数量
     */
    private Integer userCount;

    /**
     * 已派单车主数量
     */
    private Integer dispatchUserCount;

    /**
     * 接单状态：0.未接单 1.已接单
     */
    private Integer acceptStatus;

    /**
     * 接单时间
     */
    private Date acceptTime;

    /**
     * 接单车主ID
     */
    private Long acceptUserId;

    /**
     * 派单失败通知次数
     */
    private Integer notifyCount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 先后指派司机x分钟未接单指派下一位
     */
    private Integer afterMinutes;

    /**
     * 派单方式：1-同时指派，2-先后指派
     */
    private Integer dispatchType;

    /**
     * 是否指派完毕：0-否，1-是
     */
    private Integer dispatchComplete;
}
