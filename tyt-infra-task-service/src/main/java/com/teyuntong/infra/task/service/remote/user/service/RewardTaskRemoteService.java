package com.teyuntong.infra.task.service.remote.user.service;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.user.service.client.user.service.RewardTaskRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


@FeignClient(name = "tyt-user-service", path = "user", contextId = "rewardTaskRpcService", fallbackFactory = RewardTaskRemoteService.RewardTaskRemoteFallbackFactory.class)
public interface RewardTaskRemoteService extends RewardTaskRpcService {

    @Component
    class RewardTaskRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<RewardTaskRemoteService> {
        protected RewardTaskRemoteFallbackFactory() {
            super(true, RewardTaskRemoteService.class);
        }
    }
}
