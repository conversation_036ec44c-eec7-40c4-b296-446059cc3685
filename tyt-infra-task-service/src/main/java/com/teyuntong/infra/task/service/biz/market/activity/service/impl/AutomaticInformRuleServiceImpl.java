package com.teyuntong.infra.task.service.biz.market.activity.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.teyuntong.infra.common.message.bean.MessagePushBase;
import com.teyuntong.infra.common.message.bean.NewsMessagePush;
import com.teyuntong.infra.common.message.bean.NotifyMessagePush;
import com.teyuntong.infra.common.message.bean.ShortMessageBean;
import com.teyuntong.infra.common.message.service.MessageCenterService;
import com.teyuntong.infra.task.service.biz.market.activity.dto.RuleMessageBean;
import com.teyuntong.infra.task.service.biz.market.activity.dto.UserInformBean;
import com.teyuntong.infra.task.service.biz.market.activity.enums.JobTypeEnum;
import com.teyuntong.infra.task.service.biz.market.activity.enums.RuleConditionEnum;
import com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.AutomaticInformJobDO;
import com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.AutomaticInformRuleDO;
import com.teyuntong.infra.task.service.biz.market.activity.mybatis.mapper.AutomaticInformBlackListMapper;
import com.teyuntong.infra.task.service.biz.market.activity.mybatis.mapper.AutomaticInformJobMapper;
import com.teyuntong.infra.task.service.biz.market.activity.mybatis.mapper.AutomaticInformRuleMapper;
import com.teyuntong.infra.task.service.biz.market.activity.service.AutomaticInformRuleService;
import com.teyuntong.infra.task.service.biz.user.info.mybatis.mapper.TytUserMapper;
import com.teyuntong.infra.task.service.biz.user.info.mybatis.mapper.UserArchivesMapper;
import com.teyuntong.infra.task.service.biz.user.permission.mybatis.mapper.UserPermissionMapper;
import com.teyuntong.infra.task.service.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 自动营销触达规则表 服务实现类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-04-01
 */

@Service
@Slf4j
public class AutomaticInformRuleServiceImpl implements AutomaticInformRuleService {

    @Autowired
    private AutomaticInformRuleMapper automaticInformRuleMapper;

    @Autowired
    private AutomaticInformJobMapper automaticInformJobMapper;

    @Autowired
    private AutomaticInformBlackListMapper automaticInformBlackListMapper;

    @Autowired
    private TytUserMapper tytUserMapper;

    @Autowired
    private MessageCenterService messageCenterService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private UserArchivesMapper userArchivesMapper;

    @Autowired
    private UserPermissionMapper userPermissionMapper;

    private static final String AUTO_INFORM_SEND_KEY = "auto_inform_send_";

    private static final int PAGE_SIZE = 50;

    @Override
    public void sendInform() {
        //获取当前有效规则
        List<AutomaticInformRuleDO> rules = automaticInformRuleMapper.getValidRule();
        if (CollectionUtils.isEmpty(rules)) {
            return;
        }
        int hour = DateUtil.getHours();
        for (AutomaticInformRuleDO rule : rules) {
            List<AutomaticInformJobDO> jobs = automaticInformJobMapper.getJobByRuleIdAndTime(rule.getId(), hour);
            if (CollectionUtils.isEmpty(jobs)) {
                continue;
            }
            //获取规则下的任务
            RuleMessageBean ruleMessageBean = getMessageBean(jobs, rule);
            //执行任务
            sendJobInfo(rule, ruleMessageBean);
        }
    }

    private RuleMessageBean getMessageBean(List<AutomaticInformJobDO> jobs,AutomaticInformRuleDO rule){
        ShortMessageBean shortMessage = null;
        NotifyMessagePush notifyMessage = null;
        NewsMessagePush newsMessagePush = null;
        StringBuilder redisKey = new StringBuilder(AUTO_INFORM_SEND_KEY).append(rule.getId());
        for (AutomaticInformJobDO job : jobs) {
            //拼装短信、站内信、push消息
            if (job.getJobType().equals(JobTypeEnum.SHORT_MESSAGE.getCode())) {
                shortMessage = new ShortMessageBean();
                shortMessage.setContent(job.getJobContent());
                shortMessage.setRemark("");
                redisKey.append("_").append(JobTypeEnum.SHORT_MESSAGE.getCode());
            }
            if (job.getJobType().equals(JobTypeEnum.NOTIFY_MESSAGE.getCode()) || job.getJobType().equals(JobTypeEnum.NEWS_MESSAGE.getCode())) {
                MessagePushBase messagePush = new MessagePushBase();
                messagePush.setTitle(job.getJobTitle());
                messagePush.setContent(job.getJobContent());
                messagePush.setRemarks("");
                if (rule.getPort() == 1) {
                    messagePush.setPushType(1, 0);
                } else {
                    messagePush.setPushType(0, 1);
                }
                if (job.getJobType().equals(JobTypeEnum.NOTIFY_MESSAGE.getCode())) {
                    notifyMessage = NotifyMessagePush.createByPushBase(messagePush);
                    redisKey.append("_").append(JobTypeEnum.NOTIFY_MESSAGE.getCode());
                } else {
                    newsMessagePush = NewsMessagePush.createByPushBase(messagePush);
                    newsMessagePush.setSummary(job.getJobTitle());
                    redisKey.append("_").append(JobTypeEnum.NEWS_MESSAGE.getCode());
                }
            }
        }
        return RuleMessageBean.builder().shortMessage(shortMessage).notifyMessage(notifyMessage).newsMessagePush(newsMessagePush).redisKey(redisKey.toString()).build();
    }

    private void sendJobInfo(AutomaticInformRuleDO rule, RuleMessageBean ruleMessageBean) {
        List<Long> blackUsers = null;
        if (rule.getBlackListStatus() == 1) {
            //获取黑名单列表
            blackUsers = automaticInformBlackListMapper.getBlackList(rule.getId());
        }
        List<String> blackArea = null;
        if (StringUtils.isNotBlank(rule.getBlackArea())) {
            blackArea = Arrays.stream(rule.getBlackArea().trim().split(",")).toList();
        }
        String startTime = DateUtil.minusDaysByYYYMMDD(rule.getDaysEnd()) + " 00:00:00";
        String endTime = DateUtil.minusDaysByYYYMMDD(rule.getDaysStart()) + " 23:59:59";
        Long maxUserId = 0L;
        int days = DateUtil.getDays(new Date(), rule.getEffectEndTime()) + 1;
        while (true) {
            List<UserInformBean> userList = getUserList(rule.getRuleCondition(), blackUsers, blackArea, rule.getPort(), startTime, endTime, maxUserId);
            if (CollectionUtils.isEmpty(userList)) {
                return;
            }
            maxUserId = sendMessage(userList,ruleMessageBean,days,maxUserId);
            if (userList.size() < PAGE_SIZE) {
                return;
            }
            userList.clear();
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
    }

    private Long sendMessage(List<UserInformBean> userList, RuleMessageBean ruleMessageBean, int redisDays, Long maxUserId){
        ShortMessageBean shortMessage = ruleMessageBean.getShortMessage();
        NotifyMessagePush notifyMessage = ruleMessageBean.getNotifyMessage();
        NewsMessagePush newsMessagePush = ruleMessageBean.getNewsMessagePush();
        String redisKey = ruleMessageBean.getRedisKey();
        List<Long> userIds = new ArrayList<>();
        for (UserInformBean userInformBean : userList) {
            maxUserId = userInformBean.getUserId();
            String flag = stringRedisTemplate.opsForValue().get(redisKey+"_"+maxUserId);
            if (StringUtils.isNotBlank(flag)){
                continue;
            }
            if (null != shortMessage) {
                shortMessage.setCellPhone(userInformBean.getCellPhone());
                messageCenterService.sendMultiMessage(shortMessage, null, null);
            }
            userIds.add(userInformBean.getUserId());
            stringRedisTemplate.opsForValue().set(redisKey+"_"+maxUserId,"1", redisDays,TimeUnit.DAYS);
        }
        if (null != notifyMessage) {
            notifyMessage.setUserIdList(userIds);
        }
        if (null != newsMessagePush) {
            newsMessagePush.setUserIdList(userIds);
        }
        if (null != notifyMessage || null != newsMessagePush) {
            messageCenterService.sendMultiMessage(null, newsMessagePush, notifyMessage);
        }
        userIds.clear();
        return maxUserId;
    }


    private List<UserInformBean> getUserList(int ruleCondition, List<Long> blackUsers, List<String> blackArea, int port, String startTime, String endTime, Long maxUserId) {

        if (ruleCondition == RuleConditionEnum.NO_LOGIN.getCode()) {
            return tytUserMapper.getNeedInformUsers(blackUsers, blackArea, port, startTime, endTime, maxUserId, PAGE_SIZE);
        }

        if (ruleCondition == RuleConditionEnum.NO_ACTIVE.getCode()){
            return userArchivesMapper.getNeedInformUsers(blackUsers, blackArea, port, startTime, endTime, maxUserId, PAGE_SIZE);
        }

        if (ruleCondition == RuleConditionEnum.VIP_EXPIRE.getCode()){
            List<Long> userIds = userPermissionMapper.getVipExpireUser(blackUsers, port, startTime, endTime, maxUserId, PAGE_SIZE);
            if (CollectionUtils.isEmpty(userIds)){
                return null;
            }
            return tytUserMapper.getNeedInformUsersForVip(blackArea,userIds);
        }

        if (ruleCondition == RuleConditionEnum.REGISTER.getCode()){
            return userArchivesMapper.getUsersByRegister(blackUsers,blackArea,port,startTime,endTime,maxUserId,PAGE_SIZE);
        }
        return null;
    }
}
