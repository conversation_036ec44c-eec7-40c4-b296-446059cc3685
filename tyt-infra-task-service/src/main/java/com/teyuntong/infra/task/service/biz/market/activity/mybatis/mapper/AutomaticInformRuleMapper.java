package com.teyuntong.infra.task.service.biz.market.activity.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.AutomaticInformRuleDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 自动营销触达规则表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-04-01
 */
@Mapper
public interface AutomaticInformRuleMapper extends BaseMapper<AutomaticInformRuleDO> {

    List<AutomaticInformRuleDO> getValidRule();
}
