package com.teyuntong.infra.task.service.biz.market.activity.service;


import com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.MarketingActivityDO;

import java.util.List;

/**
 * <p>
 * 运营活动表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-22
 */
public interface MarketingActivityService {

    List<MarketingActivityDO> getEndActivity();

    MarketingActivityDO findById(Long activityId);

    List<MarketingActivityDO> getByType(Integer activityType);

    MarketingActivityDO getById(Long id);

    List<MarketingActivityDO> getEndActivity(Integer activityType);
}
