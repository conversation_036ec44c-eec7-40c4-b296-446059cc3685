package com.teyuntong.infra.task.service.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.teyuntong.infra.task.service.common.exception.TytException;
import com.teyuntong.infra.task.service.common.mq.pojo.ResponseEnum;
import com.teyuntong.infra.task.service.common.response.ResponseCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.net.SocketTimeoutException;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2021/07/07
 */
@Slf4j
public class HttpClientUtil {
    private HttpClientUtil() {
    }

    private static final HttpClient HTTP_CLIENT = HttpClientBuilder.create().build();

    private static final RequestConfig REQUEST_CONFIG =
            RequestConfig.custom()
                    .setConnectTimeout(3000)
                    .setSocketTimeout(15000)
                    .build();

    public static String doGet(String url, Map<String, String> parameterMap, Map<String, String> headerMap) {
        try {
            URIBuilder builder = new URIBuilder(url);
            Optional.ofNullable(parameterMap).ifPresent(map -> map.forEach(builder::addParameter));

            HttpGet httpGet = new HttpGet(builder.build());
            httpGet.setConfig(REQUEST_CONFIG);
            Optional.ofNullable(headerMap).ifPresent(map -> map.forEach(httpGet::addHeader));

            HttpResponse httpResponse = HTTP_CLIENT.execute(httpGet);
            return EntityUtils.toString(httpResponse.getEntity());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static String doPost(String url, Map<String, String> parameterMap,
                                String body, Map<String, String> headerMap) {
        try {
            URIBuilder builder = new URIBuilder(url);
            Optional.ofNullable(parameterMap).ifPresent(map -> map.forEach(builder::addParameter));

            HttpPost httpPost = new HttpPost(builder.build());
            httpPost.setConfig(REQUEST_CONFIG);
            Optional.ofNullable(headerMap).ifPresent(map -> map.forEach(httpPost::addHeader));

            HttpEntity httpEntity = new StringEntity(body == null ? "" : body, "utf-8");
            httpPost.setEntity(httpEntity);

            HttpResponse httpResponse = HTTP_CLIENT.execute(httpPost);
            return EntityUtils.toString(httpResponse.getEntity());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /** ---------------------------------------------- **/


    /** 同域名同时请求线程数 **/
    private static final Integer MAX_CONN_PER_ROUTE = 10;

    /** httpClient **/
    private static final CloseableHttpClient instance = getHttpClientInstance();

    public static CloseableHttpClient getInstance(){
        return instance;
    }

    /**
     * 默认配置
     * @return
     */
    private static RequestConfig getHttpClientConfig(){
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(2000)
                .setSocketTimeout(5000)
                .build();
        return requestConfig;
    }

    /**
     * 生成httpclient
     * @return
     */
    private static CloseableHttpClient getHttpClientInstance() {
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(100);
        connectionManager.setDefaultMaxPerRoute(20);//例如默认每路由最高并发，具体依据业务来定

        RequestConfig httpClientConfig = getHttpClientConfig();

        CloseableHttpClient httpClient = HttpClients.custom()
                .setConnectionManager(connectionManager)
                .setDefaultRequestConfig(httpClientConfig)
                .setMaxConnPerRoute(MAX_CONN_PER_ROUTE)
                .build();

        return httpClient;
    }

    /**
     * 创建uri 并拼接参数
     * @param apiUrl
     * @param paramMap
     * @return
     */
    public static URI createUri(String apiUrl, Map<String, String> paramMap){
        URI uri = null;
        try {
            URIBuilder builder = new URIBuilder(apiUrl);

            if(MapUtils.isNotEmpty(paramMap)){
                paramMap.forEach(builder::addParameter);
            }
            uri = builder.build();
        } catch (URISyntaxException e) {
            log.error("", e);
        }

        return uri;
    }

    /**
     * 解析json
     * @param httpResponse
     * @return
     */
    public static JSONObject toJsonObject(HttpResponse httpResponse) {

        JSONObject jsonObject = null;

        try {
            HttpEntity entity = httpResponse.getEntity();
            String jsonStr = EntityUtils.toString(entity, StandardCharsets.UTF_8);

            int statusCode = httpResponse.getStatusLine().getStatusCode();
            if(statusCode != 200) {
                log.warn("toJsonObject_error : httpStatus : [{}], respBody : {}", statusCode, jsonStr);
                throw TytException.createException(ResponseEnum.sys_busy.info());
            }

            if(StringUtils.isNotBlank(jsonStr)) {
                jsonObject = JSON.parseObject(jsonStr);
            }
        } catch (IOException e) {
            throw TytException.createException(e);
        }

        return jsonObject;
    }

    /**
     * http client 请求
     * @param httpRequest
     * @return
     */
    public static CloseableHttpResponse execute(HttpUriRequest httpRequest) throws IOException{
        CloseableHttpResponse httpResponse = null;

        try {
            httpResponse = instance.execute(httpRequest);
        } catch (Exception e) {
            log.error("http_client_execute_error : ", e);

            TytException te = null;

            if(e instanceof ConnectTimeoutException){
                te = TytException.createException(ResponseEnum.sys_busy.info());
            } else if(e instanceof SocketTimeoutException){
                te = TytException.createException(ResponseEnum.sys_busy.info());
            } else {
                te = TytException.createException(ResponseEnum.sys_busy.info());
            }

            throw te;
        }

        return httpResponse;
    }

    /**
     * 读取data 数据
     * @param httpResponse
     * @param typeReference
     * @param <T>
     * @return
     */
    public static <T> T getResponseData(HttpResponse httpResponse, TypeReference<T> typeReference) {
        JSONObject jsonObject = toJsonObject(httpResponse);

        if(jsonObject == null){
            return null;
        }
        T data = null;

        Integer code = jsonObject.getInteger("code");
        if(code != null && code.equals(200)){

            data = jsonObject.getObject("data", typeReference);

        } else {
            log.warn("getResponseData_error : body : {}", jsonObject.toJSONString());
            String msg = jsonObject.getString("msg");
            throw TytException.createException(new ResponseCode(code , msg));
        }

        return data;
    }

    /**
     * 读取data 数据
     * @param httpResponse
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> T getResponseData(HttpResponse httpResponse, Class<T> clazz) {

        JSONObject jsonObject = toJsonObject(httpResponse);

        if(jsonObject == null){
            return null;
        }

        T data = null;

        Integer code = jsonObject.getInteger("code");
        if(code != null && code.equals(200)) {

            data = jsonObject.getObject("data", clazz);

        } else {
            log.warn("getResponseData_error : body : {}", jsonObject.toJSONString());
            String msg = jsonObject.getString("msg");
            throw TytException.createException(new ResponseCode(code , msg));
        }

        return data;
    }

    /**
     * 校验返回Json数据
     * @param jsonObject
     */
    public static void checkJsonCode(JSONObject jsonObject) {

        if(jsonObject == null){
            log.error("jsonObject_is_null ... ");
            throw TytException.createException();
        }

        Integer code = jsonObject.getInteger("code");
        if(code != null && code.equals(200)) {
            //success.

        } else {
            log.warn("getResponseData_error : body : {}", jsonObject.toJSONString());
            String msg = jsonObject.getString("msg");
            throw TytException.createException(new ResponseCode(code , msg));
        }

    }

    /**
     * 校验返回数据
     * @param httpResponse
     */
    public static void checkResponseData(HttpResponse httpResponse) {

        JSONObject jsonObject = toJsonObject(httpResponse);

        checkJsonCode(jsonObject);

    }
}
