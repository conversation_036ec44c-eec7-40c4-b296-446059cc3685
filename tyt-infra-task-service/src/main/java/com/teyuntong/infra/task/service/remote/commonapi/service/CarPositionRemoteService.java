package com.teyuntong.infra.task.service.remote.commonapi.service;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.inner.export.service.client.car.service.CarPositionRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/03/25 14:37
 */
@Service
@FeignClient(name = "tyt-inner-export-service", path = "inner-export", contextId = "carPositionRpcService", fallbackFactory = CarPositionRemoteService.CarPositionRemoteServiceFallbackFactory.class)
public interface CarPositionRemoteService extends CarPositionRpcService {

    @Component
    class CarPositionRemoteServiceFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<CarPositionRemoteService> {
        protected CarPositionRemoteServiceFallbackFactory() {
            super(true, CarPositionRemoteService.class);
        }
    }

}
