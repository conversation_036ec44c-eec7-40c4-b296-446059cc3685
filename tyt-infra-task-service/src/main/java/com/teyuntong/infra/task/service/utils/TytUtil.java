package com.teyuntong.infra.task.service.utils;

import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/01/10 17:19
 */
public class TytUtil {

    public static String isCarOrGoodsOrOrigin(int clientSign){
        //82 小程序算车主端
        if (clientSign == 21 || clientSign ==31|| clientSign == 82){
            return "1";//车主端
        }else if (clientSign == 22 || clientSign == 32 || clientSign == 62){
            return "2"; //货主端
        }else if (clientSign == 72) {
            return "3"; // 小程序货主
        }else {
            return "0"; //原特运通
        }
    }

    public static List<Long> idArraySplit(String idArrayStr) {
        List<Long> idList = null;
        if (StringUtils.isNotBlank(idArrayStr)) {
            String[] idArray = idArrayStr.split(",");
            if (ArrayUtils.isNotEmpty(idArray)) {
                idList = new ArrayList();
                String[] var3 = idArray;
                int var4 = idArray.length;

                for(int var5 = 0; var5 < var4; ++var5) {
                    String oneId = var3[var5];
                    Long idLong = Long.parseLong(oneId.trim());
                    idList.add(idLong);
                }
            }
        }

        return idList;
    }

    /**
     * 用冒号(:) 拼接 redis key
     */
    public static String joinRedisKey(String... strArray) {
        StringBuilder builder = new StringBuilder();

        int i = 0;
        boolean endwithSplit = false;
        for (String onePart : strArray) {

            if (i > 0 && !endwithSplit) {
                builder.append(":");
            }

            builder.append(onePart);

            if (onePart.endsWith(":")) {
                endwithSplit = true;
            } else {
                endwithSplit = false;
            }

            i++;
        }

        return builder.toString();
    }
}
