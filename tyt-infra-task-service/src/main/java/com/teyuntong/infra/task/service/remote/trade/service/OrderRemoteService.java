package com.teyuntong.infra.task.service.remote.trade.service;

import com.teyuntong.infra.common.web.feign.fallback.ThrowOriginExceptionRemoteFallbackFactory;
import com.teyuntong.trade.service.client.orders.service.OrdersRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/03/25 10:50
 */
@Service
@FeignClient(name = "tyt-trade-service", path = "trade", contextId = "ordersRpcService", fallbackFactory = OrderRemoteService.OrderRemoteFallbackFactory.class)
public interface OrderRemoteService extends OrdersRpcService {

    @Component
    class OrderRemoteFallbackFactory extends ThrowOriginExceptionRemoteFallbackFactory<OrderRemoteService> {
        protected OrderRemoteFallbackFactory() {
            super(true, OrderRemoteService.class);
        }
    }
}
