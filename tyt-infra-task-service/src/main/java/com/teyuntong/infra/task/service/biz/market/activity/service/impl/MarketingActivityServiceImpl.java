package com.teyuntong.infra.task.service.biz.market.activity.service.impl;

import com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.MarketingActivityDO;
import com.teyuntong.infra.task.service.biz.market.activity.mybatis.mapper.MarketingActivityMapper;
import com.teyuntong.infra.task.service.biz.market.activity.service.MarketingActivityService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 运营活动表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-22
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class MarketingActivityServiceImpl implements MarketingActivityService {

    private final MarketingActivityMapper marketingActivityMapper;

    /**
     * 查询车方评价活动已结束的活动
     * @param
     * @return List<MarketingActivity>
     */
    @Override
    public List<MarketingActivityDO> getEndActivity() {
        return marketingActivityMapper.getEndActivity(20);
    }

    @Override
    public MarketingActivityDO findById(Long activityId) {
        return marketingActivityMapper.selectById(activityId);
    }

    @Override
    public List<MarketingActivityDO> getByType(Integer activityType) {
        return marketingActivityMapper.getByType(activityType);
    }

    @Override
    public MarketingActivityDO getById(Long id) {
        return marketingActivityMapper.selectById(id);
    }

    @Override
    public List<MarketingActivityDO> getEndActivity(Integer activityType) {
        return marketingActivityMapper.getEndActivity(activityType);
    }
}
