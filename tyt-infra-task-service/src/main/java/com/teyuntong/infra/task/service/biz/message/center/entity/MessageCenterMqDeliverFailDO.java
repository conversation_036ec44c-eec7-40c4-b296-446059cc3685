package com.teyuntong.infra.task.service.biz.message.center.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tyt_message_center_mq_deliver_fail")
public class MessageCenterMqDeliverFailDO {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 消息序列号，每个消息有一个唯一的序列号，用于唯一标示一条消息
     */
    private String messageSerialNum;

    /**
     * 消息的原始完整内容
     */
    private String messageContent;

    /**
     * 消息的投递状态，1：未投递 2：已投递 3: 投递失败，4连续失败后状态
     */
    private Byte deliverStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 失败次数
     */
    private Integer sendNbr;
}