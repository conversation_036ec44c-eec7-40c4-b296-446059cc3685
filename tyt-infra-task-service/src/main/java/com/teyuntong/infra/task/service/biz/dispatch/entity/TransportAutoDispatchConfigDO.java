package com.teyuntong.infra.task.service.biz.dispatch.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 非专车货源自动派单配置表
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-12-12
 */
@Getter
@Setter
@TableName("tyt_transport_auto_dispatch_config")
public class TransportAutoDispatchConfigDO {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 出发地城市
     */
    private String startCity;

    /**
     * 目的地城市
     */
    private String destCity;

    /**
     * 城市 多个之间用英文逗号分隔
     */
    private String citys;

    /**
     * 运距类型 1：短0<运距≤200km；2：中200km<运距≤500km；3：长500km≤运距 多个之间用英文逗号分隔
     */
    private String distanceType;

    /**
     * 是否接入好货分数：1-是，2-否
     */
    private Integer matchGoodsScore;

    /**
     * 好货分数配置json
     */
    private String goodsScoreConfig;

    /**
     * 适用货源：1-普货，2-优车1.0，3-优车2.0，4-特惠优车、5-快速优车、6-极速优车。多个英文逗号分隔
     */
    private String goodsType;

    /**
     * 价格模式：1-一口价，2-无价，3-电议有价。多个英文逗号分隔
     */
    private String priceType;

    /**
     * 专票货源：1-不包含，2-包含
     */
    private Integer includeInvoice;

    /**
     * 货源首发超过X分钟，给专车司机自动派单
     */
    private Integer afterMinutes;

    /**
     * 车辆距出发地小于X公里进行派单
     */
    private Integer distanceLimit;

    /**
     * 状态：1-启用，2-停用
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 操作人ID
     */
    private Long opUserId;

    /**
     * 操作人姓名
     */
    private String opUserName;

    /**
     * 记录状态：0-正常，1-已删除
     */
    private Integer delStatus;
}
