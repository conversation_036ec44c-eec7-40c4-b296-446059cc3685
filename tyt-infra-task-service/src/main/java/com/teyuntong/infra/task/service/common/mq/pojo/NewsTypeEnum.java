package com.teyuntong.infra.task.service.common.mq.pojo;

public enum NewsTypeEnum {
    text(0, "文本"),
    img(1, "图片");

    private Integer code;
    private String zhName;

    private NewsTypeEnum(Integer code, String zhName) {
        this.code = code;
        this.zhName = zhName;
    }

    public static NewsTypeEnum getEnum(Integer code) {
        if (code == null) {
            return null;
        } else {
            NewsTypeEnum[] var1 = values();
            int var2 = var1.length;

            for(int var3 = 0; var3 < var2; ++var3) {
                NewsTypeEnum statusEnum = var1[var3];
                if (statusEnum.getCode().equals(code)) {
                    return statusEnum;
                }
            }

            return null;
        }
    }

    public static String getEnumName(Integer code) {
        NewsTypeEnum anEnum = getEnum(code);
        return anEnum != null ? anEnum.getZhName() : "";
    }

    public static NewsTypeEnum getEnumByZhName(String zhName) {
        if (zhName == null) {
            return null;
        } else {
            NewsTypeEnum[] var1 = values();
            int var2 = var1.length;

            for(int var3 = 0; var3 < var2; ++var3) {
                NewsTypeEnum oneEnum = var1[var3];
                if (oneEnum.getZhName().equalsIgnoreCase(zhName)) {
                    return oneEnum;
                }
            }

            return null;
        }
    }

    public Integer getCode() {
        return this.code;
    }

    public String getZhName() {
        return this.zhName;
    }
}
