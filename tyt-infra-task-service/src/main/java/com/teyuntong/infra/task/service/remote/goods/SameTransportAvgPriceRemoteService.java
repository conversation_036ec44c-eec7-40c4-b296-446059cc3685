package com.teyuntong.infra.task.service.remote.goods;

import com.teyuntong.goods.service.client.transport.service.SameTransportAvgPriceRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;


@Service
@FeignClient(name = "tyt-goods-service", path = "goods", contextId = "SameTransportAvgPriceRpcService", fallbackFactory = SameTransportAvgPriceRemoteService.SameTransportAvgPriceRemoteFallbackFactory.class)
public interface SameTransportAvgPriceRemoteService extends SameTransportAvgPriceRpcService {

    @Component
    class SameTransportAvgPriceRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<SameTransportAvgPriceRemoteService> {
        protected SameTransportAvgPriceRemoteFallbackFactory() {
            super(true, SameTransportAvgPriceRemoteService.class);
        }
    }
}
