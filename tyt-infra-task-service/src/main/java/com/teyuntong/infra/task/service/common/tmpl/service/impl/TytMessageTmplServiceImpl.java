package com.teyuntong.infra.task.service.common.tmpl.service.impl;

import com.teyuntong.infra.task.service.common.tmpl.entity.MessageTmplDO;
import com.teyuntong.infra.task.service.common.tmpl.mapper.TytMessageTmplMapper;
import com.teyuntong.infra.task.service.common.tmpl.service.TytMessageTmplService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class TytMessageTmplServiceImpl implements TytMessageTmplService {
    @Autowired
    private TytMessageTmplMapper tytMessageTmplMapper;
    @Override
    public String getSmsTmpl(String key) {
        MessageTmplDO content = tytMessageTmplMapper.getContent(key);
        if(content != null){
            return content.getContent();
        }
        return null;
    }

    @Override
    public String getPushTmpl(String key) {
        MessageTmplDO content = tytMessageTmplMapper.getPushContent(key);
        if(content != null){
            return content.getContent();
        }
        return "";
    }
}
