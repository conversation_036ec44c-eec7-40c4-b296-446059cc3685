package com.teyuntong.infra.task.service.biz.market.activity.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.MarketingActivityChargeOrderDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 冲单活动阶段时间表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-22
 */
@Mapper
public interface MarketingActivityChargeOrderMapper extends BaseMapper<MarketingActivityChargeOrderDO> {

    List<MarketingActivityChargeOrderDO> getByActivityId(@Param("activityId") Long activityId);

}
