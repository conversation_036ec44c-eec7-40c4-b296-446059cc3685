package com.teyuntong.infra.task.service.biz.dispatch.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.teyuntong.infra.common.rocketmq.core.RocketMqProducer;
import com.teyuntong.infra.common.rocketmq.message.MqMessage;
import com.teyuntong.infra.common.rocketmq.message.MqMessageFactory;
import com.teyuntong.infra.task.service.biz.dispatch.entity.AutoDispatchTransportRecordDO;
import com.teyuntong.infra.task.service.biz.dispatch.entity.SpecialCarDispatchBean;
import com.teyuntong.infra.task.service.biz.dispatch.entity.TransportAutoDispatchConfigDO;
import com.teyuntong.infra.task.service.biz.dispatch.enums.UseCarTypeEnum;
import com.teyuntong.infra.task.service.biz.dispatch.mapper.AutoDispatchTransportRecordMapper;
import com.teyuntong.infra.task.service.biz.dispatch.mapper.TransportAutoDispatchConfigMapper;
import com.teyuntong.infra.task.service.biz.dispatch.service.NonSpecialCarDispatchService;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.entity.TransportMainExtendDO;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.entity.TytTransportMain;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.mapper.TransportMainExtendMapper;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.mapper.TransportMainMapper;
import com.teyuntong.infra.task.service.common.enums.PublishTypeEnum;
import com.teyuntong.infra.task.service.remote.goods.InnerTransportRemoteService;
import com.teyuntong.infra.task.service.utils.TimeUtil;
import com.teyuntong.inner.export.service.client.common.bean.ResultMsgBean;
import com.teyuntong.inner.export.service.client.plat.transport.dto.AutoAssignOrderDto;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * 非专车货源自动指派
 *
 * <AUTHOR>
 * @since 2024-12-12 13:11
 */
@Service
@Slf4j
public class NonSpecialCarDispatchServiceImpl implements NonSpecialCarDispatchService {
    @Autowired
    private TransportAutoDispatchConfigMapper transportAutoDispatchConfigMapper;
    @Autowired
    private TransportMainMapper transportMainMapper;
    @Autowired
    private TransportMainExtendMapper transportMainExtendMapper;
    @Autowired
    private InnerTransportRemoteService innerTransportRemoteService;
    @Autowired
    private AutoDispatchTransportRecordMapper autoDispatchTransportRecordMapper;
    @Autowired
    private MqMessageFactory mqMessageFactory;
    @Autowired
    private RocketMqProducer rocketMqProducer;

    /**
     * 非专车货源自动指派
     */
    @Override
    public void nonSpecialCarAutoDispatch() {
        int pageNo = 1;
        int pageSize = 100;
        while(true) {
            int start = (pageNo - 1) * pageSize;
            List<TransportAutoDispatchConfigDO> configDOList = transportAutoDispatchConfigMapper.selectAllEnableConfig(start, pageSize);
            if (CollectionUtils.isEmpty(configDOList)) {
                log.info("非专车货源自动指派,启用状态指派配置为空");
                break;
            }

            for (TransportAutoDispatchConfigDO configDO : configDOList) {
                dispatchByConfig(configDO);
            }

            if (configDOList.size() < pageSize) {
                break;
            }
            pageNo = pageNo + 1;
        }
    }

    /**
     * 根据派单配置规则，筛选满足条件的货源，进行派单
     *
     * @param configDO
     */
    private void dispatchByConfig(TransportAutoDispatchConfigDO configDO) {
        Date startTime = new Date();
        Integer afterMinutes = configDO.getAfterMinutes();
        if (Objects.nonNull(afterMinutes)) {
            startTime = TimeUtil.addMinute(startTime, -afterMinutes);
        }
        int pageNo = 1;
        int pageSize = 100;
        while (true) {
            int start = (pageNo - 1) * pageSize;
            List<TytTransportMain> mainList;
            if (StringUtils.isNotBlank(configDO.getStartCity()) && StringUtils.isNotBlank(configDO.getDestCity())) {
                log.info("非专车派单 使用路线条件查询货源 configId:{}", configDO.getId());
                mainList = transportMainMapper.selectCanDispatchNonSpecialCarList(configDO.getStartCity(),
                        configDO.getDestCity(), startTime, start, pageSize);
            } else {
                log.info("非专车派单 使用城市运距条件查询货源 configId:{} 城市:{} 运距类型:{}", configDO.getId(), configDO.getCitys(), configDO.getDistanceType());
                String[] citys = configDO.getCitys().split("[,，]");
                List<String> cityList = List.of(citys);
                int distanceMin = 0;
                int distanceMax = 20000;
                if (configDO.getDistanceType() != null && configDO.getDistanceType().contains("2")) {
                    distanceMin = 20000;
                    distanceMax = 50000;
                } else if (configDO.getDistanceType() != null && configDO.getDistanceType().contains("3")) {
                    distanceMin = 50000;
                    distanceMax = 999999999;
                }
                mainList = transportMainMapper.selectCanDispatchNonSpecialCarList2(cityList, startTime, start, pageSize);
            }

            if (CollectionUtils.isEmpty(mainList)) {
                log.info("非专车货源自动指派，满足条件的货源为空，configId：{}", configDO.getId());
                break;
            }

            for (TytTransportMain main : mainList) {
                dispatchByCondition(configDO, main);
            }

            if (mainList.size() < pageSize) {
                break;
            }
            pageNo = pageNo + 1;
        }
    }

    /**
     * 判断货源是否满足自动指派，并进行指派
     *
     * @param configDO
     * @param main
     */
    private void dispatchByCondition(TransportAutoDispatchConfigDO configDO, TytTransportMain main) {
        // 货源只指派一次，指派过的不再指派
        int count = autoDispatchTransportRecordMapper.countDispatchedBySrcMsgId(main.getSrcMsgId());
        if (count > 0) {
            log.info("非专车货源自动指派，该货源已指派过不再进行指派，srcMsgId：{}", main.getSrcMsgId());
            return;
        }
        if (!checkMatchAutoDispatch(configDO, main)) {
            return;
        }
        // 满足自动指派条件，进行自动指派
        doDispatch(configDO, main);
    }

    /**
     * 判断货源是否满足非专车自动指派
     *
     * @param main
     * @return
     */
    public boolean matchDispatchCondition(TytTransportMain main) {
        TransportAutoDispatchConfigDO configDO = transportAutoDispatchConfigMapper.selectByRoute(main.getStartCity(), main.getDestCity());
        if (Objects.isNull(configDO)) {
            return false;
        }
        return checkMatchAutoDispatch(configDO, main);
    }

    /**
     * 判断是否满足自动指派
     *
     * @param configDO
     * @param main
     * @return
     */
    private boolean checkMatchAutoDispatch(TransportAutoDispatchConfigDO configDO, TytTransportMain main) {
        // 专票货源判断
        if (Objects.equals(configDO.getIncludeInvoice(), 1) && Objects.equals(main.getInvoiceTransport(), 1) ) {
            log.info("非专车货源自动指派，专票货源不指派");
            return false;
        }
        // 货源类型判断
        Integer goodsType = GoodsTypeEnum.NORMAL.getCode();
        if (Objects.equals(main.getExcellentGoods(), 1)) {
            goodsType = GoodsTypeEnum.EXCELLENT_1.getCode();
        }
        if (Objects.equals(main.getExcellentGoodsTwo(), 2)) {
            goodsType = GoodsTypeEnum.EXCELLENT_2.getCode();
        }

        Integer goodsTypeNew = GoodsTypeEnum.NORMAL.getCode();
        if (Objects.equals(main.getPublishGoodsType(), 20)) {
            goodsTypeNew = GoodsTypeEnum.EXCELLENT_4.getCode();
        }
        if (Objects.equals(main.getPublishGoodsType(), 21)) {
            goodsTypeNew = GoodsTypeEnum.EXCELLENT_5.getCode();
        }
        if (Objects.equals(main.getPublishGoodsType(), 22)) {
            goodsTypeNew = GoodsTypeEnum.EXCELLENT_6.getCode();
        }

        //两个都校验是为了新老兼容
        if (!configDO.getGoodsType().contains(String.valueOf(goodsType)) && !configDO.getGoodsType().contains(String.valueOf(goodsTypeNew))) {
            log.info("非专车货源自动指派，货源类型不匹配");
            return false;
        }
        // 价格模式判断
        Integer priceType = PriceTypeEnum.NO_PRICE.getCode();
        if (Objects.equals(main.getPublishType(), PublishTypeEnum.BUYOUT.getPublishType())) {
            priceType = PriceTypeEnum.FIX_PRICE.getCode();
        } else if (StringUtils.isNotBlank(main.getPrice()) && !StringUtils.equals(main.getPrice(), "0")) {
            priceType = PriceTypeEnum.TEL_PRICE.getCode();
        }
        if (!configDO.getPriceType().contains(String.valueOf(priceType))) {
            log.info("非专车货源自动指派，价格模式不匹配");
            return false;
        }
        // 已指派过相似货源，不再指派
        if (StringUtils.isNotBlank(main.getSimilarityCode())) {
            boolean haveSimilarAssignRecord = checkHaveSimilarAssignRecord(main);
            if (haveSimilarAssignRecord) {
                saveAutoDispatchRecord(configDO, main, false);
                log.info("非专车货源自动指派，已指派过相似货源");
                return false;
            }
        }
        TransportMainExtendDO mainExtendDO = transportMainExtendMapper.selectBySrcMsgId(main.getSrcMsgId());
        if (Objects.nonNull(mainExtendDO) && Objects.equals(mainExtendDO.getUseCarType(), UseCarTypeEnum.PART.getCode())) {
            // 拼车不自动派单
            return false;
        }
        // 好货分数判断
        if (Objects.equals(configDO.getMatchGoodsScore(), 1)) {
            // 货源质量分不满足好货分数配置
            return matchGoodsScoreConfig(configDO, mainExtendDO);
        }
        return true;
    }

    /**
     * 校验是否指派过相似货源
     *
     * @param main
     * @return
     */
    private boolean checkHaveSimilarAssignRecord(TytTransportMain main) {
        List<Long> srcMsgIds = transportMainMapper.selectBySimilarityCode(main.getSimilarityCode(), main.getId());
        if (CollectionUtils.isNotEmpty(srcMsgIds)) {
            int count = autoDispatchTransportRecordMapper.countBySrcMsgIdList(srcMsgIds);
            return count > 0;
        }
        return false;
    }

    /**
     * 执行派单，调用plat派单接口
     *
     * @param configDO
     * @param main
     */
    public void doDispatch(TransportAutoDispatchConfigDO configDO, TytTransportMain main) {
        // AutoAssignOrderDto assignOrderDto = new AutoAssignOrderDto();
        // assignOrderDto.setTsId(main.getSrcMsgId());
        // assignOrderDto.setStartCity(main.getStartCity());
        // assignOrderDto.setDestCity(main.getDestCity());
        // assignOrderDto.setGoodsTypeName(main.getGoodTypeName());
        // assignOrderDto.setUserId(main.getUserId());
        // assignOrderDto.setDistanceLimit(configDO.getDistanceLimit());
        // log.info("非专车货源自动指派，调用plat指派接口参数:{}", JSONObject.toJSONString(assignOrderDto));
        // ResultMsgBean resultMsgBean = innerTransportRemoteService.autoAssignOrderForSpecialCar(assignOrderDto);
        // log.info("非专车货源自动指派，调用plat指派接口返回:{}", JSONObject.toJSONString(resultMsgBean));
        SpecialCarDispatchBean dispatchBean = new SpecialCarDispatchBean();
        dispatchBean.setSrcMsgId(main.getSrcMsgId());
        dispatchBean.setUserId(main.getUserId());
        dispatchBean.setDistanceLimit(configDO.getDistanceLimit());
        MqMessage mqMessage = mqMessageFactory.create("GOODS_CENTER_TOPIC", "DISPATCH", UUID.randomUUID().toString(), dispatchBean);
        rocketMqProducer.sendNormal(mqMessage);

        // 保存指派记录
        saveAutoDispatchRecord(configDO, main, true);
    }

    /**
     * 保存自动指派记录
     *
     * @param configDO
     * @param main
     */
    private void saveAutoDispatchRecord(TransportAutoDispatchConfigDO configDO, TytTransportMain main, boolean dispatch) {
        AutoDispatchTransportRecordDO recordDO = new AutoDispatchTransportRecordDO();
        recordDO.setSrcMsgId(main.getSrcMsgId());
        recordDO.setConfigId(configDO.getId());
        if (dispatch) {
            recordDO.setDispatchStatus(1);
        } else {
            recordDO.setDispatchStatus(0);
            recordDO.setRemark("因相似货源未进行指派");
        }
        autoDispatchTransportRecordMapper.insert(recordDO);
    }

    /**
     * 是否满足好货分数配置判断
     *
     * @param configDO
     * @param mainExtendDO
     * @return
     */
    private boolean matchGoodsScoreConfig(TransportAutoDispatchConfigDO configDO, TransportMainExtendDO mainExtendDO) {
        if (Objects.isNull(mainExtendDO) || Objects.isNull(mainExtendDO.getLimGoodModelScore())) {
            log.info("非专车货源自动指派，好货模型分数不满足");
            return false;
        }
        String goodsScoreConfig = configDO.getGoodsScoreConfig();
        JSONArray configArray = JSONObject.parseArray(goodsScoreConfig);
        for (int i = 0; i < configArray.size(); i++) {
            JSONObject configJson = configArray.getJSONObject(i);
            String start = configJson.getString("start");
            String end = configJson.getString("end");
            if (StringUtils.isBlank(start) || StringUtils.isBlank(end)) {
                continue;
            }
            // 货源质量分在配置区间内
            if (mainExtendDO.getLimGoodModelScore().compareTo(new BigDecimal(start)) >= 0 &&
                    mainExtendDO.getLimGoodModelScore().compareTo(new BigDecimal(end)) <= 0) {
                return true;
            }
        }
        log.info("非专车货源自动指派，好货模型分数不满足");
        return false;
    }

    @Getter
    public enum GoodsTypeEnum {
        NORMAL(1, "普货"),
        EXCELLENT_1(2, "优车1.0"),
        EXCELLENT_2(3, "优车2.0"),
        EXCELLENT_4(4, "特惠优车"),
        EXCELLENT_5(5, "快速优车"),
        EXCELLENT_6(6, "极速优车")
        ;
        private final Integer code;
        private final String name;
        GoodsTypeEnum(Integer code, String name) {
            this.code = code;
            this.name = name;
        }
    }

    @Getter
    public enum PriceTypeEnum {
        FIX_PRICE(1, "一口价"),
        NO_PRICE(2, "无价"),
        TEL_PRICE(3, "电议有价")
        ;
        private final Integer code;
        private final String name;
        PriceTypeEnum(Integer code, String name) {
            this.code = code;
            this.name = name;
        }
    }
}
