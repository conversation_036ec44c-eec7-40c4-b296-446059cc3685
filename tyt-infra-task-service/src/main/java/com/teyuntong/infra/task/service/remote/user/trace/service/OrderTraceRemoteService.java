//package com.teyuntong.infra.task.service.remote.user.trace.service;
//
//import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
//import com.teyuntong.infra.task.service.remote.trade.service.OrderRemoteService;
//import com.teyuntong.user.trace.client.order.trace.OrderTraceRpcService;
//import com.teyuntong.user.trace.client.order.trace.dto.OrderTraceDTO;
//import com.teyuntong.user.trace.client.order.trace.dto.OrderTraceSaveDTO;
//import com.teyuntong.user.trace.client.order.trace.vo.OrderTraceRpcVo;
//import java.util.Collections;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.cloud.openfeign.FallbackFactory;
//import org.springframework.cloud.openfeign.FeignClient;
//import org.springframework.stereotype.Component;
//import org.springframework.stereotype.Service;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// * @since 2024/03/22 18:45
// */
//@Service
//@FeignClient(name = "tyt-user-trace", path = "trace", contextId = "orderTraceRpcService", fallbackFactory = OrderTraceRemoteService.OrderTraceFallbackFactory.class)
//public interface OrderTraceRemoteService extends OrderTraceRpcService {
//
//    @Component
//    class OrderTraceFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<OrderTraceRemoteService> {
//        protected OrderTraceFallbackFactory() {
//            super(true, OrderTraceRemoteService.class);
//        }
//    }
//
//}
