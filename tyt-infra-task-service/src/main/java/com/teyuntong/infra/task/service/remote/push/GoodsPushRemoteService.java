package com.teyuntong.infra.task.service.remote.push;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.inner.export.service.client.push.service.GoodsPushRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;

/**
 * 推荐货源推送
 *
 * <AUTHOR>
 * @since 2024-11-01 10:24
 */
@Service
@FeignClient(name = "tyt-inner-export-service", path = "inner-export", contextId = "goodsPushRpcService", fallbackFactory = GoodsPushRemoteService.GoodsPushFallbackFactory.class)
public interface GoodsPushRemoteService extends GoodsPushRpcService {
    class GoodsPushFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<GoodsPushRemoteService> {
        protected GoodsPushFallbackFactory() {
            super(true, GoodsPushRemoteService.class);
        }
    }
}
