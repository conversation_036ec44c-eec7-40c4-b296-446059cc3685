package com.teyuntong.infra.task.service.utils;

import java.math.BigDecimal;

public class MoneyUtil {
    /**
     * 将单位为元的金额转换为单位为分
     *
     * @param yuan 单位为元的字符型值
     * @return
     */
    public static int yuan2Fen(String yuan) {
        int value = 0;

        try {
            BigDecimal var1 = new BigDecimal(yuan);
            BigDecimal var2 = new BigDecimal(100);
            BigDecimal var3 = var1.multiply(var2);
            value = Integer.parseInt(var3.stripTrailingZeros().toPlainString());
        } catch (Exception e) {
            throw new IllegalArgumentException(String.format("非法金额[%s]", yuan));
        }

        return value;
    }

    /**
     * 将单位为元的金额转换为单位为分
     *
     * @param yuan 单位为元的字符型值
     * @return
     */
    public static int yuan2Fen(BigDecimal yuan) {
        int value = 0;

        try {
            BigDecimal var2 = new BigDecimal(100);
            BigDecimal var3 = yuan.multiply(var2);
            value = Integer.parseInt(var3.stripTrailingZeros().toPlainString());
        } catch (Exception e) {
            throw new IllegalArgumentException(String.format("非法金额[%s]", yuan));
        }

        return value;
    }
    /**
     * 将单位为分的金额转换为单位为元
     * @param fen 单位为分的字符型值
     * @return
     */
    public static String fen2Yuan(int fen) {
        BigDecimal var1 = new BigDecimal(fen);
        BigDecimal var2 = new BigDecimal(100);
        BigDecimal var3 = var1.divide(var2);
        return var3.stripTrailingZeros().toPlainString();
    }

    /**
     * 将单位为分的金额转换为单位为元
     * @param fen 单位为分的字符型值
     * @return
     */
    public static BigDecimal fen2YuanDecimal(int fen) {
        BigDecimal var1 = new BigDecimal(fen);
        BigDecimal var2 = new BigDecimal(100);
        BigDecimal var3 = var1.divide(var2);
        return var3;
    }

    public static void main(String[] args) {
        int i = yuan2Fen(new BigDecimal(20));
        System.out.println(i);
    }

}
