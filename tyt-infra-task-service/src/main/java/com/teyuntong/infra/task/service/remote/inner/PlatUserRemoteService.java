package com.teyuntong.infra.task.service.remote.inner;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.inner.export.service.client.plat.user.service.PlatUserRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;


@Service
@FeignClient(name = "tyt-inner-export-service", path = "inner-export", contextId = "platUserRpcService", fallbackFactory = PlatUserRemoteService.PlatUserRemoteFallbackFactory.class)
public interface PlatUserRemoteService extends PlatUserRpcService {

    @Component
    class PlatUserRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<PlatUserRemoteService>{

        protected PlatUserRemoteFallbackFactory() {
            super(true, PlatUserRemoteService.class);
        }
    }
}
