package com.teyuntong.infra.task.service.common.mq.pojo;


import java.util.HashMap;
import java.util.Map;

public enum PublishTypeEnum {
    ELECTRODISCUSSION(1,"电议"),
    BUY_IT_NOW(2,"一口价");


    public final int code;
    public final String description;
    private PublishTypeEnum(int code, String description){
        this.code = code;
        this.description = description;
    }

    public static String getDescription(Integer code) {
        if(code == null){
            return "";
        }
        BondExceptionEnum type = getDeliveryType(code);
        return type==null ? "" : type.description;
    }

    private static Map<Integer,BondExceptionEnum> valueMap = new HashMap<Integer,BondExceptionEnum>();
    static{
        for(BondExceptionEnum type : BondExceptionEnum.values()){
            valueMap.put(type.code,type);
        }
    }
    public static BondExceptionEnum getDeliveryType(Integer code){
        return valueMap.get(code);
    }
}
