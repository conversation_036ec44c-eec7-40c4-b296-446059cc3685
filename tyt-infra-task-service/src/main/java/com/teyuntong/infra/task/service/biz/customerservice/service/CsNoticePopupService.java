package com.teyuntong.infra.task.service.biz.customerservice.service;


import com.teyuntong.infra.task.service.biz.customerservice.entity.CsNoticePopupTmpl;

/**
 * <p>
 * 客服系统弹窗通知模板 服务类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-09-29
 */
public interface CsNoticePopupService {
    /**
     * 根据code获取通知模板
     * @param code 标识
     * @return CsNoticePopupTmpl
     */
    CsNoticePopupTmpl getNoticeTmpl(String code);

    /**
     * 通知发送
     * @param opUserId 处理人id
     * @param noticeTmpl 通知模板
     */
    void saveCsNotice(Long opUserId, CsNoticePopupTmpl noticeTmpl);
}
