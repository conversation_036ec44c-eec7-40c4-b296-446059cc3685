package com.teyuntong.infra.task.service.remote.commonapi.api.service.impl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.google.common.collect.Maps;
import com.teyuntong.infra.task.service.remote.commonapi.api.bean.*;
import com.teyuntong.infra.task.service.remote.commonapi.api.service.CommonApiRemote;
import com.teyuntong.infra.task.service.remote.commonapi.api.service.CommonApiService;
import com.teyuntong.infra.task.service.utils.SecureLinkUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okio.Buffer;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;
import retrofit2.converter.scalars.ScalarsConverterFactory;

import java.time.Duration;
import java.util.HashMap;


@Slf4j
@Service
public class CommonApiServiceImpl extends BaseApiServiceImpl implements CommonApiService, InitializingBean {

    private final ObjectMapper mapper;

    private CommonApiRemote commonApiRemote;

    public CommonApiServiceImpl() {
        mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.configure(SerializationFeature.WRITE_NULL_MAP_VALUES, false);
    }

    private <T> T getData(Response<CommonApiRemote.CommonApiResponse<T>> response) {
        log.info("common-api orc response:{}", response);
        if (!response.isSuccessful()) {
            return null;
        }

        CommonApiRemote.CommonApiResponse<T> body = response.body();

        printFixLog("commonapi_getData", body);

        return body != null && body.isSuccess() ? body.getData() : null;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        // common-api 拦截器
        Interceptor commApiInterceptor = chain -> {
            Request oldRequest = chain.request();

            HttpUrl httpUrl = oldRequest.url()
                    .newBuilder()
                    .addQueryParameter("timestamp", String.valueOf(System.currentTimeMillis()))
                    // 拦截器动态修改host
                    //.host("dev.teyuntong.net").build();
                    .host(StringUtils.replacePattern(getPrivatePlatHost(), "(http|https)://(.*)", "$2")).build();

            // -----------------  验签 ---------------------
            HashMap<String, String> paramMap = Maps.newHashMap();
            for (String parameterName : httpUrl.queryParameterNames()) {
                paramMap.put(parameterName, httpUrl.queryParameter(parameterName));
            }

            String linkString = SecureLinkUtil.createLinkString(SecureLinkUtil.paraFilter(paramMap));

            final Request copy = oldRequest.newBuilder().build();
            if (copy.body() != null) {
                final Buffer buffer = new Buffer();
                copy.body().writeTo(buffer);

                String body = buffer.readUtf8();

                if (StringUtils.isNotBlank(body)) {

                    linkString += body;

                }
            }

            String sign = SecureLinkUtil.sign(linkString, RemoteApiConstant.CommonApi.secret_key, "MD5");

            // sign header
            Headers headers = oldRequest.headers().newBuilder()
                    .add("sign", sign)
                    .add("tyt-sign-type", "stream")
                    .build();
            // -----------------  验签结束 -------------------

            // 构建新请求
            Request request = oldRequest.newBuilder().url(httpUrl).headers(headers).build();
            return chain.proceed(request);
        };

        OkHttpClient httpClient = new OkHttpClient.Builder()
                .readTimeout(Duration.ofSeconds(15))
                .connectTimeout(Duration.ofSeconds(5))
                .addInterceptor(commApiInterceptor)
                .build();

        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(getPrivatePlatHost() + "/common-api/")
                .addConverterFactory(ScalarsConverterFactory.create())
                .addConverterFactory(JacksonConverterFactory.create(mapper))
                .client(httpClient).build();

        commonApiRemote = retrofit.create(CommonApiRemote.class);
    }

    @Override
    public CDRsResp getCDR(CDRReq req) {
        try {
            Response<CommonApiRemote.CommonApiResponse<CDRsResp>> response = commonApiRemote.getCDR(req).execute();
            return getData(response);
        } catch (Exception e) {
            log.error("common-api 获取话单记录失败", e);
            return null;
        }
    }

    @Override
    public PhoneLocaleResp mobile(String mobile) {
        try {
            Response<CommonApiRemote.CommonApiResponse<PhoneLocaleResp>> response = commonApiRemote.mobile(mobile).execute();
            return getData(response);
        } catch (Exception e) {
            log.error("common-api 获取手机号归属地失败", e);
            return null;
        }
    }

    @Override
    public void autoDeleteAutoCallTask() {
        try {
            Response<CommonApiRemote.CommonApiResponse<Object>> response = commonApiRemote.autoDeleteAutoCallTask().execute();
        } catch (Exception e) {
            log.error("common-api 清除已结束的专车派单自动外呼任务失败", e);
        }
    }

    @Override
    public String getRecordUrl(String recordName, String subId) {
        try {
            Response<CommonApiRemote.CommonApiResponse<String>> response = commonApiRemote.getRecordUrl(recordName, subId).execute();
            return getData(response);
        } catch (Exception e) {
            log.error("common-api 获取通话记录新URL失败", e);
            return null;
        }
    }

    public static void printFixLog(String label, Object info){
        String logText = "";

        if(info != null){
            if(info instanceof String){
                logText = (String) info;
            }else {
                logText = JSON.toJSONString(info);
            }

        }

        int maxLength = 1000;

        if (logText != null && logText.length() > maxLength) {
            logText = logText.substring(0, maxLength - 1) + "...skipping...";
        }

        log.info(label + " : {}", logText);
    }


    /**
     * AI自动外呼
     * @param autoCallTaskRequest
     */
    @Override
    public Object autoCallTask(AutoCallTaskRequest autoCallTaskRequest) {
        try {
            Response<CommonApiRemote.CommonApiResponse<Object>> response =
                    commonApiRemote.autoCallTask(autoCallTaskRequest).execute();
            return getData(response);
        } catch (Exception e) {
            log.error("common-api 调用AI自动外呼接口失败", e);
            return null;
        }
    }
}
