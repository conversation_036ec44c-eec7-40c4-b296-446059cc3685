package com.teyuntong.infra.task.service.common.tmpl.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.infra.task.service.common.tmpl.entity.MessageTmplDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TytMessageTmplMapper extends BaseMapper<MessageTmplDO> {
    MessageTmplDO getContent(@Param("tmplKey") String tmplKey);

    MessageTmplDO getPushContent(@Param("tmplKey") String key);
}
