package com.teyuntong.infra.task.service.common.exception;

import com.teyuntong.infra.task.service.common.mq.pojo.ResponseEnum;
import com.teyuntong.infra.task.service.common.response.ResponseCode;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

/**
 * 自定义异常集合
 */
public class TytException extends RuntimeException {
    @Getter
    @Setter
    private Integer errorCode;
    @Getter
    @Setter
    private String errorMsg;

    protected TytException(String msg) {
        super(msg);
    }

    protected TytException(Throwable te) {
        super(te);
    }

    protected void initException(ResponseCode error, String errorMsg){
        if(error == null){
            error = ResponseEnum.sys_error.info();
        }

        this.errorCode = error.getCode();

        if(StringUtils.isBlank(errorMsg)){
            this.errorMsg = error.getMsg();
        }else{
            this.errorMsg = errorMsg;
        }
    }

    /**
     * 创建捕获到的异常
     * @param responseCode
     * @param errorMsg
     * @return
     */
    public static TytException createException(ResponseCode responseCode, String errorMsg){

        if(responseCode == null){
            responseCode = ResponseEnum.sys_error.info();
        }

        if(errorMsg == null){
            errorMsg = responseCode.getMsg();
        }

        TytException oe = new TytException(responseCode.getMsg());

        oe.initException(responseCode, errorMsg);

        return oe;
    }

    /**
     * 创建捕获到的异常
     * @param responseCode
     * @return
     */
    public static TytException createException(ResponseCode responseCode){
        TytException oe = createException(responseCode, null);
        return oe;
    }

    /**
     * 创建捕获到的异常
     * @return
     */
    public static TytException createException(){
        TytException oe = createException(null, null);
        return oe;
    }

    /**
     * 创建捕获到的异常
     * @param te
     * @return
     */
    public static TytException createException(Throwable te){
        TytException oe = null;

        if(te instanceof TytException){
            oe = (TytException)te;
        }else{
            oe = new TytException(te);
            oe.initException(null, null);
        }

        return oe;
    }
    
}
