package com.teyuntong.infra.task.service.utils;

import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class PageCalculateUtil {

    public int pageNumber(int pageSize,int total){
        return total%pageSize == 0 ? total/pageSize : total/pageSize+1;
    }

    public Map<String,Object> start2EndIndex(int currentPage,int pageSize){
        Map<String,Object> map = new HashMap<>();
        int startIndex = (currentPage - 1) * pageSize;
        int endIndex = currentPage * pageSize;
        map.put("startIndex",startIndex);
        map.put("endIndex",endIndex);
        return map;
    }
}
