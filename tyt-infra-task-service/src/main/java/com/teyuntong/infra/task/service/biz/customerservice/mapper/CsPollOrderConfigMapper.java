package com.teyuntong.infra.task.service.biz.customerservice.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.infra.task.service.biz.customerservice.entity.CsPollOrderConfig;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 轮单配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-09-29
 */
@Mapper
public interface CsPollOrderConfigMapper extends BaseMapper<CsPollOrderConfigMapper> {
    /**
     * 获取轮单配置
     * @return CsPollOrderConfig
     */
    CsPollOrderConfig getConfig();
}
