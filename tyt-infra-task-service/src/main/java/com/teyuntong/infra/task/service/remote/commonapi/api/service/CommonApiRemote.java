package com.teyuntong.infra.task.service.remote.commonapi.api.service;

import com.teyuntong.infra.task.service.remote.commonapi.api.bean.AutoCallTaskRequest;
import com.teyuntong.infra.task.service.remote.commonapi.api.bean.CDRReq;
import com.teyuntong.infra.task.service.remote.commonapi.api.bean.CDRsResp;
import com.teyuntong.infra.task.service.remote.commonapi.api.bean.PhoneLocaleResp;
import lombok.Data;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

import java.util.Date;

public interface CommonApiRemote {

    @Data
    class CommonApiResponse<T> {
        private int code;
        private String msg;
        private T data;
        private Date timestamp;

        public boolean isSuccess() {
            return code == 200;
        }
    }


    @POST("axb/getCDR")
    Call<CommonApiResponse<CDRsResp>> getCDR(@Body CDRReq req);

    @POST("cticloud/task/autoDeleteAutoCallTask")
    Call<CommonApiResponse<Object>> autoDeleteAutoCallTask();

    @GET("mobile")
    Call<CommonApiResponse<PhoneLocaleResp>> mobile(@Query("mobile") String mobile);

    /**
     * AI自动外呼接口
     * @param autoCallTaskRequest
     * @return
     */
    @POST("cticloud/task/autoCallTask")
    Call<CommonApiResponse<Object>> autoCallTask(@Body AutoCallTaskRequest autoCallTaskRequest);

    @GET("vnc/getRecordUrl")
    public Call<CommonApiResponse<String>> getRecordUrl(@Query("recordName") String recordName, @Query("subId") String subId);

}
