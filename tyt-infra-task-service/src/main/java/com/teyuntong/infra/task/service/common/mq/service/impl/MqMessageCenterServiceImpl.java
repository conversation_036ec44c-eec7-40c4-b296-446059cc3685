package com.teyuntong.infra.task.service.common.mq.service.impl;

import com.teyuntong.infra.task.service.common.mq.mybatis.entity.TytMqMessageCenter;
import com.teyuntong.infra.task.service.common.mq.mybatis.mapper.MqMessageCenterMapper;
import com.teyuntong.infra.task.service.common.mq.service.MqMessageCenterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class MqMessageCenterServiceImpl implements MqMessageCenterService {

    @Autowired
    private MqMessageCenterMapper mqMessageCenterMapper;

    @Override
    public boolean insertMessageCenter(TytMqMessageCenter tytMqMessageCenter) {
        try {
            mqMessageCenterMapper.insert(tytMqMessageCenter);
            return true;
        }catch (Exception e){
            log.error("insertMessageCenter_error:", e);
            return false;
        }
    }

}
