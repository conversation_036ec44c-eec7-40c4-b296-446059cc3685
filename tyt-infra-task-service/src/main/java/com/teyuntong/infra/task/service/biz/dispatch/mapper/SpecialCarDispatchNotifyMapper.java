package com.teyuntong.infra.task.service.biz.dispatch.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.infra.task.service.biz.dispatch.entity.SpecialCarDispatchNotifyDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 * <p>
 * 专车派单通知调度表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-09-19
 */
@Mapper
public interface SpecialCarDispatchNotifyMapper extends BaseMapper<SpecialCarDispatchNotifyDO> {

    SpecialCarDispatchNotifyDO selectByGoodsId(@Param("tsId") Long tsId);

    SpecialCarDispatchNotifyDO selectByDispatchId(@Param("dispatchId") Long dispatchId);
}
