package com.teyuntong.infra.task.service.remote.trade.service;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.trade.service.client.protocol.service.TransportProtocolRpcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024/03/07 13:41
 */
@Component
@FeignClient(name = "tyt-trade-service", contextId = "protocolContentRpcService",path = "trade",fallbackFactory = TransportProtocolRemoteService.TransportProtocolFallBack.class)
public interface TransportProtocolRemoteService extends TransportProtocolRpcService {


    @Slf4j
    @Component
    public class TransportProtocolFallBack  extends LogAndReturnNullRemoteFallbackFactory<TransportProtocolRpcService> {
        protected TransportProtocolFallBack() {
            super(true, TransportProtocolRpcService.class);
        }
    }
}
