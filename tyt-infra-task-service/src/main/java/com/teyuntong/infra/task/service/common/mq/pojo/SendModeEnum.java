package com.teyuntong.infra.task.service.common.mq.pojo;

public enum SendModeEnum {

    manual(0, "手动"),
    timer(1, "定时自动发送");

    private Integer code;
    private String zhName;

    private SendModeEnum(Integer code, String zhName) {
        this.code = code;
        this.zhName = zhName;
    }

    public static SendModeEnum getEnum(Integer code) {
        if (code == null) {
            return null;
        } else {
            SendModeEnum[] var1 = values();
            int var2 = var1.length;

            for(int var3 = 0; var3 < var2; ++var3) {
                SendModeEnum statusEnum = var1[var3];
                if (statusEnum.getCode().equals(code)) {
                    return statusEnum;
                }
            }

            return null;
        }
    }

    public static String getEnumName(Integer code) {
        SendModeEnum anEnum = getEnum(code);
        return anEnum != null ? anEnum.getZhName() : "";
    }

    public static SendModeEnum getEnumByZhName(String zhName) {
        if (zhName == null) {
            return null;
        } else {
            SendModeEnum[] var1 = values();
            int var2 = var1.length;

            for(int var3 = 0; var3 < var2; ++var3) {
                SendModeEnum oneEnum = var1[var3];
                if (oneEnum.getZhName().equalsIgnoreCase(zhName)) {
                    return oneEnum;
                }
            }

            return null;
        }
    }

    public Integer getCode() {
        return this.code;
    }

    public String getZhName() {
        return this.zhName;
    }
}
