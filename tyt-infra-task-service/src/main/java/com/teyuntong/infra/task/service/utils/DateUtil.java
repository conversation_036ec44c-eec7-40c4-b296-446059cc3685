package com.teyuntong.infra.task.service.utils;

import lombok.extern.slf4j.Slf4j;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/01/10 15:28
 */
@Slf4j
public class DateUtil {


    public static final String YYYMMDD = "yyyy-MM-dd";
    public static final String YYYMMDDHHMMSS = "yyyy-MM-dd HH:mm:ss";

    /**
     * 当前时间加上指定天数
     *
     * @param day 指定的天数
     * @return
     */
    public static String plusDaysByYYYMMDD(int day) {
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern(YYYMMDD);
        // 获取当前时间
        LocalDate now = LocalDate.now();
        // 加**天
        LocalDate yesterday = now.plusDays(day);
        return yesterday.format(fmt);
    }

    /**
     * 指定时间 加上指定天数
     *
     * @param day  天数
     * @param date 从哪天开始加
     * @return
     */
    public static String plusDaysByYYYYMMDD(int day, String date) {
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern(YYYMMDD);
        // 减去**天
        LocalDate parse1 = LocalDate.parse(date);
        LocalDate localDate = parse1.plusDays(day);
        return localDate.format(fmt);
    }

    /**
     * 当前时间减去指定天数
     *
     * @param day 天数
     * @return
     */
    public static String minusDaysByYYYMMDD(int day) {
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern(YYYMMDD);
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        // 减去**天
        LocalDateTime yesterday = now.minusDays(day);
        return yesterday.format(fmt);
    }

    /**
     * 指定时间 加上指定天数
     *
     * @param day  天数
     * @param date 从哪天开始加
     * @return
     */
    public static String minusDaysByYYYYMMDD(int day, String date) {
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern(YYYMMDD);
        // 减去**天
        LocalDate parse1 = LocalDate.parse(date);
        LocalDate localDate = parse1.minusDays(day);
        return localDate.format(fmt);
    }

    /**
     * 获取n小时前的时间
     *
     * @param hour 小时
     * @return Date 时间
     */
    public static Date getHourAgo(int hour) {
        // 获取当前时间
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.HOUR_OF_DAY, -hour);
        Date hourAgo = calendar.getTime();
        return hourAgo;
    }

    public static Date addHours(Date time, int hours) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(time);
        cal.add(Calendar.HOUR, hours);
        return cal.getTime();
    }

    public static Date addMinutes(Date time, int minutes) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(time);
        cal.add(Calendar.MINUTE, minutes);
        return cal.getTime();
    }

    public static Date addSeconds(Date time, int seconds) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(time);
        cal.add(Calendar.SECOND, seconds);
        return cal.getTime();
    }

    public static Date parseDate(String dateStr, String format) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            return sdf.parse(dateStr);
        } catch (ParseException e) {
            log.error("parseDate error， date:{}, format:{}", dateStr, format, e);
        }
        return null;
    }

    /**
     * 获取当前小时数
     *
     * @return
     */
    public static int getHours() {
        Calendar calendar = Calendar.getInstance();
        return calendar.get(Calendar.HOUR_OF_DAY);
    }

    public static int getDays(Date startTime, Date endTime) {
        return (int) ((endTime.getTime() - startTime.getTime()) / 1000 / 60 / 60 / 24);
    }


    public static void main(String[] args) {
        System.out.println(addMinutes(new Date(), -59));
        System.out.println(addTime(new Date(), Calendar.DAY_OF_YEAR, -30));
    }

    /**
     * 获取近一年时间
     *
     * @return
     */
    public static Date addYear() {
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.YEAR, -1);
        Date year = c.getTime();
        return year;
    }


    /**
     * 昨天0点
     *
     * @param
     * @return Date
     */
    public static Date yesterdayStart() {
        Calendar calendar = Calendar.getInstance();
        // 将日期设置为昨天
        calendar.add(Calendar.DATE, -1);
        // 将时间设置为昨天的0点
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 昨天23点
     *
     * @param
     * @return Date
     */
    public static Date yesterdayEnd() {
        Calendar calendar = Calendar.getInstance();

        calendar.add(Calendar.DATE, -1);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    /**
     * 天开始时间
     *
     * @param date
     * @return
     */
    public static Date startOfDay(Date date) {

        Calendar todayBegin = Calendar.getInstance();
        todayBegin.setTime(date);
        todayBegin.set(Calendar.HOUR_OF_DAY, 0); // Calendar.HOUR 12小时制
        todayBegin.set(Calendar.MINUTE, 0);
        todayBegin.set(Calendar.SECOND, 0);
        todayBegin.set(Calendar.MILLISECOND, 0);

        Date time = todayBegin.getTime();

        return time;
    }

    /**
     * 天结束时间
     *
     * @param date
     * @return
     */
    public static Date endOfDay(Date date) {

        Calendar todayEnd = Calendar.getInstance();
        todayEnd.setTime(date);
        todayEnd.set(Calendar.HOUR_OF_DAY, 23); // Calendar.HOUR 12小时制
        todayEnd.set(Calendar.MINUTE, 59);
        todayEnd.set(Calendar.SECOND, 59);
        todayEnd.set(Calendar.MILLISECOND, 0);

        Date time = todayEnd.getTime();

        return time;
    }

    /**
     * 日期计算
     *
     * @param date   时间
     * @param type   Calendar.DAY_OF_MONTH
     * @param number 可以为负数
     * @return
     */
    public static Date addTime(Date date, int type, int number) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        calendar.add(type, number);

        Date resultDate = calendar.getTime();

        return resultDate;
    }

    public static String getNowTime(String formatType) {

        Date date = new Date();

        SimpleDateFormat format = new SimpleDateFormat(formatType);

        String resultDay = format.format(date);

        return resultDay;
    }
}
