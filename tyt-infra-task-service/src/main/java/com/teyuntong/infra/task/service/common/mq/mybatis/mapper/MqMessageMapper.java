package com.teyuntong.infra.task.service.common.mq.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.infra.task.service.common.mq.mybatis.entity.MqMessageDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 存储发送到mq的处理消息,用于保证每条消息都得到正确的处理 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-1-20 17:30:08
 */
@Mapper
public interface MqMessageMapper extends BaseMapper<MqMessageDO> {

    /**
     * 根据消息状态获取消息
     *
     * <AUTHOR>
     * @param mqStatus 消息状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return List<MqMessageDO>
     */
    List<MqMessageDO> getMqMessageByStatus(@Param("mqStatus") Integer mqStatus, @Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 修改发送次数+1
     *
     * <AUTHOR>
     * @param messageSerailNum 消息序列号
     * @return void
     */
     void updateMqMessageSendNbr(@Param("messageSerailNum") String messageSerailNum);

    /**
     * 修改消息状态
     *
     * <AUTHOR>
     * @param messageSerailNum 消息序列号
     * @param mqMessageStatus 消息状态
     * @return void
     */
    void updateMqMessageStatus(@Param("messageSerailNum") String messageSerailNum, @Param("mqMessageStatus") Integer mqMessageStatus);
    List<MqMessageDO> getMqMessageByStatusForDate(@Param("dealStatus") int status, @Param("beginTime") String beginTime, @Param("endTime") String endTime);

    /**
     * 根据消息类型以及时间更新初始失败的mq消息状态
     * <AUTHOR>
     * @param messageType 消息类型
     * @param beginTime 开始时间
     * @param endTime 结束时间
     */
    void  updateDealFailMqMessage( @Param("messageType") int messageType, @Param("beginTime") String beginTime, @Param("endTime") String endTime);

}
