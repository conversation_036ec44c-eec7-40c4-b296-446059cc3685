package com.teyuntong.infra.task.service.common.tmpl.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 消息模版表
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-08-16
 */
@Getter
@Setter
@TableName("tyt_message_tmpl")
public class MessageTmplDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 模版类型 0是sms 1中列表消息
     */
    private String type;

    /**
     * 内容类型1string 2 json
     */
    private String contentType;

    /**
     * 模版key
     */
    private String tmplKey;

    /**
     * 模版内容 如果是列表消息 保存json
     */
    private String content;

    /**
     * 是否有效0是有效1是无效
     */
    private String status;

    /**
     * 创建时间
     */
    private Date ctime;

    /**
     * 备注
     */
    private String remark;
}
