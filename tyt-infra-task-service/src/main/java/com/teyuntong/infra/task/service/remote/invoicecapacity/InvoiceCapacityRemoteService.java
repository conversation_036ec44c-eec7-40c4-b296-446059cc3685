package com.teyuntong.infra.task.service.remote.invoicecapacity;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.user.service.client.invoicecapacity.service.InvoiceCapacityRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/02/20 10:23
 */
@FeignClient(name = "tyt-user-service", path = "user", contextId = "invoiceCapacityRpcService", fallbackFactory = InvoiceCapacityRemoteService.InvoiceCapacityRemoteFallbackFactory.class)
public interface InvoiceCapacityRemoteService extends InvoiceCapacityRpcService {

    @Component
    class InvoiceCapacityRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<InvoiceCapacityRemoteService> {
        protected InvoiceCapacityRemoteFallbackFactory() {
            super(true, InvoiceCapacityRemoteService.class);
        }
    }
}
