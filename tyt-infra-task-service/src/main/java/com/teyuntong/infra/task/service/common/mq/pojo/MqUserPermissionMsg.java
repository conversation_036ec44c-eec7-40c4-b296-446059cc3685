package com.teyuntong.infra.task.service.common.mq.pojo;


import com.teyuntong.infra.task.service.biz.user.goods.entity.UserBuyGoodsDO;
import com.teyuntong.infra.task.service.biz.user.permission.mybatis.entity.ExposureRecordInfo;
import lombok.Data;

/**
 * @Description  分配用户权益mq消息
 * <AUTHOR>
 * @Date  2019/7/2 15:34
 * @Param
 * @return
 **/
@Data
public class MqUserPermissionMsg extends MqBaseMessageBean {

    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 商品ID
     */
    private Long goodsId;
    /**
     * 变更类型(1.购买 2.赠送 3.次卡用完 4.时间到期 )
     */
    private Integer changeType;
    /**
     * 商品订单信息
     */
    private UserBuyGoodsDO userBuyGoods;
    /**
     * 订单号
     */
    private String ordNum;
    //操作人id
    private Long operatorId;
    //操作人姓名
    private String operatorName;
    /**
     * 曝光卡信息
     */
    private ExposureRecordInfo exposureRecordInfo;


}
