package com.teyuntong.infra.task.service.remote.user.service;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.user.service.client.permission.service.UserPermissionRpcService;
import com.teyuntong.user.service.client.user.service.UserRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * 用户相关Remote
 *
 * <AUTHOR>
 * @since 2024-9-23 10:47:47
 */
@FeignClient(name = "tyt-user-service", path = "user", contextId = "userPermissionRpcService", fallbackFactory = UserPermissionRemoteService.UserPermissionRemoteServiceFallback.class)
public interface UserPermissionRemoteService extends UserPermissionRpcService {

    @Component
    class UserPermissionRemoteServiceFallback extends LogAndReturnNullRemoteFallbackFactory<UserRpcService> {

        public UserPermissionRemoteServiceFallback() {
            super(true, UserRpcService.class);
        }
    }
}