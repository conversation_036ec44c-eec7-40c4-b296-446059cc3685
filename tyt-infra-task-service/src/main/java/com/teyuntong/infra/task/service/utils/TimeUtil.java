package com.teyuntong.infra.task.service.utils;


import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;

/**
 * User: Administrator
 * Date: 13-12-8
 * Time: 下午8:07
 */
public class TimeUtil {

    private static final ThreadLocal<SimpleDateFormat> SDF_DATE_MM = new ThreadLocal<SimpleDateFormat>() {
        @Override
        protected SimpleDateFormat initialValue() {
            return new SimpleDateFormat("yyyyMMddHHmm");
        }

    };

    private static final ThreadLocal<SimpleDateFormat> SDF_DATE = new ThreadLocal<SimpleDateFormat>() {
        @Override
        protected SimpleDateFormat initialValue() {
            return new SimpleDateFormat("yyyy-MM-dd");
        }

    };

    private static final ThreadLocal<SimpleDateFormat> SDF_DATE_MONTH = new ThreadLocal<SimpleDateFormat>() {
        @Override
        protected SimpleDateFormat initialValue() {
            return new SimpleDateFormat("yyyy-MM");
        }

    };

    private static final ThreadLocal<SimpleDateFormat> SDF_DATETIME = new ThreadLocal<SimpleDateFormat>() {
        @Override
        protected SimpleDateFormat initialValue() {
            return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        }

    };

    private static final ThreadLocal<SimpleDateFormat> SDF_WEEK_DATETIME = new ThreadLocal<SimpleDateFormat>() {
        @Override
        protected SimpleDateFormat initialValue() {
            return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss E");
        }

    };
    private static final ThreadLocal<SimpleDateFormat> SDF_TIME = new ThreadLocal<SimpleDateFormat>() {
        @Override
        protected SimpleDateFormat initialValue() {
            return new SimpleDateFormat("HH:mm:ss");
        }

    };
    private static final ThreadLocal<SimpleDateFormat> SDF_DATE_ = new ThreadLocal<SimpleDateFormat>() {
        @Override
        protected SimpleDateFormat initialValue() {
            return new SimpleDateFormat("yyyyMMdd");
        }

    };
    private static final ThreadLocal<SimpleDateFormat> SDF_DATE_MIN = new ThreadLocal<SimpleDateFormat>() {
        @Override
        protected SimpleDateFormat initialValue() {
            return new SimpleDateFormat("yyMMdd");
        }

    };
    private static final ThreadLocal<SimpleDateFormat> SDF_TIME_NO_POINT = new ThreadLocal<SimpleDateFormat>() {
        @Override
        protected SimpleDateFormat initialValue() {
            return new SimpleDateFormat("HHmmss");
        }

    };
    private static final ThreadLocal<SimpleDateFormat> SDF_DATETIMEFORMIN = new ThreadLocal<SimpleDateFormat>() {
        @Override
        protected SimpleDateFormat initialValue() {
            return new SimpleDateFormat("yyyy-MM-dd HH:mm");
        }

    };

    private static final ThreadLocal<SimpleDateFormat> YYYYMMDDMMSS = new ThreadLocal<SimpleDateFormat>() {
        @Override
        protected SimpleDateFormat initialValue() {
            return new SimpleDateFormat("yyyyMMddHHmmss");
        }

    };

    private static final ThreadLocal<SimpleDateFormat> ZH_DAY_FORMAT = new ThreadLocal<SimpleDateFormat>() {
        @Override
        protected SimpleDateFormat initialValue() {
            return new SimpleDateFormat("yyyy年MM月dd日");
        }

    };

    /**
     * 获取昨天日期的字符串
     *
     * @return
     */
    public static String yesterDayToString() {
        return SDF_DATE.get().format(yesterDay());
    }

    /**
     * 获取昨天日期
     *
     * @return
     */
    public static Date yesterDay() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        return calendar.getTime();
    }

    /**
     * 向前推移时间
     *
     * @param offset 偏移量
     * @return
     */
    public static Date forwardDate(int offset) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -offset);
        SDF_DATETIME.get().format(calendar.getTime());
        return calendar.getTime();
    }

    public static Date forwardDateStr(int offset) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, offset);
        SDF_DATETIME.get().format(calendar.getTime());
        return calendar.getTime();
    }

    /**
     * Thu May 21 00:00:00 CST 2015
     *
     * @return
     */
    public static Date today() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }
    /**
     * 当天结束时间
     */
    public static Date getEndOfToday() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }
    public static Date todayForHour(int hour) {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.HOUR_OF_DAY, hour);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    public static Date todayForMinute(int MINUTE) {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, MINUTE);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    /**
     * 获取今天字符串日期，格式：yyyy-MM-dd
     *
     * @return
     */
    public static String getTodayStr() {
        return formatDate(today());
    }

    /**
     * Sun May 31 00:00:00 CST 2015
     *
     * @param d 提前或滞后的天数
     * @return
     */
    public static Date dateDiff(int d) {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        cal.add(Calendar.DAY_OF_YEAR, d);
        return cal.getTime();
    }

    /**
     * 今天之后某年的时期
     *
     * @param year
     * @return yyyy-MM-dd
     */
    public static String addYear(int year) {
        Calendar rightNow = Calendar.getInstance();
        rightNow.add(Calendar.YEAR, +year);//日期减1年
        return SDF_DATE.get().format(rightNow.getTime());
    }

    /**
     * 计算天数
     *
     * @param startTime
     * @param endTime
     * @return
     * @throws Exception
     */
    public int getDays(String startTime, String endTime) throws Exception {
        return (int) ((SDF_DATE.get().parse(endTime).getTime() - SDF_DATE.get().parse(startTime).getTime()) / (24 * 60 * 60 * 1000));

    }

    /**
     * String类型的日期转换为Timestamp类型
     *
     * @param time
     * @return
     */
    public Timestamp StringToTimestamp(String time) {
        return Timestamp.valueOf(time);
    }

    /**
     * Sun May 31 14:17:28 CST 2015
     *
     * @param d 提前或滞后的天数
     * @return
     */
    public static Date addDay(int d) {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_YEAR, d);
        return cal.getTime();
    }


    /**
     * 计算时间加天数
     *
     * @param time
     * @param d
     * @return
     * @throws Exception
     */
    public static Date addDay(String time, int d) throws Exception {
        Calendar cal = Calendar.getInstance();
        cal.setTime(SDF_DATE.get().parse(time));
        cal.add(Calendar.DAY_OF_YEAR, d);
        return cal.getTime();
    }

    /**
     * 计算时间加天数
     *
     * @param time
     * @param d
     * @return
     * @throws Exception
     */
    public static Date addDay(Date time, int d) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(time);
        cal.add(Calendar.DAY_OF_YEAR, d);
        return cal.getTime();
    }

    /**
     * 计算时间加小时数
     *
     * @param time
     * @param h
     * @return
     * @throws Exception
     */
    public static Date addHour(Date time, int h) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(time);
        cal.add(Calendar.HOUR_OF_DAY, h);
        return cal.getTime();
    }

    /**
     * 计算时间加分钟数
     *
     * @param time
     * @param h
     * @return
     * @throws Exception
     */
    public static Date addMinute(Date time, int h) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(time);
        cal.add(Calendar.MINUTE, h);
        return cal.getTime();
    }


    public static String formatDateTime(Date date) {
        return SDF_DATETIME.get().format(date);

    }

    public static String formatDateMonthTime(Date date) {
        return SDF_DATE_MONTH.get().format(date);

    }

    public static String formatWeekDateTime(Date date) {
        return SDF_WEEK_DATETIME.get().format(date);

    }

    public static String formatDateForMM(Date date) {
        return SDF_DATE_MM.get().format(date);

    }

    public static String formatDateForYyyyMMddHHmmss(Date date) {
        return YYYYMMDDMMSS.get().format(date);
    }

    public static String formatDate_(Date date) {
        return SDF_DATE_.get().format(date);

    }

    public static String formatDateForMin(Date date) {
        return SDF_DATETIMEFORMIN.get().format(date);

    }

    public static String formatTime(Date date) {
        return SDF_TIME.get().format(date);
    }

    public static String formatDate(Date date) {
        return SDF_DATE.get().format(date);
    }

    public static Date parseString(String time) throws Exception {
        return SDF_DATE.get().parse(time);
    }

    public static Date parseyyyymmdd(String time) throws Exception {
        return SDF_DATE_.get().parse(time);
    }

    public static Date parseForDateTime(String time) throws Exception {
        return SDF_DATETIME.get().parse(time);
    }

    public static Date parseZhDate(String dateStr) throws Exception {
        return ZH_DAY_FORMAT.get().parse(dateStr);
    }

    /**
     * HH:MM:SS
     *
     * @param time
     * @return
     */
    public static int time2Int(String time) {
        return Short.parseShort(time.substring(0, 2)) * 60 + Short.parseShort(time.substring(3, 5));
    }

    /**
     * @param time
     * @return
     */
    public static Timestamp string2Timestamp(String time) {
        try {
            return new Timestamp(TimeUtil.SDF_DATE.get().parse(time).getTime());
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }


    public static Timestamp getTimeStamp() {
        return new Timestamp(System.currentTimeMillis());
    }

    public static int getDays(Timestamp startTime, Timestamp endTime) {
        return (int) ((endTime.getTime() - startTime.getTime()) / 1000 / 60 / 60 / 24);
    }

    public static int getDaysByDate(Date startTime, Date endTime) {
        return (int) ((startTime.getTime() - endTime.getTime()) / (24 * 60 * 60 * 1000));
    }

    public static Timestamp addYearDays(int year, int days) {
        Calendar rightNow = Calendar.getInstance();
        rightNow.add(Calendar.YEAR, year);//日期减1年
        rightNow.add(Calendar.DAY_OF_YEAR, days);//日期加10天
        return Timestamp.valueOf(SDF_DATETIME.get().format(rightNow.getTime()));
    }

    public static Timestamp stampAdd(Timestamp time, Integer day) {
        try {
            Date date = SDF_DATETIME.get().parse(time.toString());
            Calendar cal = Calendar.getInstance();
            cal.setTime(date);
            cal.add(cal.DAY_OF_YEAR, day);//日期加10天
            date = cal.getTime();
            return new Timestamp(date.getTime());
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static Date stampAddDay(Date date, Integer day) {
        try {
            Calendar cal = Calendar.getInstance();
            cal.setTime(date);
            cal.add(cal.DAY_OF_YEAR, day);
            date = cal.getTime();
            return date;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static Date stampAddHour(Date date, int hour) {
        try {
            //Date date=SDF_DATETIME.parse(time.toString());
            Calendar cal = Calendar.getInstance();
            cal.setTime(date);
            cal.add(cal.HOUR_OF_DAY, hour);
            date = cal.getTime();
            return date;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static Date stampAddMinute(Date date, int minute) {
        try {
            //Date date=SDF_DATETIME.parse(time.toString());
            Calendar cal = Calendar.getInstance();
            cal.setTime(date);
            cal.add(cal.MINUTE, minute);
            cal.set(Calendar.SECOND, 0);
            cal.set(Calendar.MILLISECOND, 0);
            date = cal.getTime();
            return date;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * @return java.sql.Timestamp
     * @Description 时间戳格式日期加年
     * <AUTHOR>
     * @Date 2019/6/27 19:50
     * @Param [time, year]
     **/
    public static Timestamp stampAddYear(Timestamp time, Integer year) {
        try {
            Date date = SDF_DATETIME.get().parse(time.toString());
            Calendar cal = Calendar.getInstance();
            cal.setTime(date);
            cal.add(Calendar.YEAR, year);// 日期加10天
            date = cal.getTime();
            return new Timestamp(date.getTime());
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * @return java.sql.Timestamp
     * @Description 时间戳格式日期加月
     * <AUTHOR>
     * @Date 2019/6/27 19:49
     * @Param [time, month]
     **/
    public static Timestamp stampAddMonth(Timestamp time, Integer month) {
        try {
            Date date = SDF_DATETIME.get().parse(time.toString());
            Calendar cal = Calendar.getInstance();
            cal.setTime(date);
            cal.add(Calendar.MONTH, month);// 日期加月
            date = cal.getTime();
            return new Timestamp(date.getTime());
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static Date dateAddMonth(Date time, Integer month) {
        try {
            Calendar cal = Calendar.getInstance();
            cal.setTime(time);
            cal.add(Calendar.MONTH, month);// 日期加月
            time = cal.getTime();
            return time;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 计算两个日期之间相差的天数
     *
     * @param smdate 较小的时间
     * @param bdate  较大的时间
     * @return 相差天数
     * @throws ParseException
     */
    public static int daysBetween(Date smdate, Date bdate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            smdate = sdf.parse(sdf.format(smdate));
            bdate = sdf.parse(sdf.format(bdate));
        } catch (ParseException e) {
            throw new RuntimeException("daysBetween Exception");
        }

        Calendar cal = Calendar.getInstance();
        cal.setTime(smdate);
        long time1 = cal.getTimeInMillis();
        cal.setTime(bdate);
        long time2 = cal.getTimeInMillis();
        long betweenDays = (time2 - time1) / (1000 * 3600 * 24);
        return Integer.parseInt(String.valueOf(betweenDays));
    }

    public static int diffString(Timestamp end) {
        Date endDate = null, startDate = null;
        try {
            endDate = SDF_DATE.get().parse(SDF_DATE.get().format(end));
            startDate = SDF_DATE.get().parse(SDF_DATE.get().format(new Date()));
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return (int) ((endDate.getTime() - startDate.getTime()) / 1000 / 60 / 60 / 24);
    }

    /**
     * 日期加年数
     *
     * @param date
     * @param year
     * @return
     */
    public static Date dateAddYear(Date date, Integer year) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(cal.YEAR, year);// 日期加10天
        return cal.getTime();
    }

    public static Date parseDateString(String time) throws Exception {
        return SDF_DATETIME.get().parse(time);
    }

    /**
     * 指定时间与当前时间比较大小
     *
     * @param time
     * @return 当前时间以前返回true，否则返回false
     */
    public static boolean isStartToday(Date time) {
        boolean b = true;
        if (time == null) { //如果为null，则从今日开始
            return b;
        }
        long expTime = time.getTime();
        long nowTime = new Date().getTime();
        if (expTime < nowTime) {
            b = true;
        } else {
            b = false;
        }
        return b;
    }

    public static String formatDateMin(Date date) {
        return SDF_DATE_MIN.get().format(date);

    }

    public static int getDays() {
        Calendar calendar = Calendar.getInstance();
        return calendar.get(Calendar.DAY_OF_MONTH);
    }

    /**
     * 判断当前时间是否在from，to之内
     *
     * @param from 开始日期
     * @param to   结束日期
     * @return 在之内返回ture，否则返回false
     */
    public static Boolean assignInDate(String from, String to) {
        Date fromTime = null;
        Date toTime = null;
        try {
            fromTime = SDF_DATETIME.get().parse(from);
            toTime = SDF_DATETIME.get().parse(to);
        } catch (ParseException e) {
            e.printStackTrace();
            return false;
        }
        return assignInDate(new Date(), fromTime, toTime);
    }

    /**
     * 判断当前时间是否在from，to之内
     *
     * @param from 开始日期
     * @param to   结束日期
     * @return 在之内返回ture，否则返回false
     */
    public static Boolean assignInDate(Date from, Date to) {
        return assignInDate(new Date(), from, to);
    }

    /**
     * 判断time是否在from，to之内
     *
     * @param time 指定日期
     * @param from 开始日期
     * @param to   结束日期
     * @return 在之内返回ture，否则返回false
     */
    public static Boolean assignInDate(Date time, Date from, Date to) {
        Calendar date = Calendar.getInstance();
        date.setTime(time);

        Calendar after = Calendar.getInstance();
        after.setTime(from);

        Calendar before = Calendar.getInstance();
        before.setTime(to);

        return date.after(after) && date.before(before);
    }

    /**
     * 设置时间格式00:00:00 or 23:59:59
     *
     * @param date
     * @return
     * @flag 0 返回yyyy-MM-dd 00:00:00日期<br>
     * 1 返回yyyy-MM-dd 23:59:59日期
     */
    public static Date weeHours(Date date, int flag) {
        if (date == null) {
            return null;
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int hour = cal.get(Calendar.HOUR_OF_DAY);
        int minute = cal.get(Calendar.MINUTE);
        int second = cal.get(Calendar.SECOND);
        // 时分秒（毫秒数）
        long millisecond = hour * 60 * 60 * 1000 + minute * 60 * 1000 + second * 1000;
        // 凌晨00:00:00
        cal.setTimeInMillis(cal.getTimeInMillis() - millisecond);

        if (flag == 0) {
            return cal.getTime();
        } else if (flag == 1) {
            // 凌晨23:59:59
            cal.setTimeInMillis(cal.getTimeInMillis() + 23 * 60 * 60 * 1000 + 59 * 60 * 1000 + 59 * 1000);
        }
        return cal.getTime();
    }


    /**
     * 获取上周五时间,如果是周六日则取本周周五时间
     */
    public static Date lastFriday() {
        Calendar calendar = Calendar.getInstance();
        if (calendar.get(Calendar.DAY_OF_WEEK) >= Calendar.MONDAY && calendar.get(Calendar.DAY_OF_WEEK) <= Calendar.FRIDAY) {
            calendar.add(Calendar.DAY_OF_WEEK, -7);
        }
        if (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
            calendar.add(Calendar.DAY_OF_WEEK, -1);
        }
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.FRIDAY);//指示一个星期中的某天
        calendar.set(Calendar.HOUR_OF_DAY, 23);//指示一天中的小时。HOUR_OF_DAY 用于 24 小时制时钟。例如，在 10:04:15.250 PM 这一时刻，HOUR_OF_DAY 为 22。
        calendar.set(Calendar.MINUTE, 59);//指示一小时中的分钟。例如，在 10:04:15.250 PM 这一时刻，MINUTE 为 4。
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    //上月第一天
    public static Date lastMonthStart() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        Date lastMonthStart = weeHours(calendar.getTime(), 0);
        return lastMonthStart;
    }

    //上月最后一天
    public static Date lastMonthEnd() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.DAY_OF_MONTH, 1);
        cal.add(Calendar.DATE, -1);
        Date lastMonthEnd = weeHours(cal.getTime(), 1);
        return lastMonthEnd;
    }

    //上月第一天
    public static Date last2MonthStart() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -2);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        Date lastMonthStart = weeHours(calendar.getTime(), 0);
        return lastMonthStart;
    }

    //上月最后一天
    public static Date last2MonthEnd() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.MONTH, -1);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        cal.add(Calendar.DATE, -1);
        Date lastMonthEnd = weeHours(cal.getTime(), 1);
        return lastMonthEnd;
    }

    /**
     * 获取两个时间相差多少秒
     *
     * @param sdate
     * @param edate
     * @return
     * @throws ParseException
     */
    public static Long getMin(String sdate, String edate) throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date start = simpleDateFormat.parse(sdate);
        Date end = simpleDateFormat.parse(edate);
        long min = (end.getTime() - start.getTime()) / 1000;
        return min;

    }

    public static String formatTimeNoPoint(Date date) {
        return SDF_TIME_NO_POINT.get().format(date);
    }

    // 获得某天最小时间 2020-02-17 00:00:00
    public static Date getStartOfDay(Date date) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(date.getTime()), ZoneId.systemDefault());
        LocalDateTime startOfDay = localDateTime.with(LocalTime.MIN);
        return Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date parseDate(String time){
        try {
            return SDF_DATETIME.get().parse(time);
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获取某天最大时间
     *
     * @param date
     * @return
     */
    public static Date getMaxDate(Date date) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(date.getTime()), ZoneId.systemDefault());
        LocalDateTime startOfDay = localDateTime.with(LocalTime.MAX);
        return Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }

    //获取次日最大时间
    public static Date getEndOfDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, +1);
        date = calendar.getTime();
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(date.getTime()), ZoneId.systemDefault());
        ;
        LocalDateTime endOfDay = localDateTime.with(LocalTime.MAX);
        return Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static String getMonthFirstDayFom() {
        try {
            Calendar cal = Calendar.getInstance();
            cal.setTime(new Date());
            cal.set(Calendar.DAY_OF_MONTH, 1);// 日期设置1
            cal.set(Calendar.HOUR_OF_DAY, 0);
            cal.set(Calendar.MINUTE, 0);
            cal.set(Calendar.SECOND, 0);
            cal.set(Calendar.MILLISECOND, 0);
            return SDF_DATETIME.get().format(cal.getTime());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取当天剩余秒数
     *
     * @return
     */
    public static long getTomorrowZeroSeconds() {
        long current = System.currentTimeMillis();// 当前时间毫秒数
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        long tomorrowzero = calendar.getTimeInMillis();
        long tomorrowzeroSeconds = (tomorrowzero - current) / 1000;
        return tomorrowzeroSeconds;
    }

    /**
     * 是否为每月1日
     * @param date 判断时间
     * @return true-是；false-否；
     */
    public static boolean isFirstDayOfMonth(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int day = cal.get(Calendar.DAY_OF_MONTH);
        return day == 1;
    }

    //获取某个时间当月第一天
    public static Date firstDayOfMonth(Date date, int month) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MONTH, month);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        Date firstDayOfMonth = weeHours(cal.getTime(), 0);
        return firstDayOfMonth;
    }

    //获取某个时间当月最后一天
    public static Date lastDayOfMonth(Date date, int month) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MONTH, month + 1);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        cal.add(Calendar.DATE, -1);
        Date lastDayOfMonth = weeHours(cal.getTime(), 1);
        return lastDayOfMonth;
    }

    /**
     * 判断时间是否在一个时间段内
     *
     * @param date 时间
     * @param from 开始时间
     * @param to   结束时间
     * @return Boolean
     */
    public static boolean assignInTime(Date date, String from, String to) throws ParseException {
        SimpleDateFormat sdfTime = SDF_TIME.get();
        Date targetTime = sdfTime.parse(sdfTime.format(date));
        Date startTime = sdfTime.parse(from);
        Date endTime = sdfTime.parse(to);
        return startTime.compareTo(targetTime) <= 0 && targetTime.compareTo(endTime) <= 0;
    }


    public static Date dateDayAgoStart(int month) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR, month);
        // 设置为当天的0点
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date time = calendar.getTime();
        return time;
    }

    public static Date dateDayAgoEnd( int month) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR, month);
        // 设置为当天的0点
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        Date time = calendar.getTime();
        return time;
    }

    public static void main(String[] args) throws Exception {
//        System.out.println(TimeUtil.today());
//        System.out.println(time2Int("21:46:00"));
//        System.out.println(time2Int("01:00:00"));
//        System.out.println(dateDiff(10));
//        System.out.println(Calendar.DAY_OF_YEAR);
//        System.out.println(dateDiff(-2));
//        System.out.println(getTimeStamp());
//        System.out.println(TimeUtil.formatDateTime(stampAddHour(new Date(), -24)));
//        System.out.println(TimeUtil.formatDateTime(TimeUtil.todayForHour(3)));
//        System.out.println(TimeUtil.formatDateTime(TimeUtil.stampAddHour(new Date(), -5)));
//        System.out.println(TimeUtil.formatDateTime(TimeUtil.todayForMinute(5 * 60)));
//        System.out.println(TimeUtil.formatDateTime(TimeUtil.stampAddDay(new Date(), -6)));
//        System.out.println(SDF_DATE.format(TimeUtil.addDay(-1)));
//        System.out.println(TimeUtil.formatDateTime(lastMonthStart()));
//        System.out.println(TimeUtil.formatDateTime(lastMonthEnd()));
////        System.out.println(formatDateForMin(stampAddMinute(new Date(),-6)));
//        System.out.println(getMin("2019-01-07 14:26:09","2019-01-07 14:26:29"));
//        System.out.println(yesterDayToString());
//        System.out.println(formatDateForYyyyMMddHHmmss(new Date()));
//        System.out.println(getMaxDate(new Date()));
//        System.out.println(getStartOfDay(new Date()));
//        System.out.println(getEndOfDay(new Date()));
//        System.out.println(stampAddHour(new Date(),-1));
//        Integer unsettledPublishtime = 20;
//        Long startTime = parseForDateTime("2022-10-24 09:03:00").getTime();
//        Long time = (new Date().getTime() - startTime) / 1000 / 60;
//        if ((new Date().getTime() - startTime / 1000 / 60) <= unsettledPublishtime) {
//            System.out.println();
//        }
//        System.out.println(TimeUtil.addDay(today(), -1));
//        System.out.println(TimeUtil.getStartOfDay(yesterDay()));
//        System.out.println(TimeUtil.getMaxDate(yesterDay()));

//        System.out.println(firstDayOfMonth(new Date(),12));
//        System.out.println(lastDayOfMonth(new Date(),12));

        System.out.println(assignInTime(new Date(), "10:00:00", "15:00:00"));
        ;
    }
}
