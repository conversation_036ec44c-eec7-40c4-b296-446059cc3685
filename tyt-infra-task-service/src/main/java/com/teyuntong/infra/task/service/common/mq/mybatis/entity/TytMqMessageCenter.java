package com.teyuntong.infra.task.service.common.mq.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;

/**
 * 表名：tyt_mq_message_center
*/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("tyt_mq_message_center")
public class TytMqMessageCenter {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 消息序列号，每个消息有一个唯一的序列号，用于唯一标示一条消息
     */
    private String messageSerialNum;

    /**
     * 消息的处理状态(1：未处理 2：已处理 3: 处理失败;4多次失败死信)
     */
    private Byte dealStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 发送类型（短信，消息，通知，按顺序位数是否是1确定是否发送，该字段只用来显示）
     */
    private String messageType;

    /**
     * 失败次数
     */
    private Integer failCount;

    /**
     * 消息的原始完整内容
     */
    private String messageContent;
}