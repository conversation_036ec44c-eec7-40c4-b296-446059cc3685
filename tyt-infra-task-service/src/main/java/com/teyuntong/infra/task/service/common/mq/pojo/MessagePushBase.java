package com.teyuntong.infra.task.service.common.mq.pojo;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

@Data
public class MessagePushBase {

    /** 需要替换内容的标签 **/
    public final static String message_replace_lable = "${messageId}";

    /** 标题 **/
    protected Long pushMsgId;

    /** 标题 **/
    protected String title;
    /** 详情内容(detailsContent) **/
    protected String content;
    /** 详情链接-details_url **/
    protected String linkUrl;

    /** 需要推送的用户id **/
    protected List<Long> userIdList;

    /** 说明，建议赋值 **/
    protected String remarks;

    /** ========== 以下字段有默认值，可不设置 ========== **/

    /** 消息类别 **/
    protected Integer msgType = PushMsgTypeEnum.sys.getCode();
    /** 发送方式0是手动1是定时自动发送 **/
    protected Integer sendMode = SendModeEnum.manual.getCode();

    /** 原特运通是否推送 0不推  1.推， 该字段已弃用 **/
    @Deprecated
    protected Short originPush = 0;
    /** 车主端是否推送 0不推 1推 **/
    protected Short carPush = 0;
    /** 货主端是否推送 0不推 1推 **/
    protected Short goodsPush = 0;

    /** ========== 以下字段可为空 ========== **/

    /** 模版ID **/
    protected Long msgTmplId;

    /**
     * 添加推送用户id
     * @param userId
     */
    public void addUserId(Long userId){
        if(userId == null){
            return;
        }
        if(userIdList == null){
            userIdList = new ArrayList<>();
        }
        userIdList.add(userId);
    }

    /**
     * 判断对象是否为空
     * @param messageBean
     * @return
     */
    public static boolean isNotEmpty(MessagePushBase messageBean){
        return (messageBean != null && StringUtils.isNotBlank(messageBean.getContent()));
    }

    /**
     * 推送终端，原版终端不再推送
     * @param carPush
     * @param goodsPush
     */
    public void setPushType(int carPush, int goodsPush){
        this.carPush = (short) carPush;
        this.goodsPush = (short) goodsPush;
    }

}
