package com.teyuntong.infra.task.service.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.util.TypeUtils;
import com.teyuntong.infra.task.service.common.exception.TytException;
import com.teyuntong.infra.task.service.common.mq.pojo.ResponseEnum;
import com.teyuntong.user.service.client.constant.RegexpConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.io.*;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 公共工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class CommonUtil {

    static volatile String timeStr;
    static AtomicInteger tsn = new AtomicInteger(0);

    /** 项目根目录 **/
    private static String project_root_path;

    /**
     * 生成随机数，包含起始
     *
     * @param min
     * @param max
     * @return
     */
    public static int getRundomNumber(int min, int max) {
        if(max < min){
            throw TytException.createException(ResponseEnum.request_error.info());
        }

        Random random = new Random();

        int result = random.nextInt(max - min + 1) + min;

        return result;
    }

    /**
     * 生成唯一 id，该方法不支持分布式
     * 支持毫秒千以内
     *
     * @return
     */
    @Deprecated
    public static String getUniqueKey() {

        String dayType = "yyyyMMddHHmmssSSSS";
        String timeStamp = DateUtil.getNowTime(dayType);

        int tsnTmp = 0;
        if (!timeStamp.equals(timeStr)) {
            timeStr = timeStamp;
            tsn.set(0);
        } else {
            tsnTmp = tsn.incrementAndGet();
        }

        String tsnNumber = String.format("%03d", tsnTmp);

        String reuslt = timeStamp + tsnNumber;

        return reuslt;
    }

    /**
     * 获得 文本文件内容
     *
     * @param path
     * @return
     */
    public static String getFileContent(String path) {
        StringBuilder builder = new StringBuilder();

        FileReader fr = null;
        BufferedReader reader = null;
        try {
            fr = new FileReader(path);
            reader = new BufferedReader(fr);
            String line = null;

            int lineCount = 0;

            while ((line = reader.readLine()) != null) {
                if(lineCount > 0){
                    builder.append("\r\n");
                }
                builder.append(line);

                lineCount++;
            }

            reader.close();
        } catch (Exception e) {
            // TODO Auto-generated catch block
            log.error("", e);
        }finally {
            if(reader != null){
                try {
                    reader.close();
                } catch (IOException e) {
                    log.error("", e);
                }
            }
            if(fr != null){
                try {
                    fr.close();
                } catch (IOException e) {
                    log.error("", e);
                }
            }
        }
        return builder.toString();
    }

    /**
     * 将文本写入文件
     *
     * @param path
     * @param content
     * @param append
     */
    public static void writeFileContent(String path, String content, boolean append) {

        if(content == null){
            return;
        }

        createFileFolder(path);

        FileWriter fw = null;
        BufferedWriter writer = null;
        try {
            fw = new FileWriter(path, append);
            writer = new BufferedWriter(fw);

            writer.write(content);
        } catch (IOException e) {
            // TODO Auto-generated catch block
            log.error("", e);
        } finally {
            try {
                writer.close();
                fw.close();
            } catch (IOException e) {
                // TODO Auto-generated catch block
                log.error("", e);
            }
        }
    }

    /**
     * 将二进制转换成base64字符串格式
     *
     * @param byteData
     * @return
     */
    public static String encodeBase64(byte[] byteData) {
        String result = Base64.encodeBase64String(byteData);
        return result;
    }

    /**
     * base64 格式
     *
     * @param baseText
     * @return
     */
    public static byte[] decodeBase64(String baseText) {
        byte[] data = Base64.decodeBase64(baseText);
        return data;
    }

    /**
     * 将数据写入文件
     *
     * @param path
     * @param dataByte
     * @param append
     */
    public static void writeFile(String path, byte[] dataByte, boolean append) {

        createFileFolder(path);

        OutputStream output = null;
        try {
            output = new FileOutputStream(path, append);

            output.write(dataByte);
        } catch (Exception e) {
            throw TytException.createException(e);
        } finally {
            if (output != null) {
                try {
                    output.close();
                } catch (IOException e) {
                    throw TytException.createException(e);
                }
            }
        }
    }

    /**
     * byte 数组拼接追加
     *
     * @param byteArrayList
     * @return
     */
    public static byte[] joinAllBytes(byte[]... byteArrayList) throws Exception {
        byte[] resultBytes = null;

        ByteArrayOutputStream byteOut = null;

        byteOut = new ByteArrayOutputStream();
        try {
            for (byte[] oneArray : byteArrayList) {
                byteOut.write(oneArray);
            }

            resultBytes = byteOut.toByteArray();

            byteOut.reset();

        } finally {
            if (byteOut != null) {
                try {
                    byteOut.close();
                } catch (IOException e) {
                    log.error("", e);
                }
            }
        }

        return resultBytes;
    }

    /**
     * byte 数组拼接追加
     *
     * @param byteArrayList
     * @return
     */
    public static byte[] joinAllBytes(List<byte[]> byteArrayList) {

        ByteArrayOutputStream byteOut = new ByteArrayOutputStream();

        for (byte[] oneArray : byteArrayList) {
            try {
                byteOut.write(oneArray);
            } catch (IOException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }

        byte[] resultBytes = byteOut.toByteArray();

        byteOut.reset();
        try {
            byteOut.close();
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }

        return resultBytes;
    }

    /**
     * 读取文件
     *
     * @param path
     * @return
     */
    public static byte[] readFileData(String path) {
        byte[] resultByte = null;

        ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
        InputStream input = null;
        try {
            input = new FileInputStream(path);

            int readLength = 0;

            byte[] tmpByte = new byte[4 * 1024];

            while ((readLength = input.read(tmpByte)) > 0) {

                byteOut.write(tmpByte, 0, readLength);

            }

            resultByte = byteOut.toByteArray();

        } catch (Exception e) {
            throw TytException.createException(e);
        } finally {
            try {
                input.close();

                byteOut.close();
            } catch (IOException e) {
                throw TytException.createException(e);
            }
        }

        return resultByte;
    }

    /**
     * 创建文件包括目录
     *
     * @param pathFile
     * @return
     * @throws IOException
     */
    public static void createFile(File pathFile) throws IOException {
        File parentFile = pathFile.getParentFile();

        if (!parentFile.exists()) {
            parentFile.mkdirs();
        }
        if (!pathFile.exists()) {
            pathFile.createNewFile();
        }
    }

    /**
     * 创建文件包括目录
     *
     * @param filePath
     * @return
     * @throws IOException
     */
    public static void createFileFolder(String filePath) {

        File parentFile = new File(filePath).getParentFile();

        if (!parentFile.exists()) {
            parentFile.mkdirs();
        }
    }

    /**
     * 创建文件包括目录
     *
     * @param path
     * @return
     * @throws IOException
     */
    public static File createFile(String path) throws IOException {
        File resultFile = new File(path);

        createFile(resultFile);

        return resultFile;
    }

    /**
     * 是否都为空
     *
     * @param objArray
     * @return
     */
    public static boolean isAllNull(Object... objArray) {
        boolean result = true;

        for (Object obj : objArray) {

            if (obj != null) {
                result = false;

                break;
            }

        }
        return result;
    }

    /**
     * 是否包含空
     *
     * @param objArray
     * @return
     */
    public static boolean hasNull(Object... objArray) {
        boolean result = false;

        for (Object obj : objArray) {

            if (obj == null) {
                result = true;

                break;
            }

        }
        return result;
    }

    /**
     * 将 long set 转成字符串
     *
     * @param longSet
     * @return
     */
    public static String joinLongSet(Set<Long> longSet) {
        StringBuilder builder = new StringBuilder();

        int i = 0;
        for (Long oneLong : longSet) {
            if (i > 0) {
                builder.append(",");
            }
            builder.append(oneLong);
            i++;
        }
        return builder.toString();
    }

    /**
     * 获取不带后缀的文件名
     *
     * @param fileName
     * @return
     */
    public static String getFileNameWithoutSuffix(String fileName) {
        String result = fileName;

        int index = fileName.lastIndexOf(".");

        if (index >= 0) {
            result = fileName.substring(0, index);
        }

        return result;
    }

    /**
     * 类字段赋值验证
     *
     * @param clazz
     * @param fieldName
     * @param fieldValue
     * @return
     */
    public static boolean fastJsonValidate(Class clazz, String fieldName, Object fieldValue) {

        boolean flag = true;

        if (fieldValue != null) {

            try {
                Field field = clazz.getDeclaredField(fieldName);

                if (field != null) {

                    Class typeClazz = field.getType();

                    if (typeClazz != null) {
                        if (typeClazz.isAssignableFrom(Date.class)) {
                            //nothing to do

                        } else if (typeClazz.isAssignableFrom(Boolean.class)) {
                            TypeUtils.castToBoolean(fieldValue);

                        } else if (typeClazz.isAssignableFrom(String.class)) {
                            TypeUtils.castToString(fieldValue);

                        } else if (typeClazz.isAssignableFrom(Double.class)) {
                            TypeUtils.castToDouble(fieldValue);

                        } else if (typeClazz.isAssignableFrom(Float.class)) {
                            TypeUtils.castToFloat(fieldValue);

                        } else if (typeClazz.isAssignableFrom(Byte.class)) {
                            TypeUtils.castToByte(fieldValue);

                        } else if (typeClazz.isAssignableFrom(Short.class)) {
                            TypeUtils.castToShort(fieldValue);

                        } else if (typeClazz.isAssignableFrom(Integer.class)) {
                            TypeUtils.castToInt(fieldValue);

                        } else if (typeClazz.isAssignableFrom(Long.class)) {
                            TypeUtils.castToLong(fieldValue);

                        } else {
                            throw TytException.createException(ResponseEnum.sys_error.info("字段类型错误"));
                        }
                    }

                }
            } catch (Exception e) {
                log.info("Validate Error : ", e);
                flag = false;
            }

        }
        return flag;
    }

    /**
     * 转换
     *
     * @param content
     * @param objClass
     * @return
     */
    public static Object parseContentToObj(Object content, Class objClass) {

        String valueJson = JSON.toJSONString(content);
        Object parseObj = JSON.parseObject(valueJson, objClass);

        return parseObj;
    }

    /**
     * 校验正则表达式(全校验)
     * @param regex
     * @param content
     * @return
     */
    public static boolean regMatch(String regex, String content){
        boolean isMatch = Pattern.matches(regex, content);
        return isMatch;
    }

    /**
     * 正则校验非全匹配
     *
     * @param reg
     * @param text
     * @return
     */
    public static boolean regFind(String reg, String text) {
        Pattern pattern = Pattern.compile(reg);
        Matcher matcher = pattern.matcher(text);

        boolean result = matcher.find();
        return result;
    }

    /**
     * 用冒号(:)拼接 redis key
     *
     * @param strArray
     * @return
     */
    public static String joinRedisKey(String... strArray) {
        StringBuilder builder = new StringBuilder();

        int i = 0;
        boolean endwithSplit = false;
        for (String onePart : strArray) {

            if(StringUtils.isBlank(onePart)){
                continue;
            }

            if (i > 0 && !endwithSplit) {
                builder.append(":");
            }

            builder.append(onePart);

            if (onePart.endsWith(":")) {
                endwithSplit = true;
            } else {
                endwithSplit = false;
            }

            i++;
        }

        return builder.toString();
    }

    /**
     * 生成uuid
     *
     * @param nonSplit 是否过滤分隔符
     * @return
     */
    public static String getUUID(boolean nonSplit) {
        String uuid = UUID.randomUUID().toString();

        if (nonSplit) {
            uuid = uuid.replaceAll("-", "");
        }

        return uuid;
    }

    /**
     * 获得文件后缀
     *
     * @param fileName
     * @return
     */
    public static String getFileSuffix(String fileName) {
        String suffix = "";

        if (StringUtils.isBlank(fileName)) {
            return suffix;
        }

        File tmpFile = new File(fileName);

        String nameOnly = tmpFile.getName();

        int index = nameOnly.lastIndexOf(".");

        if (index >= 0) {
            suffix = nameOnly.substring(index).toLowerCase();
        }
        return suffix;
    }

    /**
     * 拼接文件路径
     * 统一用 Linx 路径分隔符
     *
     * @param partArray
     * @return
     */
    public static String joinPath(String... partArray) {
        String fullPath = null;

        if (partArray != null) {
            fullPath = "";

            for (String onePart : partArray) {
                if (StringUtils.isNotBlank(onePart)) {

                    onePart = onePart.replace('\\', '/');

                    if (fullPath.length() > 0) {

                        if(onePart.startsWith("/")){
                            onePart = onePart.replaceAll("^/+", "");
                        }
                    }
                    if(StringUtils.isNotBlank(onePart)){
                        if (fullPath.length() > 0) {
                            if (!fullPath.endsWith("/")) {
                                fullPath = fullPath + "/";
                            }
                        }
                        fullPath = fullPath + onePart;
                    }
                }
            }
        }

        return fullPath;
    }

    /**
     * 将对象转成map
     *
     * @param obj
     * @return
     */
    public static Map<String, Object> objectToMap(Object obj) {
        Map<String, Object> result = null;

        if (obj != null) {
            String json = JSON.toJSONString(obj);

            result = JSON.parseObject(json, new TypeReference<Map<String, Object>>() {
            });
        }

        return result;
    }

    public static String objToString(Object obj, String defaultValue){

        String result = null;
        if(obj == null){
            return defaultValue;
        }else if(obj instanceof String){
            result = (String) obj;
        }else {
            result = obj.toString();
        }
        return result;
    }

    /**
     * 将对象转为restTemplete 对象
     * @param obj
     * @return
     */
    public static MultiValueMap<String, String> objectToMultiMap(Object obj){
        Map<String, String> result = null;

        MultiValueMap<String, String> multiMap = new LinkedMultiValueMap<>();
        if (obj != null) {
            String json = JSON.toJSONString(obj);

            result = JSON.parseObject(json, new TypeReference<Map<String, String>>() {
            });

            for(Map.Entry<String, String> entry: result.entrySet()) {
                multiMap.add(entry.getKey(), entry.getValue());
            }
        }
        return multiMap;
    }

    /**
     * 删除文件及下级所有文件
     *
     * @param path
     * @return
     */
    public static boolean deleteFile(String path) {
        File file = new File(path);
        //判断是否待删除目录是否存在
        if (!file.exists()) {
            return false;
        }

        if (file.isDirectory()) {
            //取得当前目录下所有文件和文件夹
            File[] content = file.listFiles();

            for (File temp : content) {
                //递归调用，删除目录里的内容
                deleteFile(temp.getAbsolutePath());
            }
        }

        file.delete();

        return true;
    }

    /**
     * 拆分逗号分隔的id
     *
     * @param idArrayStr
     */
    public static List<Long> idArraySplit(String idArrayStr) {
        List<Long> idList = null;
        if (StringUtils.isNotBlank(idArrayStr)) {

            String[] idArray = idArrayStr.split(",");

            if (ArrayUtils.isNotEmpty(idArray)) {
                idList = new ArrayList<>();

                for (String oneId : idArray) {

                    Long idLong = Long.parseLong(oneId.trim());

                    idList.add(idLong);
                }
            }
        }

        return idList;
    }

    /**
     * 拆分 id 字符串
     * @param idArrayStr
     * @return
     */
    public static Set<Long> splitIdArraySet(String idArrayStr) {
        Set<Long> idSet = null;
        if (StringUtils.isNotBlank(idArrayStr)) {

            String[] idArray = idArrayStr.split(",");

            if (ArrayUtils.isNotEmpty(idArray)) {
                idSet = new HashSet<>();

                for (String oneId : idArray) {

                    Long idLong = Long.parseLong(oneId.trim());

                    idSet.add(idLong);
                }
            }
        }

        return idSet;
    }

    /**
     * 判断是否相同
     *
     * @param o1
     * @param o2
     * @param nullDefault 都为空时，结果
     * @return
     */
    public static boolean objEquels(Object o1, Object o2, boolean nullDefault) {
        boolean result = false;
        if (nullDefault) {
            if (o1 == null && o2 == null) {
                return true;
            }
        }

        if (o1 != null && o2 != null) {
            result = o1.equals(o2);
        }

        return result;
    }

    /**
     * 计算总数
     * @param numberArray
     * @return
     */
    public static int sumInteger(Integer ... numberArray){

        int sumResult = 0;

        if(numberArray != null) {
            for (Integer oneInt : numberArray) {
                if(oneInt != null){
                    sumResult = sumResult + oneInt;
                }
            }
        }
        return sumResult;
    }

    /**
     * 解析url参数
     * @param url
     * @return
     */
    public static Map<String, String> getUrlParameterMap(String url) {

        String params = url.substring(url.indexOf("?") + 1);

        String[] keyValueArray = params.split("&");

        Map<String, String> paramMap = new HashMap<>();

        for(String oneKeyValue : keyValueArray){
            if(StringUtils.isNotBlank(oneKeyValue)) {

                int eqIndex = oneKeyValue.indexOf("=");

                if(eqIndex > -1) {
                    String oneKey = oneKeyValue.substring(0, eqIndex);
                    String oneValue = oneKeyValue.substring(eqIndex + 1);

                    try {
                        oneKey = URLDecoder.decode(oneKey, "UTF-8");
                        oneValue = URLDecoder.decode(oneValue, "UTF-8");
                    } catch (UnsupportedEncodingException e) {
                        throw TytException.createException(e);
                    }

                    paramMap.put(oneKey, oneValue);
                }
            }
        }

        return paramMap;
    }

    /**
     * 替换url 参数
     * @param url
     * @param key
     * @param value
     * @return
     */
    public static String replaceUrlParameter(String url, String key, String value){

        int indexStart = url.indexOf(key + "=");

        if(indexStart <= -1){
            return url;
        }

        int indexEnd = url.indexOf("&", indexStart);

        if(indexEnd <= -1){
            indexEnd = url.length();
        }

        try {
            value = URLEncoder.encode(value, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw TytException.createException(e);
        }

        String urlBefore = url.substring(0, indexStart);

        String urlMiddle = key + "=" + value;

        String urlAfter = url.substring(indexEnd);

        String urlResult = urlBefore + urlMiddle + urlAfter;

        return urlResult;
    }

    /**
     * 是否全部为真
     * @param flags
     * @return
     */
    public static boolean allTrue(Boolean ... flags){

        boolean booleanResult = true;

        if(flags != null) {
            for (Boolean oneBolean : flags) {
                if(oneBolean == null || !oneBolean){
                    booleanResult = false;
                    break;
                }
            }
        }

        return booleanResult;
    }

    public static String getStringValue(String value, String defaultValue){
        if(value != null){
            return value;
        }else{
            return defaultValue;
        }
    }

    /**
     * 计算页数
     * @param totalSize
     * @param pageSize
     * @return
     */
    public static int getPageCount(int totalSize, int pageSize){

        if(pageSize == 0){
            throw TytException.createException(ResponseEnum.request_error.info());
        }

        int pageCount = new BigDecimal(totalSize).divide(new BigDecimal(pageSize), 0, RoundingMode.UP).intValue();

        return pageCount;
    }

    /**
     * list 分页
     * @param dataList
     * @param pageNumber 1开始
     * @param pageSize
     * @param <T>
     * @return
     */
    public static <T> List<T> paggerList(List<T> dataList, int pageNumber, int pageSize){
        int size = dataList.size();

        int start = (pageNumber - 1) * pageSize;

        if(start >= size){
            return null;
        }

        int end = start + pageSize;

        if(end >= dataList.size()){
            end = dataList.size();
        }

        List<T> onePageList = new ArrayList<>();

        for(int i=start; i<end; i++){
            T oneObj = dataList.get(i);

            onePageList.add(oneObj);
        }
        return onePageList;
    }

    /**
     * 循环休眠，测试用，正式不建议使用
     * @param millis
     * @param loopCount
     */
    public static void loopSleep(long millis, int loopCount){

        for(int i= loopCount; i > 0 ; i--){
            log.info(" ==== 休眠 " + millis + " 毫秒，剩余 " + i + " 次!");

            try {
                Thread.sleep(millis);
            } catch (InterruptedException e) {
                log.error("", e);
            }

        }

    }

    public static void printSpendTime(long startTime){

        long endTime = System.currentTimeMillis();

        long spend = (endTime - startTime);

        log.info("#### spend_time : " + spend + " Millis!");

    }

    public static boolean isValidIp(String ip) {
        String partReg = "((25[0-5])|(2[0-4]\\d)|(1\\d{2})|([1-9]\\d)|([0-9]))";

        String ipReg = "^" + partReg + "\\." + partReg + "\\." + partReg + "\\." + partReg + "$";

        boolean match = regMatch(ipReg, ip);

        return match;
    }

    /**
     * 循环休眠，测试用，正式不建议使用
     * @param millis
     * @param loopCount
     */
    public static void loopSleep(long millis, int loopCount, String msg){

        for(int i= loopCount; i > 0 ; i--){

            log.info(msg + " ==== 休眠 {} 毫秒，剩余 {} 次!", millis, i);

            try {
                Thread.sleep(millis);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 是否是手机号
     * @param phoneNumber
     * @return
     */
    public static boolean isPhoneNumber(String phoneNumber){
        boolean result = false;

        if(StringUtils.isNotBlank(phoneNumber)){

            String reg = RegexpConstant.REGEXP_MOBILE;

            result = regMatch(reg, phoneNumber);

        }

        return result;
    }

    /**
     * 获取项目根目录.
     * @return
     */
    public static String getProjectRoot(){

        if(StringUtils.isNotBlank(project_root_path)) {
            return project_root_path;
        }

        URL resourceUrl = CommonUtil.class.getResource("/");
        String resourcePath = resourceUrl.getPath();

        if(StringUtils.isBlank(resourcePath)){
            throw TytException.createException(ResponseEnum.sys_error.info("Init_Project_Root_Error!"));
        }

        String jarFlag = "file:";
        boolean isJar = false;
        if(resourcePath.startsWith(jarFlag)){
            isJar = true;
            resourcePath = resourcePath.substring(jarFlag.length());
        }

        File dirFileTmp = new File(resourcePath);

        int i = 0;
        while (true){
            if(dirFileTmp.isDirectory()){
                break;
            }
            dirFileTmp = dirFileTmp.getParentFile();
            i++;

            if(i > 10){
                //查找项目目录过深，正常不会超过
                throw TytException.createException(ResponseEnum.sys_error.info("Project_Root_Dir_init_Error!"));
            }
        }

        project_root_path = dirFileTmp.getAbsolutePath();

        if(StringUtils.isBlank(project_root_path)){
            throw TytException.createException(ResponseEnum.sys_error.info("Project_Root_Dir_Blank_Error!"));
        }

        log.info("project_root_path_init : " + project_root_path);

        return project_root_path;
    }

    /**
     * 获取项目临时目录
     * @return
     */
    public static String getProjectTmpPath(){
        String tmpFolder = CommonUtil.joinPath(CommonUtil.getProjectRoot(), "cargo_tmp");
        return tmpFolder;
    }

    public static void main(String[] args) throws Exception{

        String phoneNumber = "13501075392";

        String p1 = "113501075392";

        String reg = RegexpConstant.REGEXP_MOBILE;

        boolean b = regFind(reg, phoneNumber);
        boolean a = regMatch(reg, phoneNumber);


        boolean c = regFind(reg, p1);
        boolean d = regMatch(reg, p1);

        System.out.println("finished ... ");

    }

}
