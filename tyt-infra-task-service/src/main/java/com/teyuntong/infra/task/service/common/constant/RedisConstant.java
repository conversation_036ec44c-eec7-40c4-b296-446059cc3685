package com.teyuntong.infra.task.service.common.constant;

public class RedisConstant {
    /**
     * manage系统验证登录信息的缓存KEY
     */
    public static final String BS_LOGIN_VERIFY_REDIS_KEY = "bs:login:verify:redis:key:";


    /** 全局 redis key 前缀 **/
    public final static String prefix = "tyt:dispatch:";


    public static class Config {

        /**
         * 配置缓存更新标识
         */
        public static final String cache_change_key = "TYT_CONFIG_MAP_FLAG";

        /**
         * 配置map key
         */
        public static final String chche_map_key = "mem_TYT_CONFIG_MAP_";

        /**
         * 货源默认重发次数
         */
        public static final String resend_time_key = "defaultResendTime";

        /**
         * plat 调用地址
         */
        public static final String plat_service_key = "tyt_company_plat_service_url";

    }

    public static class Transport {

        public static final String hash_code_key = "mem_HASHCODE_";

        public static final String refresh_interval_key = prefix + "transport_refresh";

        //货源锁
        public static final String refresh_lock_key = prefix + "transport_lock";

        /**
         * 货源操作锁
         */
        public static final String transport_opt_lock = "tyt:plat:lock:transport_opt";

        /**
         * 后台货源锁定
         */
        public static final String backend_transport_lock = "tyt:plat:lock:backend_transport_opt";

        /**
         * 优车货源下发布时间
         */
        public static final String EXCELLENT_GOODS_PUBLISH_TIME = "mem_tyt:transport:excellent_goods_publish_time:";

    }

}
