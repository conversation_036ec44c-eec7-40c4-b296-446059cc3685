package com.teyuntong.infra.task.service.common.aop;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletRequest;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/12/17 13:17
 */
@Slf4j
//@Aspect
//@Component
public class LogAspect {

    private static final String POINT_CUT = "execution( * com.teyuntong.infra.task.schedule..*.*(..))";

    @Pointcut(POINT_CUT)
    public void requestPoint() {
    }


    @Around("requestPoint()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {

        String requestURI = Strings.EMPTY;
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (requestAttributes != null) {
            HttpServletRequest request = requestAttributes.getRequest();
            requestURI = request.getRequestURI();
        }

        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Object target = joinPoint.getTarget();
        String methodName = target.getClass().getSimpleName() + "." + signature.getName();

        String args = dealArgs(joinPoint.getArgs(), signature);

        log.info("【request】[uri]->:{},[methodName]->:{},[args]->:{}", requestURI, methodName, args);

        Object result = joinPoint.proceed();

        String resultJson = Strings.EMPTY;
        if (result != null) {
            resultJson = JSONUtil.toJsonStr(result);
            resultJson = resultJson.length() > 2000 ? resultJson.substring(0, 2000) : resultJson;
        }
        log.info("【response】[uri]->:{},[methodName]->:{},[result]->:{}", requestURI, methodName, resultJson);

        return result;
    }

    private String dealArgs(Object[] args, MethodSignature signature) {
        if (ArrayUtil.isNotEmpty(args)) {
            String[] parameterNames = signature.getParameterNames();
            Map<String, Object> paramMap = new HashMap<>();
            for (int i = 0; i < args.length; i++) {
                Object arg = args[i];
                if (arg == null || arg instanceof ServletRequest || arg instanceof MultipartFile) {
                    continue;
                }
                paramMap.put(parameterNames[i], arg);

            }
            String params = JSONUtil.toJsonStr(paramMap);
            return params.length() > 2000 ? params.substring(0, 2000) : params;
        }
        return Strings.EMPTY;
    }


}
