package com.teyuntong.infra.task.service.common.mq;

import com.teyuntong.infra.common.rocketmq.message.MessageTypeBase;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/11/04 10:31
 */
@Getter
@RequiredArgsConstructor
public enum ScaffoldMessageType implements MessageTypeBase {

    //--------------------------dev-----------------------------//
//    MESSAGE_CENTER_TOPIC_DEV("MESSAGE_CENTER_TOPIC_DEV", "tag"),

    SCAFFOLD_NORMAL_TAG_A("scaffold_normal_dev", "a"),
    SCAFFOLD_NORMAL_TAG_B("scaffold_normal_dev", "b"),
    SCAFFOLD_NORMAL_TAG_C("scaffold_normal_dev", "c"),

    SCAFFOLD_TRANSACTION_TAG_A("scaffold_transaction_dev", "a"),
    SCAFFOLD_TRANSACTION_TAG_B("scaffold_transaction_dev", "b"),

    SCAFFOLD_DELAY_TAG_A("scaffold_delay_dev", "a"),
    SCAFFOLD_DELAY_TAG_B("scaffold_delay_dev", "b"),

    SCAFFOLD_ORDER_TAG_A("scaffold_order_dev", "a"),
    SCAFFOLD_ORDER_TAG_B("scaffold_order_dev", "b"),







    //--------------------------test-----------------------------//
//    MESSAGE_CENTER_TOPIC_TEST("MESSAGE_CENTER_TOPIC_TEST", "tag"),






    //--------------------------release-----------------------------//
//    MESSAGE_CENTER_TOPIC_RELEASE("MESSAGE_CENTER_TOPIC_RELEASE", "tag"),







    //--------------------------生产-----------------------------//
//    MESSAGE_CENTER_TOPIC("TOPIC_TYT_MESSAGE_CENTER", "tag"),





    ;
    /**
     * 消息主题, 最长不超过255个字符; 由a-z, A-Z, 0-9, 以及中划线"-"和下划线"_"构成.
     */
    private final String topic;
    /**
     * 消息标签, 合法标识符, 尽量简短且见名知意
     */
    private final String tag;
}
