package com.teyuntong.infra.task.service.biz.customerservice.service.impl;

import com.teyuntong.infra.common.redis.utils.RedisUtil;
import com.teyuntong.infra.task.service.biz.customerservice.bean.CsOpUserInfo;
import com.teyuntong.infra.task.service.biz.customerservice.entity.CsNoticePopupTmpl;
import com.teyuntong.infra.task.service.biz.customerservice.entity.CsPollOrderConfig;
import com.teyuntong.infra.task.service.biz.customerservice.enums.CsNoticeTmplCodeEnum;
import com.teyuntong.infra.task.service.biz.customerservice.mapper.CsComplaintRecordMapper;
import com.teyuntong.infra.task.service.biz.customerservice.mapper.CsPollOrderConfigMapper;
import com.teyuntong.infra.task.service.biz.customerservice.service.CsComplaintRecordService;
import com.teyuntong.infra.task.service.biz.customerservice.service.CsNoticePopupService;
import com.teyuntong.infra.task.service.utils.TimeUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import lombok.RequiredArgsConstructor;

import javax.annotation.Resource;
import java.text.ParseException;
import java.time.Duration;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 客服投诉记录表 服务实现类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-09-29
 */

@Service
@Slf4j
@RequiredArgsConstructor
public class CsComplaintRecordServiceImpl implements CsComplaintRecordService {
    @Resource
    private CsComplaintRecordMapper csComplaintRecordMapper;
    @Resource
    private CsPollOrderConfigMapper csPollOrderConfigMapper;
    @Resource
    private CsNoticePopupService csNoticePopupService;
    @Resource
    private RedisUtil redisUtil;

    private static final int POLL_ORDER_SWITCH_ON = 1;
    private static final String POLL_ORDER_USER_INDEX_KEY = "poll_order_user_index";
    public static final int REDIS_EXPIRE_TIME_24H = 86400;// 24h

    @Override
    public void updateOpUser() throws ParseException {
        CsPollOrderConfig config = csPollOrderConfigMapper.getConfig();
        if (config == null || config.getPollOrderSwitch() != POLL_ORDER_SWITCH_ON || StringUtils.isBlank(config.getPollOrderUserList())) {
            log.info("投诉工单轮单已关闭");
            return;
        }
        Date now = new Date();
        if (!TimeUtil.assignInTime(now, config.getLimitStartTime(), config.getLimitEndTime())) {
            log.info("投诉工单轮单当前时间不在时间段内");
            return;
        }
        //获取需要分配的工单
        List<Long> workOrderIds = csComplaintRecordMapper.getNewWorkOrderIds();
        if (CollectionUtils.isEmpty(workOrderIds)) {
            log.info("投诉工单轮单，当前无需要轮单的投诉工单");
            return;
        }
        int limit = config.getPeopleUpperLimit();
        String[] userList = config.getPollOrderUserList().split(";");
        Map<Long, Integer> userCount = new HashMap<>();
        for (String user : userList) {
            Long userId = Long.parseLong(user.split(":")[0]);
            Integer count = csComplaintRecordMapper.getCountByOpUser(userId);
            userCount.put(userId, count);
        }
        String index = redisUtil.getString(POLL_ORDER_USER_INDEX_KEY);
        int userIndex = StringUtils.isBlank(index) ? 0 : Integer.parseInt(index);
        //获取弹窗模板
        CsNoticePopupTmpl noticeTmpl = csNoticePopupService.getNoticeTmpl(CsNoticeTmplCodeEnum.NEW_WORKORDER_NPTICE.getCode());
        //分配处理人
        for (Long workOrderId : workOrderIds) {
            CsOpUserInfo opUserInfo = getOpUser(userList, userCount, userIndex, limit);
            userIndex = opUserInfo.getIndex();
            if (opUserInfo.getOpUserId() == null) {
                log.info("投诉工单处理人单量均已达到上限");
                break;
            }
            csComplaintRecordMapper.updateOpUser(workOrderId, opUserInfo.getOpUserId(), opUserInfo.getOpUser());
            // 发送通知
            csNoticePopupService.saveCsNotice(opUserInfo.getOpUserId(), noticeTmpl);
        }
        redisUtil.set(POLL_ORDER_USER_INDEX_KEY, userIndex + "", Duration.ofSeconds(REDIS_EXPIRE_TIME_24H));

    }

    /**
     * 获取处理人信息
     *
     * @param userList  处理人列表
     * @param userCount 可分配人员列表
     * @param userIndex 循环位置
     * @param limit     处理人单量上限
     * @return String
     */
    private CsOpUserInfo getOpUser(String[] userList, Map<Long, Integer> userCount, int userIndex, int limit) {
        int length = userList.length;
        for (int i = 0; i < length; i++) {
            if (userIndex >= userList.length) {
                userIndex = 0;
            }
            String userInfo = userList[userIndex];
            Long opUserId = Long.parseLong(userInfo.split(":")[0]);
            userIndex++;
            int count = userCount.get(opUserId) + 1;
            if (count > limit) {
                continue;
            }
            userCount.put(opUserId, count);
            String opUser = userInfo.split(":")[1];
            return new CsOpUserInfo(opUserId, opUser, userIndex);
        }
        return new CsOpUserInfo(null, null, userIndex);
    }
}
