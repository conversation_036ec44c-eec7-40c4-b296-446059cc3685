package com.teyuntong.infra.task.service.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * tyt车型与集团车型对应关系，漏不漏轮胎ymm无对应车型
 */
@Getter
@AllArgsConstructor
public enum CarStyleEnum {
    CAR_TYPE_01("纯平", "爬梯车+平板", "1,15"),
    CAR_TYPE_02("高低高", "爬梯车", "15"),
    CAR_TYPE_03("带爬梯", "爬梯车", "15"),
    ;

    private final String tytCarStyle;
    private final String ymmCarStyle;
    private final String ymmCarStyleCode;

    public static String getYmmCarTypeByTytCarType(String tytCarType) {
        for (CarStyleEnum carStyleEnum : CarStyleEnum.values()) {
            if (carStyleEnum.tytCarStyle.equals(tytCarType)) {
                return carStyleEnum.ymmCarStyleCode;
            }
        }
        return "";
    }
}
