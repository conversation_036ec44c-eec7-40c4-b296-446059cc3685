package com.teyuntong.infra.task.service.remote.commonapi.api.service.impl;

import com.teyuntong.infra.task.service.biz.config.service.TytConfigService;
import com.teyuntong.infra.task.service.common.exception.TytException;
import com.teyuntong.infra.task.service.common.mq.pojo.ResponseEnum;
import com.teyuntong.infra.task.service.remote.commonapi.api.bean.TytHostConstant;
import com.teyuntong.infra.task.service.remote.commonapi.api.service.BaseApiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2022/3/21 9:53
 */
@Slf4j
public class BaseApiServiceImpl implements BaseApiService {

    @Autowired
    private TytConfigService tytConfigService;

    /**
     * 获取内网host
     * @return String
     */
    @Override
    public String getPrivatePlatHost(){

        String privatePlatHost = tytConfigService.getStringValue(TytHostConstant.PRIVATE_PLAT_HOST);

        if(StringUtils.isBlank(privatePlatHost)){
            throw TytException.createException(ResponseEnum.sys_error.info("privatePlatHost is blank!"));
        }

        return privatePlatHost;
    }

    /**
     * 获取公网host
     * @return String
     */
    @Override
    public String getPublicPlatHost(){

        String privatePlatHost = tytConfigService.getStringValue(TytHostConstant.PUBLIC_PLAT_HOST);

        if(StringUtils.isBlank(privatePlatHost)){
            throw TytException.createException(ResponseEnum.sys_error.info("privatePlatHost is blank!"));
        }

        return privatePlatHost;
    }
}
