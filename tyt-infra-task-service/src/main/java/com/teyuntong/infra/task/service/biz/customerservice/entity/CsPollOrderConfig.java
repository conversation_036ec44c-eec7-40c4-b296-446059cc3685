package com.teyuntong.infra.task.service.biz.customerservice.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 轮单配置表
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-09-29
 */
@Getter
@Setter
@TableName("cs_poll_order_config")
public class CsPollOrderConfig {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 轮单是否开启（1 开启 2 关闭）
     */
    private Integer pollOrderSwitch;

    /**
     * 单人处理中上限
     */
    private Integer peopleUpperLimit;

    /**
     * 轮单限制开始时间(存时分秒)
     */
    private String limitStartTime;

    /**
     * 轮单限制结束时间(存时分秒)
     */
    private String limitEndTime;

    /**
     * 参与轮单人员集合 (id:姓名;id:姓名)
     */
    private String pollOrderUserList;

    /**
     * 操作人ID
     */
    private Long operateUserId;

    /**
     * 操作人名称
     */
    private String operateUserName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date modifyTime;
}
