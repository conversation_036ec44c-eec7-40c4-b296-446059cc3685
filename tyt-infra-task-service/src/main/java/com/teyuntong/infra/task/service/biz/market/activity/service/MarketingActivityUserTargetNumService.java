package com.teyuntong.infra.task.service.biz.market.activity.service;


import com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.MarketingActivityUserTargetNumDO;

import java.util.List;

/**
 * <p>
 * 冲单活动用户目标单量 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-22
 */
public interface MarketingActivityUserTargetNumService {

    List<MarketingActivityUserTargetNumDO> getByActivityId(Long activityId, Long id);

    void updateTargetNum(MarketingActivityUserTargetNumDO updateDo);

    MarketingActivityUserTargetNumDO getByActivityUserId(Long activityId, Long userId);
}
