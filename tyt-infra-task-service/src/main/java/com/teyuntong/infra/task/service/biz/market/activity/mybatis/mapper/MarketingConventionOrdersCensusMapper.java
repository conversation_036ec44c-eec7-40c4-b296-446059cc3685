package com.teyuntong.infra.task.service.biz.market.activity.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.MarketingConventionOrdersCensusDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 冲单活动轮次奖品 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-22
 */
@Mapper
public interface MarketingConventionOrdersCensusMapper extends BaseMapper<MarketingConventionOrdersCensusDO> {

    MarketingConventionOrdersCensusDO getActivityLevel(@Param("activityId") Long activityId, @Param("rankLevel") Integer rankLevel, @Param("stage") Integer stage);
}
