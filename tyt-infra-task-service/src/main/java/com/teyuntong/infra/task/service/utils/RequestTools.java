package com.teyuntong.infra.task.service.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.parser.Feature;
import com.teyuntong.infra.task.service.common.exception.TytException;
import com.teyuntong.infra.task.service.common.request.TytBaseParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.TreeMap;

@Slf4j
public final class RequestTools {

    public final static String SIGN = "sign";
    public final static String CLIENT_VERSION = "clientVersion";
    public final static String CLIENT_SIGN = "clientSign";

    public final static String OS_VERSION = "osVersion";
    public final static String CLIENT_ID = "clientId";
    public final static String USER_ID = "userId";
    public final static String TTKN = "ttkn";
    public final static String TICKET = "ticket";

    public static TytBaseParam getBaseParamWithRequest(HttpServletRequest httpRequest){
        String clientSign = httpRequest.getParameter(RequestTools.CLIENT_SIGN);
        String clientVersion = httpRequest.getParameter(RequestTools.CLIENT_VERSION);

        String osVersion = httpRequest.getParameter(RequestTools.OS_VERSION);
        String clientId = httpRequest.getParameter(RequestTools.CLIENT_ID);
        String userId = httpRequest.getParameter(RequestTools.USER_ID);
        String ttkn = httpRequest.getParameter(RequestTools.TTKN);

        String ticket = httpRequest.getParameter(RequestTools.TICKET);

        TytBaseParam baseParam = new TytBaseParam();

        baseParam.setClientSign(clientSign);
        baseParam.setClientVersion(clientVersion);

        baseParam.setOsVersion(osVersion);
        baseParam.setClientId(clientId);
        baseParam.setUserId(Long.parseLong(userId));
        baseParam.setTtkn(ttkn);
        baseParam.setTicket(ticket);

        return baseParam;
    }

    public static TytBaseParam getBaseParamWithBody(Map<String, Object> bodyMap){

        String clientSign = CommonUtil.objToString(bodyMap.get(RequestTools.CLIENT_SIGN), null);
        String clientVersion = CommonUtil.objToString(bodyMap.get(RequestTools.CLIENT_VERSION), null);

        String osVersion = CommonUtil.objToString(bodyMap.get(RequestTools.OS_VERSION), null);
        String clientId = CommonUtil.objToString(bodyMap.get(RequestTools.CLIENT_ID), null);
        String userId = CommonUtil.objToString(bodyMap.get(RequestTools.USER_ID), null);
        String ttkn = CommonUtil.objToString(bodyMap.get(RequestTools.TTKN), null);

        String ticket = CommonUtil.objToString(bodyMap.get(RequestTools.TICKET), null);

        TytBaseParam baseParam = new TytBaseParam();

        baseParam.setClientSign(clientSign);
        baseParam.setClientVersion(clientVersion);

        baseParam.setOsVersion(osVersion);
        baseParam.setClientId(clientId);
        baseParam.setUserId(Long.parseLong(userId));
        baseParam.setTtkn(ttkn);
        baseParam.setTicket(ticket);
        return baseParam;
    }


    public static TytBaseParam getBaseParam(HttpServletRequest httpRequest){
        TytBaseParam tytBaseParam = null;
        //如果是json传参，校验body
        String contentType = httpRequest.getContentType();

        if (StringUtils.isNotEmpty(contentType) && contentType.contains(MediaType.APPLICATION_JSON_VALUE)) {
            String requestBodyStr = RequestTools.getRequestBodyStr(httpRequest, StandardCharsets.UTF_8);
            TreeMap treeMapBody = JSON.parseObject(requestBodyStr, TreeMap.class, Feature.SortFeidFastMatch);

            tytBaseParam = RequestTools.getBaseParamWithBody(treeMapBody);
        }else{
            tytBaseParam = RequestTools.getBaseParamWithRequest(httpRequest);
        }

        return tytBaseParam;
    }

    /**
     * 获取请求参数
     *
     * @param httpRequest
     * @return
     */
    public static TreeMap<String, String> getParameterMap(HttpServletRequest httpRequest) {
        Map<String, String[]> srcParamMap = httpRequest.getParameterMap();
        TreeMap<String, String> treeMap = new TreeMap<String, String>();
        for (String name : srcParamMap.keySet()) {
            String[] values = srcParamMap.get(name);
            String valueStr = "";
            for (int i = 0; i < values.length; i++) {
                valueStr = (i == values.length - 1) ? valueStr + values[i]
                        : valueStr + values[i] + ",";
            }
            treeMap.put(name, valueStr);
        }

        return treeMap;
    }

    /**
     * 获得排序后的请求参数不包含秘钥
     * @param httpRequest
     * @return 获得排序后的请求参数不包含秘钥
     */
    public static String getRequestParameters(HttpServletRequest httpRequest) {
        TreeMap<String, String> treeMap = getParameterMap(httpRequest);
        return treeMap.isEmpty() ? null : String.join("&", treeMap.keySet());
    }

    public static byte[] getRequestBodyBytes(HttpServletRequest httpRequest) {
        byte[] bytes = null;
        ByteArrayOutputStream output = null;

        try {
            ServletInputStream inputStream = httpRequest.getInputStream();
            output = new ByteArrayOutputStream();
            IOUtils.copy(inputStream, output);

            bytes = output.toByteArray();
        } catch (IOException e){
            throw TytException.createException(e);
        } finally {
            try {
                output.close();
            } catch (IOException e) {
                log.error("", e);
            }
        }

        return bytes;
    }

    public static String getRequestBodyStr(HttpServletRequest httpRequest, Charset charset){

        byte[] bodyBytes = getRequestBodyBytes(httpRequest);

        String bodyTxt = null;

        if(bodyBytes != null){
            bodyTxt = new String(bodyBytes, charset);
        }
        return bodyTxt;
    }

    /**
     * 获取远端Ip地址
     * @param request
     * @return
     */
    public static String getRemoteIp(HttpServletRequest request) {
        String unknown = "unknown";
        String ipStr = "";
        try {
            ipStr = request.getHeader("X-Forwarded-For");

            if (StringUtils.isBlank(ipStr) || unknown.equalsIgnoreCase(ipStr)) {
                ipStr = request.getHeader("X-Real-IP");
            }
            if (StringUtils.isBlank(ipStr) || unknown.equalsIgnoreCase(ipStr)) {
                ipStr = request.getHeader("Proxy-Client-IP");
            }
            if (StringUtils.isBlank(ipStr) || unknown.equalsIgnoreCase(ipStr)) {
                ipStr = request.getHeader("WL-Proxy-Client-IP");
            }
            if (StringUtils.isBlank(ipStr) || unknown.equalsIgnoreCase(ipStr)) {
                ipStr = request.getHeader("HTTP_CLIENT_IP");
            }
            if (StringUtils.isBlank(ipStr) || unknown.equalsIgnoreCase(ipStr)) {
                ipStr = request.getHeader("HTTP_X_FORWARDED_FOR");
            }
            if (StringUtils.isBlank(ipStr) || unknown.equalsIgnoreCase(ipStr)) {
                ipStr = request.getRemoteAddr();
            }

            if(StringUtils.isNotEmpty(ipStr) && !unknown.equalsIgnoreCase(ipStr)){
                //多次反向代理后会有多个ip值，第一个ip才是真实ip
                //为防止暴露服务器ip
                /*
                if(ipStr.equals("127.0.0.1") || ipStr.equalsIgnoreCase("localhost")) {
                    // 根据网卡取本机配置的IP
                    InetAddress inet = null;
                    try {
                        inet = InetAddress.getLocalHost();

                        ipStr = inet.getHostAddress();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                */
                if(ipStr.equals("0.0.0.0")) {
                    ipStr = "127.0.0.1";
                }

                int index = ipStr.indexOf(",");
                if(index != -1) {
                    ipStr = ipStr.substring(0, index);
                }
            }

            if(!CommonUtil.isValidIp(ipStr)){
                ipStr = "";
            }
        } catch (Exception e) {
            log.error("", e);
        }

        return ipStr;
    }

    /**
     * 获取远端Ip地址
     * @return
     */
    public static String getRemoteIp(){
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = servletRequestAttributes.getRequest();

        return getRemoteIp(request);
    }

    public static String getUserAgent(){

        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = servletRequestAttributes.getRequest();

        String userAgent = request.getHeader("User-Agent");

        return userAgent;

    }

}
