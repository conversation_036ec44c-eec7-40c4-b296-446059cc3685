package com.teyuntong.infra.task.service.utils;

import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public class CityUtil {
    public static final String[] municipalit_array = new String[]{"北京市", "天津市", "上海市", "重庆市"};
    public static final Set<String> municipality_set = new HashSet();

    public CityUtil() {
    }

    public static boolean isMunicipalit(String city) {
        boolean result = municipality_set.contains(city);
        return result;
    }

    public static String createAddressInfo(String city, String area) {
        return createAddressInfo("", city, area);
    }

    public static String createAddressInfo(String province, String city, String area) {
        StringBuilder builder = new StringBuilder();
        if (province == null) {
            province = "";
        }

        if (isMunicipalit(city)) {
            province = "";
            city = city.substring(0, city.length() - 1);
        }

        builder.append(province);
        builder.append(city);
        builder.append(area);
        return builder.toString();
    }

    /**
     * 拼接地址信息，不带区
     * @param province
     * @param city
     * @return
     */
    public static String createAddressInfoNoArea(String province, String city){
        return createAddressInfo(province, city, "");
    }

    public static String toMapPointStr(Integer pointNumber) {
        if (pointNumber == null) {
            return "";
        } else {
            return pointNumber.toString().length() > 6 ? (new BigDecimal(pointNumber)).movePointLeft(6).toString() : (new BigDecimal(pointNumber)).movePointLeft(2).toString();
        }
    }

    public static String toCoordStr(Integer coordNumber) {
        return coordNumber == null ? "" : (new BigDecimal(coordNumber)).movePointLeft(2).toString();
    }

    /**
     * 坐标系转换
     *
     * @param coordNumber
     * @return
     */
    public static BigDecimal toCoordBigDecimal(Integer coordNumber) {
        if (coordNumber != null) {
            return new BigDecimal(coordNumber).movePointLeft(2);
        }
        return null;
    }

    /**
     * 经纬度
     * @param pointNumber
     * @return
     */
    public static BigDecimal toMapPointBigDecimal(Integer pointNumber) {
        if (pointNumber != null) {
            if (pointNumber.toString().length() > 6) {
                return new BigDecimal(pointNumber).movePointLeft(6);
            } else {
                return new BigDecimal(pointNumber).movePointLeft(2);
            }
        }
        return null;
    }

    public static BigDecimal toMapPointBigDecimal(String pointNumber) {
        if (StringUtils.isBlank(pointNumber)) {
            if (pointNumber.length() > 6) {
                return new BigDecimal(pointNumber).movePointLeft(6);
            } else {
                return new BigDecimal(pointNumber).movePointLeft(2);
            }
        }
        return null;

    }

    static {
        municipality_set.addAll(Arrays.asList(municipalit_array));
    }
}