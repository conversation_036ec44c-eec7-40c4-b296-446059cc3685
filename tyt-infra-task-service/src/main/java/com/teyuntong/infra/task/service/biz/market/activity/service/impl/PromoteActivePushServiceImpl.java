package com.teyuntong.infra.task.service.biz.market.activity.service.impl;

import cn.hutool.core.date.DateUtil;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.service.TransportMainService;
import com.teyuntong.infra.task.service.biz.market.activity.service.PromoteActivePushService;
import com.teyuntong.infra.task.service.biz.user.abtest.entity.AbtestConfigDO;
import com.teyuntong.infra.task.service.biz.user.abtest.service.AbtestConfigService;
import com.teyuntong.infra.task.service.biz.user.abtest.service.AbtestConfigUserService;
import com.teyuntong.infra.task.service.biz.user.info.mybatis.entity.User;
import com.teyuntong.infra.task.service.biz.user.info.service.UserCallPhoneRecordService;
import com.teyuntong.infra.task.service.biz.user.info.service.UserService;
import com.teyuntong.infra.task.service.biz.user.permission.mybatis.entity.UserPermissionDO;
import com.teyuntong.infra.task.service.biz.user.permission.pojo.PermissionEnum;
import com.teyuntong.infra.task.service.biz.user.permission.service.UserPermissionService;
import com.teyuntong.infra.task.service.common.mq.pojo.NativePageEnum;
import com.teyuntong.infra.task.service.common.mq.pojo.NotifyMessagePush;
import com.teyuntong.infra.task.service.common.mq.service.MessageCenterPushService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2025/01/23 11:03
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PromoteActivePushServiceImpl implements PromoteActivePushService {

    private final UserService userService;
    private final StringRedisTemplate stringRedisTemplate;
    private final MessageCenterPushService messageCenterPushService;
    private final UserCallPhoneRecordService userCallPhoneRecordService;
    private final AbtestConfigService abtestConfigService;
    private final AbtestConfigUserService abtestConfigUserService;
    private final UserPermissionService userPermissionService;
    private final TransportMainService transportMainService;

    private static final String NO_CAR_LOGIN_PUSH_USER_CACHE_KEY = "task:car:no:login:push:user:";
    private static final String NO_CAR_ACTIVE_PUSH_USER_CACHE_KEY = "task:car:no:active:push:user:";
    private static final String NO_GOODS_ACTIVE_PUSH_USER_CACHE_KEY = "task:goods:no:active:push:user:";
    private static final String GOODS_NO_ACTIVE_ABTEST_CODE = "goods_no_active_push";


    @Override
    public void carNoLoginPush() {
        // 获取车用户未登录天数≥90天且近360天登录过
        Date now = new Date();
        Date startTime = DateUtil.beginOfDay(DateUtil.offsetDay(now, -360));
        Date endTime = DateUtil.endOfDay(DateUtil.offsetDay(now, -90));
        Long startUserId = 0L;
        String title = "已送您免费找货卡！";
        String content = "大件货源多，找货快，点击找货";
        List<Long> userIds = new ArrayList<>();
        while (true) {
            List<Long> users = userService.getByCarLoginTime(startUserId, startTime, endTime);
            if (CollectionUtils.isEmpty(users)) {
                break;
            }
            for (Long userId : users) {
                String cacheKey = stringRedisTemplate.opsForValue().get(NO_CAR_LOGIN_PUSH_USER_CACHE_KEY + userId);
                if (StringUtils.isNotBlank(cacheKey)){
                    continue;
                }
                userIds.add(userId);
                stringRedisTemplate.opsForValue().set(NO_CAR_LOGIN_PUSH_USER_CACHE_KEY + userId, "1", 20, TimeUnit.DAYS);
            }
            this.sendNotifyMessage(userIds, title, content, 1);
            userIds.clear();
            startUserId = users.get(users.size() - 1);
        }
    }

    @Override
    public void carNoActivePush() {
        Date now = new Date();
        Date startTime = DateUtil.beginOfDay(DateUtil.offsetDay(now, -89));
        Date endTime = DateUtil.endOfDay(DateUtil.offsetDay(now, -7));
        Date callStartTime = DateUtil.beginOfDay(DateUtil.offsetDay(now, -15));
        Long startUserId = 0L;
        String title = "拨打货主电话，拿现金大奖，点击找货";
        String content = "有多位货主正在等您接单，抓紧订货";
        List<Long> userIds = new ArrayList<>();
        while (true) {
            List<Long> users = userService.getByCarLoginTime(startUserId, startTime, endTime);
            if (CollectionUtils.isEmpty(users)) {
                break;
            }
            for (Long userId : users) {
                String cacheKey = stringRedisTemplate.opsForValue().get(NO_CAR_ACTIVE_PUSH_USER_CACHE_KEY + userId);
                if (StringUtils.isNotBlank(cacheKey)){
                    continue;
                }
                //查询用户15天内是否活跃过
                int callCount = userCallPhoneRecordService.getUserCallRecordsCount(userId, callStartTime, new Date());
                if (callCount == 0){
                    userIds.add(userId);
                    stringRedisTemplate.opsForValue().set(NO_CAR_ACTIVE_PUSH_USER_CACHE_KEY + userId, "1", 3, TimeUnit.DAYS);
                }
            }
            this.sendNotifyMessage(userIds, title, content, 1);
            userIds.clear();
            startUserId = users.get(users.size() - 1);
        }
    }

    @Override
    public void goodsNoActivePush() {
        AbtestConfigDO abtestConfigDO = abtestConfigService.getByCode(GOODS_NO_ACTIVE_ABTEST_CODE);
        if (Objects.isNull(abtestConfigDO) || Objects.equals(abtestConfigDO.getEnable(), 0)) {
            log.info("货老客非会员用户push消息提醒，ab测不存在或未启用");
            return;
        }
        Long abConfigId = abtestConfigDO.getId();
        Date now = new Date();
        List<String> servicePermissionTypeIds = Stream.of(
                        PermissionEnum.货会员,
                        PermissionEnum.货会员次数
                )
                .map(PermissionEnum::getTypeId)
                .collect(Collectors.toList());
        Date publishStartTime = DateUtil.beginOfDay(DateUtil.offsetDay(now, -163));
        Date publishEndTime = DateUtil.endOfDay(DateUtil.offsetDay(now, -6));
        Date loginStartTime = DateUtil.beginOfDay(DateUtil.offsetDay(now, -174));
        Date loginEndTime = DateUtil.endOfDay(DateUtil.offsetDay(now, -4));
        Long startUserId = 0L;
        String title = "登录即可免费发货";
        String content = "您的免费发货次数已到账，点击发货吧~";
        List<Long> userIds = new ArrayList<>();
        while (true) {
            List<Long> users = abtestConfigUserService.getUserIdList(abConfigId,startUserId);
            if (CollectionUtils.isEmpty(users)) {
                break;
            }
            for (Long userId : users) {
                String cacheKey = stringRedisTemplate.opsForValue().get(NO_GOODS_ACTIVE_PUSH_USER_CACHE_KEY + userId);
                if (StringUtils.isNotBlank(cacheKey)){
                    continue;
                }
                //用户是否是货会员
                List<UserPermissionDO> list = userPermissionService.getListByUserIdAndType(userId, servicePermissionTypeIds);
                if (CollectionUtils.isNotEmpty(list)){
                    continue;
                }
                //4天≤最后一次登录距今天数＜174天
                User user = userService.getByUserId(userId);
                if (user.getGoodsLastLoginTime() != null
                        && user.getGoodsLastLoginTime().after(loginStartTime)
                        && user.getGoodsLastLoginTime().before(loginEndTime)){
                    userIds.add(userId);
                    stringRedisTemplate.opsForValue().set(NO_GOODS_ACTIVE_PUSH_USER_CACHE_KEY + userId, "1", 3, TimeUnit.DAYS);
                    continue;
                }
                // 6天≤最后一天发货距今天数＜164天
                Date lastDate = transportMainService.getUserLastPublishTime(userId, publishStartTime);
                if (lastDate != null && lastDate.before(publishEndTime)){
                    stringRedisTemplate.opsForValue().set(NO_GOODS_ACTIVE_PUSH_USER_CACHE_KEY + userId, "1", 3, TimeUnit.DAYS);
                    userIds.add(userId);
                }
            }
            this.sendNotifyMessage(userIds, title, content, 2);
            userIds.clear();
            startUserId = users.get(users.size() - 1);
        }
    }


    private void sendNotifyMessage(List<Long> userIds, String title, String content, int port) {
        if (CollectionUtils.isEmpty(userIds)){
            return;
        }
        NotifyMessagePush notifyMessage = new NotifyMessagePush();
        notifyMessage.setTitle(title);
        notifyMessage.setRemarks(title);
        if (port == 1){
            notifyMessage.setCarPush((short) 1);
            notifyMessage.openWithNativePage(NativePageEnum.transport_search);
        } else {
            notifyMessage.setGoodsPush((short) 1);
            notifyMessage.openWithNativePage(NativePageEnum.goods_publish);
        }
        notifyMessage.setUserIdList(userIds);
        notifyMessage.setContent(content);
        messageCenterPushService.sendMultiMessage(null, null, notifyMessage);
    }
}
