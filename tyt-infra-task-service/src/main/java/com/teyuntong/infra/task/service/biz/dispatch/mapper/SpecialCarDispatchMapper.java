package com.teyuntong.infra.task.service.biz.dispatch.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.infra.task.service.biz.dispatch.entity.SpecialCarDispatchDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;


/**
 * <p>
 * 专车派单表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-09-19
 */
@Mapper
public interface SpecialCarDispatchMapper extends BaseMapper<SpecialCarDispatchDO> {

    /**
     * 查询未派单的货源记录
     *
     * <AUTHOR>
     * @param
     * @return List<TytSpecialCarDispatch>
     */
    List<SpecialCarDispatchDO> selectUnDispatchGoodsV2(@Param("todayDate") Date todayDate);

    /**
     * 查询同时指派的未派单的货源记录
     *
     * <AUTHOR>
     * @param
     * @return List<TytSpecialCarDispatch>
     */
    List<SpecialCarDispatchDO> selectUnDispatchGoodsV3(@Param("todayDate") Date todayDate);

    /**
     * 查询派单失败的订单记录
     * a. 无人接单时间超过15分钟，或符合条件的司机全部指派完成，或无符合条件的司机，结束自动指派，转人工
     * <AUTHOR>
     * @param todayDate
     * @return List<TytSpecialCarDispatch>
     */
    List<SpecialCarDispatchDO> selectDispatchFailOrdersV2(@Param("thresholdTime") Date thresholdTime, @Param("todayDate") Date todayDate);

    /**
     * 查询待指派的记录列表
     *
     * @param todayDate
     * @return
     */
    List<SpecialCarDispatchDO> selectDispatchFailOrdersV3(@Param("todayDate") Date todayDate);

    /**
     * 查询先后指派的记录列表
     *
     * @return
     */
    List<SpecialCarDispatchDO> selectDispatchInOrderRecords();

    /**
     * 更新派单信息
     *
     * <AUTHOR>
     * @param
     * @return int
     */
    void updateDispatchCarAmount(@Param("id") Long id,
                                 @Param("nowDate") Date nowDate,
                                 @Param("firstDispatchTime") Date firstDispatchTime,
                                 @Param("dispatchCarAmount") Integer dispatchCarAmount,
                                 @Param("dispatchComplete") Integer dispatchComplete);

    /**
     * 更新派单通知次数
     *
     * <AUTHOR>
     * @param
     * @return int
     */
    void updateNotifyCountAndModifyTime(@Param("id") Long id, @Param("nowDate") Date nowDate, @Param("notifyCount") Integer notifyCount);
}
