package com.teyuntong.infra.task.service.remote.commonapi.service;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.user.trace.client.location.CarLocationRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/12/17 16:13
 */
@Service
@FeignClient(name = "tyt-user-trace", path = "trace", contextId = "carLocationRpcService", fallbackFactory = CarLocationRemoteService.CarLocationRemoteServiceFallbackFactory.class)
public interface CarLocationRemoteService extends CarLocationRpcService {

    @Component
    class CarLocationRemoteServiceFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<CarLocationRemoteService> {
        protected CarLocationRemoteServiceFallbackFactory() {
            super(true, CarLocationRemoteService.class);
        }
    }
}
