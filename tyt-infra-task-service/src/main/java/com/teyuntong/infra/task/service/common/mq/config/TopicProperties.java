package com.teyuntong.infra.task.service.common.mq.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/12/20 11:01
 */
@Data
@Component
@ConfigurationProperties(prefix = "mq-topic-other")
public class TopicProperties {
    /**
     * 短信中心topic
     */
    @NestedConfigurationProperty
    private ShortMessage shortMessage;

    /**
     * 短信中心
     */
    @Data
    public static class ShortMessage {


        /**
         * 发送短信消息topic
         */
        private String topic;

        /**
         * 发送短信消息tag
         */
        private String tag;

    }
}
