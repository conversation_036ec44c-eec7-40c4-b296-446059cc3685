package com.teyuntong.infra.task.service.utils;

import com.teyuntong.infra.task.service.common.security.MD5;
import com.teyuntong.infra.task.service.common.security.RSA;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

/**
 * Created by duanwc on 2019/11/29.
 * 签名/验签工具类
 */
public class SecureLinkUtil {

    private static Logger logger = LogManager.getLogger(SecureLinkUtil.class);

    // 字符编码格式 目前支持 gbk 或 utf-8
    public static final String INPUT_CHARSET = "utf-8";

    private SecureLinkUtil() {
    }

    /**
     * 签名字符串
     *
     * @param text     需要签名的字符串
     * @param key      密钥
     * @param signType 签名类型 MD5|RSA
     * @return 签名结果
     */
    public static String sign(String text, String key, String signType) {
        String sign = null;
        if ("RSA".equals(signType)) {
            sign = RSA.sign(text, key, INPUT_CHARSET);
        } else if ("MD5".equals(signType)) {
            sign = MD5.sign(text, key, INPUT_CHARSET);
        }
        return sign;
    }

    /**
     * 验证消息是否是服务发出的合法消息
     *
     * @param params   通知返回来的参数数组
     * @param key      MD5密钥 或 商户RSA公钥
     * @param signType 签名类型 MD5|RSA
     * @return 验证结果
     */
    public static boolean verify(Map<String, String> params, String key, String signType) {

        //判断responsetTxt是否为true，isSign是否为true
        //responsetTxt的结果不是true，与服务器设置问题、合作身份者ID、notify_id一分钟失效有关
        //isSign不是true，与安全校验码、请求时的参数格式（如：带自定义参数等）、编码格式有关
        String sign = "";
        if (params.get("sign") != null) {
            sign = params.get("sign");
        }
        boolean isSign = getSignVeryfy(params, sign, key, signType);

        //写日志记录（若要调试，请取消下面两行注释）
        logger.info("isSign={} 返回来的参数:{}", isSign, createLinkString(params));
        return isSign;
    }

    /**
     * 验证消息是否是服务发出的合法消息
     *
     * @param params       通知返回来的参数数组
     * @param sortJsonBody body的json经过treeMap滤空，排序后的json字符串
     * @param key          MD5密钥 或 商户RSA公钥
     * @param signType     签名类型 MD5|RSA
     * @return 验证结果
     */
    public static boolean verify(Map<String, String> params, String sortJsonBody, String key, String signType) {

        //判断responsetTxt是否为true，isSign是否为true
        //responsetTxt的结果不是true，与服务器设置问题、合作身份者ID、notify_id一分钟失效有关
        //isSign不是true，与安全校验码、请求时的参数格式（如：带自定义参数等）、编码格式有关
        String sign = "";
        if (params.get("sign") != null) {
            sign = params.get("sign");
        }
        boolean isSign = getSignVeryfy(params, sortJsonBody, sign, key, signType);

        //写日志记录（若要调试，请取消下面两行注释）
        logger.info("isSign={} 返回来的参数:{}", isSign, createLinkString(params) + "&body=" + sortJsonBody);
        return isSign;
    }

    /**
     * 根据反馈回来的信息，生成签名结果
     *
     * @param params 通知返回来的参数数组
     * @param key    MD5密钥 或 商户RSA公钥
     * @param sign   比对的签名结果
     * @return 生成的签名结果
     */
    private static boolean getSignVeryfy(Map<String, String> params, String sign, String key, String signType) {
        //过滤空值、sign与sign_type参数
        Map<String, String> sParaNew = paraFilter(params);
        //获取待签名字符串
        String preSignStr = createLinkString(sParaNew);
        //获得签名验证结果
        boolean isSign = false;
        if ("MD5".equals(signType)) {
            isSign = MD5.verify(preSignStr, sign, key, INPUT_CHARSET);
        } else if ("RSA".equals(signType)) {
            isSign = RSA.verify(preSignStr, sign, key, INPUT_CHARSET);
        }
        return isSign;
    }

    /**
     * 根据反馈回来的信息，生成签名结果
     *
     * @param params       通知返回来的参数数组
     * @param sortJsonBody body的json经过treeMap滤空，排序后的json字符串
     * @param key          MD5密钥 或 商户RSA公钥
     * @param sign         比对的签名结果
     * @return 生成的签名结果
     */
    private static boolean getSignVeryfy(Map<String, String> params, String sortJsonBody, String sign, String key, String signType) {
        //过滤空值、sign与sign_type参数
        Map<String, String> sParaNew = paraFilter(params);
        //获取待签名字符串
        String preSignStr = createLinkString(sParaNew);
        preSignStr += sortJsonBody;//连接body的字符串
        //获得签名验证结果
        boolean isSign = false;
        if ("MD5".equals(signType)) {
            isSign = MD5.verify(preSignStr, sign, key, INPUT_CHARSET);
        } else if ("RSA".equals(signType)) {
            isSign = RSA.verify(preSignStr, sign, key, INPUT_CHARSET);
        }
        return isSign;
    }

    /**
     * 除去数组中的空值和签名参数
     *
     * @param sArray 签名参数组
     * @return 去掉空值与签名参数后的新签名参数组
     */
    public static Map<String, String> paraFilter(Map<String, String> sArray) {

        Map<String, String> result = new HashMap<String, String>();

        if (sArray == null || sArray.size() <= 0) {
            return result;
        }

        for (String key : sArray.keySet()) {
            String value = sArray.get(key);
            if (value == null || value.equals("") || key.equalsIgnoreCase("sign")
                    || key.equalsIgnoreCase("signType")) {
                continue;
            }
            result.put(key, value);
        }

        return result;
    }

    /**
     * 把数组所有元素排序，并按照“参数=参数值”的模式用“&”字符拼接成字符串
     *
     * @param params 需要排序并参与字符拼接的参数组
     * @return 拼接后字符串
     */
    public static String createLinkString(Map<String, String> params) {
        removeNullValue(params); // 过滤

        List<String> keys = new ArrayList<String>(params.keySet());
        Collections.sort(keys);

        String prestr = "";

        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            String value = params.get(key);

            if (i == keys.size() - 1) {//拼接时，不包括最后一个&字符
                prestr = prestr + key + "=" + value;
            } else {
                prestr = prestr + key + "=" + value + "&";
            }
        }

        return prestr;
    }


    /**
     * 移除map中的value空值
     *
     * @param map
     * @return
     */
    public static void removeNullValue(Map<String, String> map) {
        Set set = map.keySet();
        for (Iterator iterator = set.iterator(); iterator.hasNext(); ) {
            Object obj = iterator.next();
            Object value = map.get(obj);
            if (value == null || "".equals(value)) {
                iterator.remove();
            }
        }
    }

    /**
     * 将url参数转换成map
     *
     * @param param aa=11&bb=22&cc=33
     * @return
     */
    public static Map<String, String> getUrlParams(String param) {
        Map<String, String> map = new HashMap<>();
        if (StringUtils.isBlank(param)) {
            return map;
        }
        String[] params = param.split("&");
        for (int i = 0; i < params.length; i++) {
            String[] p = StringUtils.split(params[i], "=", 2);
            if (p.length == 2) {
                map.put(p[0], p[1]);
            }
        }
        return map;
    }

    public static void main(String[] args) {
        //MD5
        String secretKey = "b470d4909aab55a8da46e07da294d990";
        //RSA
        String privateKey = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAMTh4WzAT0p7n5ZxsEuhMq7/9rVMuGgU2bNyoJHQLYRFw5z5Q6VsK0iV5NerOIZvL87dcjQwasa70FP+kyETz24P9s0hFPxz5sEUlG/1jUM1YTlDMNsNDKKLYyo8SK33oOvmHhBCyYB9mSRU6Sg9RWHYOr2cBKyeNw7KRwSvr0iXAgMBAAECgYEAnpXnk0awV6mOWYtH2xOEFMwet9iNbWDmHpJFXk5Wa4YaU7XAVF3122jJxSKozDiBRNrA4+LgDyY3X4DemDUxzebsKM3ovvjmfxzz1xMlRhjtStiitZOrmXB7rP+AY32P9K+ju9QETdmFhv/Eo2CyYwS/qv/ZWdTT0w73tlkHUAkCQQDosJhYeFX6VHdTRY+IIFQzVzjtf4+X1UjtrsY/5xVeXBshWkRr3YJZ1TqgUuoFi0CBeKPJ/SQqf3l0P5QW6RNLAkEA2Jr9wY7eXLgyULjzFWGZY5+iMloB9kFtQY8Pyb7reNLSctDm9EtqucDk3Tt1tDg37pouWIiH+JcRao7ewliEZQJAXtoV0AKFsbyaw6QYZTogeKk2eIHGK/Nyfgj/oW72Y/eRy5+7QmkSSMJF2rLR2J6yFdTyl1UMWBKMlNmR6WDrTwJAYqTDJ6D+107zaxuvaG9YQaxzW2buOc22CgMy93fBGPc57BwC8gJeilW0ZKQtiHOJ1VnV/W/ALI/v2I1UGUHimQJAZSZg7ezDycttWxeV+Oe8zaqmhWe4HNBT0gKP7nTKe/PdqjIPscb9Zz9BLB/aTTCJhL9UZc02AvGMOZyANMxdBw==";
        String publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDE4eFswE9Ke5+WcbBLoTKu//a1TLhoFNmzcqCR0C2ERcOc+UOlbCtIleTXqziGby/O3XI0MGrGu9BT/pMhE89uD/bNIRT8c+bBFJRv9Y1DNWE5QzDbDQyii2MqPEit96Dr5h4QQsmAfZkkVOkoPUVh2Dq9nASsnjcOykcEr69IlwIDAQAB";
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("merId", "10013");
        paramMap.put("amout", "1000.24");
        paramMap.put("orderNo", "20191129173423473001");
        paramMap.remove("sign");
        String text = createLinkString(paramMap);
        // RSA签名测试
        String sign = sign(text, privateKey, "RSA");
        System.out.println("RSA sign:" + sign);
        text += "&sign=" + sign;
        System.out.println("RSA params:" + text);

        // RSA验签测试
        paramMap.put("sign", "oG9I0t4jvwqA1r5FF2RaNBE/T4nrznnOaMyfNhz+XbkLGLcP7tb7CZJatMsMZLsrtJv95uJRgdcSsaikeDx5RqhrQeHWF7+Tn0Yhyr7ENtbWrEIGP99EM5qgjqXVa0LDyqNzz+fi8hZr4tianbfdkD8U4R4494rIhaIkquCZfQY=");
        boolean isSign = verify(paramMap, publicKey, "RSA");
        System.out.println("RSA 验签结果：" + isSign);

        System.out.println("\n");

        // MD5 签名测试
        paramMap.remove("sign");
        text = createLinkString(paramMap);
        sign = sign(text, secretKey, "MD5");
        System.out.println("MD5 sign:" + sign);
        text += "&sign=" + sign;
        System.out.println("MD5 params:" + text);
        // MD5验签测试
        paramMap.put("sign", "c9460c9b71c4615bcfc302b3a81b2630");
        isSign = verify(paramMap, secretKey, "MD5");
        System.out.println("MD5 验签结果：" + isSign);
    }
}
