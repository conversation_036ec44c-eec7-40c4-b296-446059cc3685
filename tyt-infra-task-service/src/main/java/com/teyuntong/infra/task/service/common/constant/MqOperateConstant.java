package com.teyuntong.infra.task.service.common.constant;

/**
* 订单发送mq常量类
* <AUTHOR>
* @since 2024/8/14 11:10
*/
public class MqOperateConstant {

    /*
     * mq消息处理状态 1:未处理  2:已处理  3:处理失败 4:连续失败后状态
     *
     */
    public static final int MQ_STATUS_DEAL_NOT_STARTED = 1;
    public static final int MQ_STATUS_DEAL_SUCCESS = 2;
    public static final int MQ_STATUS_DEAL_FAILED = 3;
    public static final int MQ_STATUS_DEAL_CONTINUE_FAILED = 4;

    /**
     * 消息延时时间
     */
    public static final Long DELAY_TIME = 3000L;

    /**
     * 满帮开放平台MQ消息类型列表
     */
    public static final String MB_MESSAGE_TYPES="MB_OPENPLATFORM_MQ_MESSAGE_TYPE_LIST";

    /**
     * APP信息费操作
     */
    public static final int INFO_FEE_OPERATE_DEAL = 36;


    /**
     *  订单操作 状态同步阔库
     */
    public static final int ORDER_OPERATE_STATUS_SYNC = 1077;

    /**
     *  运费操作
     */
    public static final int ORDERS_FREIGHT_OPERATE_CODE = 1088;

    /**
     * 优惠券到期提醒消息
     */
    public static final int COUPON_EXPIRE_NOTIFY = 38;

}
