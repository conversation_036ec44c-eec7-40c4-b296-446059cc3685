package com.teyuntong.infra.task.service.common.mq.pojo;

import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.util.HashMap;
import java.util.Map;
@Data
public class NotifyMessagePush extends MessagePushBase {

    /** ========== 以下字段有默认值，可不设置 ========== **/
    /** 打开方式0打开应用,1打开连接，如果是1，但是linkUrl 为空，则打开对应message url **/
    private Integer openType = NotifyOpenTypeEnum.app.getCode();
    /** 有效时长单位小时 **/
    private Integer timeLong = 1;

    /**
     * 推送类型
     */
    private String pushCode;

    /** 通知透传内容 **/
    private String extraData;

    /** 打开原生页面时的code **/
    private Integer typeCode;
    /** typeCode 不为null时，需传递的参数 **/
    private Map<String, String> nativeParameterMap;

    public static NotifyMessagePush createByPushBase(MessagePushBase pushBase){
        NotifyMessagePush notifyMessagePush = beanConvertObject(pushBase, new NotifyMessagePush());

        return notifyMessagePush;
    }

    public static NotifyMessagePush beanConvertObject(MessagePushBase source, NotifyMessagePush target) {
        NotifyMessagePush result = null;

        if(target == null){
            return result;
        }

        if (source != null) {
            result = target;

            BeanUtils.copyProperties(source, result, target.getClass());
        }
        return result;
    }

    public void openWithNativePage(NativePageEnum nativePageEnum) {
        this.typeCode = nativePageEnum.getCode();
        this.openType = NotifyOpenTypeEnum.link.getCode();
    }
    public void openWithNativePage(NativePageEnum nativePageEnum,Map<String, String> nativeParameterMap) {
        this.typeCode = nativePageEnum.getCode();
        this.openType = NotifyOpenTypeEnum.link.getCode();
        this.nativeParameterMap = nativeParameterMap;
    }

    public void addNativeParameter(String key, String value) {
        if (this.nativeParameterMap == null) {
            this.nativeParameterMap = new HashMap();
        }

        this.nativeParameterMap.put(key, value);
    }
}
