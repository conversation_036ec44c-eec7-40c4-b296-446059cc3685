package com.teyuntong.infra.task.service.remote.enterprise;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.outer.export.service.client.invoice.hbwj.service.InvoiceWjOpenApiService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

@Service
@FeignClient(name = "tyt-outer-export-service", path = "outer-export", contextId = "invoiceWjOpenApiService", fallbackFactory = WJOpenApiRemoteService.WJOpenApiRemoteServiceFallbackFactory.class)
public interface WJOpenApiRemoteService extends InvoiceWjOpenApiService {
    @Component
    class WJOpenApiRemoteServiceFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<WJOpenApiRemoteService> {
        protected WJOpenApiRemoteServiceFallbackFactory() {
            super(true, WJOpenApiRemoteService.class);
        }
    }
}
