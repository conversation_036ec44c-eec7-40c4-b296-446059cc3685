package com.teyuntong.infra.task.service.remote.goods;

import com.teyuntong.goods.service.client.transport.service.ThPriceRpcService;
import com.teyuntong.goods.service.client.transport.service.TransportMainRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;


@Service
@FeignClient(name = "tyt-goods-service", path = "goods", contextId = "ThPriceRpcService", fallbackFactory = ThPriceRemoteService. ThPriceRemoteFallbackFactory.class)
public interface ThPriceRemoteService extends ThPriceRpcService {

    @Component
    class  ThPriceRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<ThPriceRemoteService> {
        protected  ThPriceRemoteFallbackFactory() {
            super(true, ThPriceRemoteService.class);
        }
    }
}
