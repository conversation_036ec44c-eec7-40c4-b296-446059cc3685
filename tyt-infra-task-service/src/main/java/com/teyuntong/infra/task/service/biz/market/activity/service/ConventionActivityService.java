package com.teyuntong.infra.task.service.biz.market.activity.service;


import com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.ConventionActivityDO;
import com.teyuntong.infra.task.service.biz.user.car.pojo.FeedbackNumBean;

import java.util.List;

/**
 * <p>
 * 履约活动表 服务类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-09-30
 */
public interface ConventionActivityService {

    void giveUserEquity();

    List<ConventionActivityDO> getByActivityId(Long activityId);

    void updateEvaluateNum(List<FeedbackNumBean> feedbackNumBeans, Long activityId);

}
