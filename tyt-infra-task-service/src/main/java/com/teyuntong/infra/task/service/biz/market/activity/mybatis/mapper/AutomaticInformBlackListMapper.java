package com.teyuntong.infra.task.service.biz.market.activity.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.AutomaticInformBlackListDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 自动营销触达黑名单表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-04-01
 */
@Mapper
public interface AutomaticInformBlackListMapper extends BaseMapper<AutomaticInformBlackListDO> {

    List<Long> getBlackList(@Param("ruleId") Long ruleId);
}
