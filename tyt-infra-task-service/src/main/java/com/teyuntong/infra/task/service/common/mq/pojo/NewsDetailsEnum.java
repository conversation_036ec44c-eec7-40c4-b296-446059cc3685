package com.teyuntong.infra.task.service.common.mq.pojo;

public enum NewsDetailsEnum {
    text(0, "文本"),
    link(1, "链接"),
    /** @deprecated */
    @Deprecated
    img(1, "图片");

    private Integer code;
    private String zhName;

    private NewsDetailsEnum(Integer code, String zhName) {
        this.code = code;
        this.zhName = zhName;
    }

    public static NewsDetailsEnum getEnum(Integer code) {
        if (code == null) {
            return null;
        } else {
            NewsDetailsEnum[] var1 = values();
            int var2 = var1.length;

            for(int var3 = 0; var3 < var2; ++var3) {
                NewsDetailsEnum statusEnum = var1[var3];
                if (statusEnum.getCode().equals(code)) {
                    return statusEnum;
                }
            }

            return null;
        }
    }

    public static String getEnumName(Integer code) {
        NewsDetailsEnum anEnum = getEnum(code);
        return anEnum != null ? anEnum.getZhName() : "";
    }

    public static NewsDetailsEnum getEnumByZhName(String zhName) {
        if (zhName == null) {
            return null;
        } else {
            NewsDetailsEnum[] var1 = values();
            int var2 = var1.length;

            for(int var3 = 0; var3 < var2; ++var3) {
                NewsDetailsEnum oneEnum = var1[var3];
                if (oneEnum.getZhName().equalsIgnoreCase(zhName)) {
                    return oneEnum;
                }
            }

            return null;
        }
    }

    public Integer getCode() {
        return this.code;
    }

    public String getZhName() {
        return this.zhName;
    }
}
