package com.teyuntong.infra.task.service.biz.customerservice.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 客服投诉记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-09-29
 */
@Mapper
public interface CsComplaintRecordMapper extends BaseMapper<CsComplaintRecordMapper> {
    /**
     * 获取需要分配的投诉工单
     *
     * @return List<CsComplaintRecord>
     */
    List<Long> getNewWorkOrderIds();

    /**
     * 获取处理人未完成的单量
     *
     * @param userId 处理人id
     * @return Integer
     */
    Integer getCountByOpUser(@Param("userId") Long userId);

    /**
     * 分配处理人
     *
     * @param workOrderId 工单id
     * @param opUserId 处理人id
     * @param opUserName 处理人姓名
     */
    void updateOpUser(@Param("workOrderId")Long workOrderId, @Param("opUserId")Long opUserId,@Param("opUserName") String opUserName);
}
