package com.teyuntong.infra.task.service.remote.commonapi.service;


import com.teyuntong.inner.export.service.client.common.bean.ResultMsgBean;
import com.teyuntong.inner.export.service.client.commonapi.service.GaoDeMapRpcService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;


@Service
@FeignClient(name = "tyt-inner-export-service", path = "inner-export", contextId = "gaoDeMapRpcService", fallbackFactory = GaoDeMapRemoteService.GaoDeMapRemoteServiceFallbackFactory.class)
public interface GaoDeMapRemoteService extends GaoDeMapRpcService {


    @Slf4j
    @Component
    class GaoDeMapRemoteServiceFallbackFactory implements FallbackFactory<GaoDeMapRemoteService> {
        @Override
        public GaoDeMapRemoteService create(Throwable cause) {
            return (longitude, latitude) -> {
                log.error(cause.getMessage());
                return ResultMsgBean.error();
            };
        }
    }
}
