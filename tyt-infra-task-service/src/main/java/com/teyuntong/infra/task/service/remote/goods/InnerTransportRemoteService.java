package com.teyuntong.infra.task.service.remote.goods;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.inner.export.service.client.plat.transport.service.TransportRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;

/**
 * 通过inner调用plat
 *
 * <AUTHOR>
 * @since 2024-11-16 16:51
 */
@Service
@FeignClient(name = "tyt-inner-export-service", path = "inner-export", contextId = "TransportRpcService", fallbackFactory = InnerTransportRemoteService.InnerTransportFallbackFactory.class)
public interface InnerTransportRemoteService extends TransportRpcService {
    class InnerTransportFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<InnerTransportRemoteService> {
        protected InnerTransportFallbackFactory() {
            super(true, InnerTransportRemoteService.class);
        }
    }
}
