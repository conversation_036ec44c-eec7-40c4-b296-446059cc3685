package com.teyuntong.infra.task.service.biz.dispatch.enums;

/**
 * 接单状态枚举类
 *
 * <AUTHOR>
 * @since 2024/06/13 14:20
 */
public enum AcceptStatusEnum {

    NOT_ACCEPT(0,"未接单"),
    ACCEPT(1,"已接单");

    private int code;

    private final String desc;

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    AcceptStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
