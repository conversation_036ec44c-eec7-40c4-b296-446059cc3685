package com.teyuntong.infra.task.service.common.constant;

/**
 * plat 调用时需要的常量
 * 该类只在对接plat 等老项目时使用
 *
 * <AUTHOR>
 * @date 2022/11/12 10:37
 */
public class TytDepConstant {

    //接口签名秘钥
    public static final String sign_private_key = "1345~opo-4%";
    //aes 加/解密key
    public static final String aes_Key = "a3+@$@$!~!#22244";


    public static final String CACHE_PERSONAL_GOODS_TICKET_KEY = "login:clientSign:user:personal:goods:";

    /**
     * 货源hashcode缓存有效期
     */
    public static final long CACHE_EXPIRE_TIME_24H = 86400;
    /*------------------------数字---------------------------*/
    /**
     * 车辆定位信息缓存时间s
     */
    public static final int CACHE_EXPIRE_TIME_5MIN = 300;



    /*------------------------字符---------------------------*/
    /**
     * 缓存车辆当前位置信息
     */
    public static final String CACHE_CAR_CURRENT_LOCATION = "car:current:location:";
    public static final String SYNC_CAR_BI_HTTP_BASE_KEY = "sync.car.bi.http.base";
    public static final String SYNC_CAR_BI_PRIVATEKEY_KEY = "sync.car.bi.privatekey";


}
