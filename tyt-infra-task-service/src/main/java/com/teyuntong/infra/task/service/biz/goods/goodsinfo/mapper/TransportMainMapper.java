package com.teyuntong.infra.task.service.biz.goods.goodsinfo.mapper;


import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.bean.TransportResultVO;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.bean.UserOrderStatisticsModel;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.bean.UserTransportCountBean;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.entity.TransportCreditRetopVo;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.entity.TytTransportMain;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 运输信息表主表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-29
 */
@Mapper
public interface TransportMainMapper extends BaseMapper<TytTransportMain> {
    /**
     * 根据时间查询
     *
     * @param startDate
     * @param endDate
     * @return
     */
    List<TytTransportMain> selectByCtime(@Param("startDate") DateTime startDate, @Param("endDate") DateTime endDate, @Param("lastSrcMagId") long lastSrcMagId, @Param("pageSize") Integer pageSize);

    Integer selectCountByCtime(@Param("startDate") DateTime startDate, @Param("endDate") DateTime endDate);

    TytTransportMain selectTransportMainById( @Param("tsId") long tsId);


    void updateTecServiceFeeBySrcMsgId(@Param("srcMsgId") Long srcMsgId, @Param("tecServiceFee") BigDecimal noMemberAfterFee);

    List<Long> getAllTransportSrcMsgIdByDateParam(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    void saveViewCount(@Param("srcMsgId") Long srcMsgId, @Param("count") Integer count);

    List<TytTransportMain> selectNotDealByDateRange(@Param("start") Date start,
                                                    @Param("end") Date end,
                                                    @Param("startIndex") int startIndex,
                                                    @Param("pageSize") int pageSize);

    List<TytTransportMain> selectNotDealByDateRangeV2(@Param("start") Date start,
                                                      @Param("end") Date end,
                                                      @Param("startIndex") int startIndex,
                                                      @Param("pageSize") int pageSize,
                                                      @Param("distanceStart") int distanceStart,
                                                      @Param("distanceEnd") int distanceEnd);
    Long selectUserFirstTransportId(@Param("userId") Long userId);
    List<TransportCreditRetopVo> selectOfCreditRetop(@Param("beginTime") Date beginTime, @Param("endTime") Date endTime);

    List<TransportResultVO> selectUserCtimes(@Param("startTime") Date startTime, @Param("endTime") Date endTime);


    List<UserOrderStatisticsModel> listLastDayPublishNo(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<UserOrderStatisticsModel> listLastDayGoodsNo(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<Long> selectUserFirstThreeExcellentGoods(@Param("userId") Long userId);

    List<TytTransportMain> selectExcellentGoodsByTimeRange(@Param("lastMaxId") Long lastMaxId,
                                                           @Param("start") Date start,
                                                           @Param("end") Date end,
                                                           @Param("pageSize") Integer pageSize);

    void updateExcellentGoodsTwo(@Param("needProcessIds") List<Long> needProcessIds);

    List<Long> selectPublishingTransports(@Param("srcMsgIdList") List<Long> srcMsgIdList);

    List<Long> selectBySimilarityCode(@Param("similarityCode") String similarityCode,
                                      @Param("srcMsgId") Long srcMsgId);

    List<TytTransportMain> queryPublishingTransport(@Param("startIndex") Integer startIndex,
                                        @Param("pageSize") Integer pageSize,
                                        @Param("start") Date start,
                                        @Param("end") Date end);

    List<TytTransportMain> queryNeedSystemQuotedTransports(@Param("lastId") Long lastId,
                                                           @Param("startDate") Date startDate,
                                                           @Param("pageSize") Integer pageSize);

    Long selectTodayMinTransportMainId();

    List<TytTransportMain> selectCanDispatchNonSpecialCarList(@Param("startCity") String startCity,
                                                              @Param("destCity") String destCity,
                                                              @Param("startTime") Date startTime,
                                                              @Param("start") Integer start,
                                                              @Param("pageSize") Integer pageSize);

    Date getUserLastPublishTime(@Param("userId") Long userId, @Param("startTime") Date startTime);

    Date getEarliestGoodCarPriceTransportTime(@Param("userId") Long userId);

    // 查询一段时间内相似货源数>1的货主id
    List<String> getUserIdIfSimilarityCodeGreaterThanOne(@Param("startTime") Date startTime,
                                                       @Param("endTime") Date endTime);

    // 统计用户成交率
    UserTransportCountBean countUserDealRate(@Param("userId") Long userId, @Param("startTime") Date startTime,
                                             @Param("endTime") Date endTime);

    // 返回未成交的秒抢货源id
    List<Long> getValidSeckillGoodsId(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    List<TytTransportMain> selectCanDispatchNonSpecialCarList2(@Param("cityList") List<String> cityList,
                                                               @Param("startTime") Date startTime,
                                                               @Param("start") Integer start,
                                                               @Param("pageSize") Integer pageSize);

}
