package com.teyuntong.infra.task.service.common.mq.pojo;

public enum NotifyOpenTypeEnum {

    app(0, "打开应用"),
    link(1, "打开连接");

    private Integer code;
    private String zhName;

    private NotifyOpenTypeEnum(Integer code, String zhName) {
        this.code = code;
        this.zhName = zhName;
    }

    public static NotifyOpenTypeEnum getEnum(Integer code) {
        if (code == null) {
            return null;
        } else {
            NotifyOpenTypeEnum[] var1 = values();
            int var2 = var1.length;

            for(int var3 = 0; var3 < var2; ++var3) {
                NotifyOpenTypeEnum statusEnum = var1[var3];
                if (statusEnum.getCode().equals(code)) {
                    return statusEnum;
                }
            }

            return null;
        }
    }

    public static String getEnumName(Integer code) {
        NotifyOpenTypeEnum anEnum = getEnum(code);
        return anEnum != null ? anEnum.getZhName() : "";
    }

    public static NotifyOpenTypeEnum getEnumByZhName(String zhName) {
        if (zhName == null) {
            return null;
        } else {
            NotifyOpenTypeEnum[] var1 = values();
            int var2 = var1.length;

            for(int var3 = 0; var3 < var2; ++var3) {
                NotifyOpenTypeEnum oneEnum = var1[var3];
                if (oneEnum.getZhName().equalsIgnoreCase(zhName)) {
                    return oneEnum;
                }
            }

            return null;
        }
    }

    public Integer getCode() {
        return this.code;
    }

    public String getZhName() {
        return this.zhName;
    }
}
