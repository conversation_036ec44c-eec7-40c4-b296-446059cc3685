package com.teyuntong.infra.task.service.common.mq.pojo;

public enum PushMsgTypeEnum {

    bill(1, "账单消息"),
    sys(2, "系统消息"),
    operate(3, "运营消息"),
    other(4, "其他消息");

    private Integer code;
    private String zhName;

    private PushMsgTypeEnum(Integer code, String zhName) {
        this.code = code;
        this.zhName = zhName;
    }

    public static PushMsgTypeEnum getEnum(Integer code) {
        if (code == null) {
            return null;
        } else {
            PushMsgTypeEnum[] var1 = values();
            int var2 = var1.length;

            for(int var3 = 0; var3 < var2; ++var3) {
                PushMsgTypeEnum statusEnum = var1[var3];
                if (statusEnum.getCode().equals(code)) {
                    return statusEnum;
                }
            }

            return null;
        }
    }

    public static String getEnumName(Integer code) {
        PushMsgTypeEnum anEnum = getEnum(code);
        return anEnum != null ? anEnum.getZhName() : "";
    }

    public static PushMsgTypeEnum getEnumByZhName(String zhName) {
        if (zhName == null) {
            return null;
        } else {
            PushMsgTypeEnum[] var1 = values();
            int var2 = var1.length;

            for(int var3 = 0; var3 < var2; ++var3) {
                PushMsgTypeEnum oneEnum = var1[var3];
                if (oneEnum.getZhName().equalsIgnoreCase(zhName)) {
                    return oneEnum;
                }
            }

            return null;
        }
    }

    public Integer getCode() {
        return this.code;
    }

    public String getZhName() {
        return this.zhName;
    }
}
