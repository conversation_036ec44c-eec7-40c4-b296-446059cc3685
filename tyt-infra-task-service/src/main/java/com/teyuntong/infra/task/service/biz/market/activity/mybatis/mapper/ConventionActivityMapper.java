package com.teyuntong.infra.task.service.biz.market.activity.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.ConventionActivityDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 履约活动表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-09-30
 */
@Mapper
public interface ConventionActivityMapper extends BaseMapper<ConventionActivityDO> {

    List<ConventionActivityDO> getAwardsUser(@Param("activityId") Long activityId);
    List<ConventionActivityDO> getByActivityId(@Param("activityId") Long activityId);

    ConventionActivityDO selectByUserId(@Param("userId") Long userId, @Param("activityId") Long activityId);

    void updateConvention(@Param("activityId") Long activityId, @Param("orderNum") Integer orderNum,
                          @Param("prize") String prize, @Param("userId") Long userId);

    void updateConventionNum(@Param("activityId") Long activityId, @Param("orderNum") Integer orderNum,
                             @Param("userId") Long userId);
}
