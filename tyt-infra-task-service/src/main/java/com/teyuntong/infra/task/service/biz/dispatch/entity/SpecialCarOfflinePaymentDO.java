package com.teyuntong.infra.task.service.biz.dispatch.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 专车线下付款表
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-06-20
 */
@Getter
@Setter
@TableName("tyt_special_car_offline_payment")
public class SpecialCarOfflinePaymentDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单调度表ID
     */
    private Long dispatchId;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 是否线下支付：1-是、0-否
     */
    private Integer offlinePayment;

    /**
     * 收款人姓名
     */
    private String recipientName;

    /**
     * 银行卡号
     */
    private String bankNo;

    /**
     * 开户行
     */
    private String openBank;

    /**
     * 司机线下付款金额
     */
    private BigDecimal driverPaymentAmount;

    /**
     * 含税线下付款金额
     */
    private BigDecimal taxPaymentAmount;

    /**
     * 导航测距（公里）
     */
    private BigDecimal navigationDistance;

    /**
     * 无法开票原因
     */
    private String unableInvoiceReason;

    /**
     * 付款进度：1-未处理、2-已申请、3-已审批、4-已支付
     */
    private Integer paymentProgress;

    /**
     * 图片URL（多张逗号分割）
     */
    private String picUrl;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除状态：0-否 1-是
     */
    private Integer deleteStatus;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 操作人ID
     */
    private Long operateId;

    /**
     * 操作人姓名
     */
    private String operateName;

    /**
     * 是否发送消息提醒：0-否，1-是
     */
    private Integer sendMessageStatus;
}
