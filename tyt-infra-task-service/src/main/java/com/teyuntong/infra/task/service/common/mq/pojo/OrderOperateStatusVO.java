package com.teyuntong.infra.task.service.common.mq.pojo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *  订单操作状态列表
 */
@Data
public class OrderOperateStatusVO extends MqBaseMessageBean implements Serializable {
    //操作记录id
    private Long orderId;
    private int orderStatus;
    private int costStatus;
    private int operateType;
    private int dataType;
    private Date createTime;
    private int delFlag;
}
