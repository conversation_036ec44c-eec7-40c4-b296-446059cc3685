package com.teyuntong.infra.task.service.biz.market.activity.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.MarketingActivityDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 运营活动表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-22
 */
@Mapper
public interface MarketingActivityMapper extends BaseMapper<MarketingActivityDO> {
//    List<MarketingActivityDO> getEndActivity();

    List<MarketingActivityDO> getByType(@Param("activityType") Integer activityType);

    List<MarketingActivityDO> getEndActivity(@Param("activityType") Integer activityType);
}
