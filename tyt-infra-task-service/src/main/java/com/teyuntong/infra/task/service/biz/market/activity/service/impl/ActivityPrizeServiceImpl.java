package com.teyuntong.infra.task.service.biz.market.activity.service.impl;

import com.teyuntong.infra.task.service.biz.market.activity.dto.ActivityGradeBean;
import com.teyuntong.infra.task.service.biz.market.activity.mybatis.mapper.ActivityPrizeMapper;
import com.teyuntong.infra.task.service.biz.market.activity.service.ActivityPrizeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 活动奖品表 服务实现类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-09-30
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class ActivityPrizeServiceImpl implements ActivityPrizeService {
    @Resource
    private ActivityPrizeMapper activityPrizeMapper;

    @Override
    public Long getByPrize(Integer prizeId) {
        return activityPrizeMapper.getByPrize(prizeId);
    }

    @Override
    public List<ActivityGradeBean> getActivityGradeBeanByActivityId(Long activityId) {
        return activityPrizeMapper.getActivityGradeBeanByActivityId(activityId);
    }
}
