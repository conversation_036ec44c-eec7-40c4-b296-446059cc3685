package com.teyuntong.infra.task.service.biz.dispatch.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 专车派单详情表
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-09-19
 */
@Getter
@Setter
@TableName("tyt_special_car_dispatch_detail")
public class SpecialCarDispatchDetailDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 专车派单id(tyt_special_car_dispatch表id)
     */
    private Long dispatchId;

    /**
     * 货源ID
     */
    private Long tsId;

    /**
     * 车主ID
     */
    private Long userId;

    /**
     * 车主联系电话
     */
    private String payLinkPhone;

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 位置
     */
    private String location;

    /**
     * 距离
     */
    private BigDecimal distance;

    /**
     * tyt_signing_car_info表ID
     */
    private Long carInfoId;

    /**
     * 派单优先级
     */
    private Integer priority;

    /**
     * 派单状态：0.未派单  1.派单成功 2.派单失败
     */
    private Integer dispatchStatus;

    /**
     * 派单失败原因
     */
    private String dispatchErrorMsg;

    /**
     * 派单时间
     */
    private Date dispatchTime;

    /**
     * 接单状态：0.未接单 1.已接单
     */
    private Integer acceptStatus;

    /**
     * 接单时间
     */
    private Date acceptTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 是否人工指派：0-否，1-是
     */
    private Integer manualAssign;

    /**
     * 司机标签：1-兼职运力，2-全职运力
     */
    private Integer driverTag;

    /**
     * 车辆ID
     */
    private Long carId;
}
