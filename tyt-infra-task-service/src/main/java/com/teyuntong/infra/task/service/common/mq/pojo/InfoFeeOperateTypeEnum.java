package com.teyuntong.infra.task.service.common.mq.pojo;

import lombok.Getter;

/**
* 信息费操作类型
* <AUTHOR>
* @since 2024/8/13 18:31
*/
public enum InfoFeeOperateTypeEnum {
    //0:不播报,1:播报
    info_fee_operate_pay(1, "车主支付订金"),
    info_fee_operate_confirm(2, "装货完成,确认订金"),
    info_fee_operate_shipper_refund_apply(3, "(订金类型为不退还)货方发起订金退款"),
    info_fee_operate_carOwner_agree_refund(4, "车方同意退款"),
    info_fee_operate_carOwner_refuse_refund(5, "车主拒绝退款"),
    info_fee_operate_carOwner_freeze(6, "车主发起订金冻结"),
    info_fee_operate_unfreeze(7, "订金解冻"),
    info_fee_operate_carOwner_exception(8, "车主异常上报"),
    info_fee_operate_shipper_exception(9, "货方异常上报"),
    info_fee_operate_auto_confirm(10, "订金自动确认"),
    info_fee_operate_exception_finished(11, "异常处理完成"),
    info_fee_operate_carOwner_delay_confirm(12, "车主发起延迟付款"),
    info_fee_operate_carOwner_shipper_refund(13, "(订金类型为退还)车方订金退还申请/货方全额退回订金"),
    info_fee_operate_shipper_delay_refund(14, "货方发起延迟退款"),
    info_fee_operate_system_auto_refund(15, "系统自动退款"),
    info_fee_operate_save_complaint(16, "发起投诉"),
    info_fee_operate_cancel_exception(17, "撤销异常上报"),
    info_fee_operate_repeat_refund(18, "调车数量为一多个车主支付时系统发起自动退款请求"),
    info_fee_operate_manage_ex_refund(19, "后台管理系统针对交易异常退还订金和技术服务费");

    @Getter
    private Integer code;

    @Getter
    private String zhName;

    private InfoFeeOperateTypeEnum(Integer code, String zhName) {
        this.code = code;
        this.zhName = zhName;
    }

}
