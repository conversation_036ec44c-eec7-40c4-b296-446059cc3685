package com.teyuntong.infra.task.service.common.sequence.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.infra.task.service.common.sequence.entity.SequenceDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 序列表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-10-08
 */
@Mapper
public interface SequenceMapper extends BaseMapper<SequenceDO> {

    void addNumber(@Param("sequenceName") String sequenceName);

    SequenceDO selectByName(@Param("sequenceName") String sequenceName);
}
