package com.teyuntong.infra.task.service.remote.goods;

import com.teyuntong.goods.service.client.transport.service.TransportMainRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;


@Service
@FeignClient(name = "tyt-goods-service", path = "goods", contextId = "transportMainRpcService", fallbackFactory = TytTransportMainRemoteService.TytTransportMainRemoteFallbackFactory.class)
public interface TytTransportMainRemoteService extends TransportMainRpcService {

    @Component
    class TytTransportMainRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<TytTransportMainRemoteService> {
        protected TytTransportMainRemoteFallbackFactory() {
            super(true, TytTransportMainRemoteService.class);
        }
    }
}
