package com.teyuntong.infra.task.service.biz.dispatch.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.infra.task.service.biz.dispatch.entity.SpecialCarDispatchDetailDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 * <p>
 * 专车派单详情表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-09-19
 */
@Mapper
public interface SpecialCarDispatchDetailMapper extends BaseMapper<SpecialCarDispatchDetailDO> {

    SpecialCarDispatchDetailDO getNextDispatchDetail(@Param("dispatchId") Long dispatchId);
}
