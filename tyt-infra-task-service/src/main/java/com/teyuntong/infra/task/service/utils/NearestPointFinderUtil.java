package com.teyuntong.infra.task.service.utils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/03/22 10:09
 */
public class NearestPointFinderUtil {

    /**
     * 区域划分深度最大值
     */
    private static final int SOME_THRESHOLD = 4;

    /**
     * 轨迹点最小值
     */
    private static final int SOME_SMALL_SIZE = 1;

    static class Point {
        double longitude;
        double latitude;

        public Point(double longitude, double latitude) {
            this.longitude = longitude;
            this.latitude = latitude;
        }

        // 计算两点之间的距离（简化的Haversine公式）
        public double distanceTo(Point other) {
            double R = 6371e3; // 地球半径，单位：米
            double dLat = Math.toRadians(other.latitude - this.latitude);
            double dLon = Math.toRadians(other.longitude - this.longitude);
            double a = Math.sin(dLat / 2) * Math.sin(dLat / 2)
                    + Math.cos(Math.toRadians(this.latitude)) * Math.cos(Math.toRadians(other.latitude))
                    * Math.sin(dLon / 2) * Math.sin(dLon / 2);
            double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
            double distance = R * c;
            return distance;
        }
    }

    // 假设我们有一个函数可以计算给定区域内离目标最近的点
    private static Point findNearestInRegion(Point destination, List<Point> regionPoints) {
        Point nearest = null;
        double minDistance = Double.MAX_VALUE;
        for (Point point : regionPoints) {
            double distance = point.distanceTo(destination);
            if (distance < minDistance) {
                minDistance = distance;
                nearest = point;
            }
        }
        return nearest;
    }

    // 分治策略：递归地将点集划分为更小的区域，并在每个区域中找到最近的点
    private static Point findNearestRecursive(Point destination, List<Point> points, int depth) {

        if (points.size() <= 1) {
            return points.isEmpty() ? null : points.get(0);
        }

        // 这里简单地将点集划分为四个区域作为示例，实际应用中可能需要根据经纬度范围更细致地划分
        List<List<Point>> regions = new ArrayList<>();
        int halfSize = points.size() / 2;
        regions.add(points.subList(0, halfSize)); // 左上区域
        regions.add(points.subList(halfSize, points.size())); // 右下区域
        // ... 可以添加更多区域划分逻辑 ...

        Point nearest = null;
        double minDistance = Double.MAX_VALUE;
        for (List<Point> region : regions) {
            Point regionNearest = findNearestRecursive(destination, region, depth + 1);
            if (regionNearest != null) {
                double distance = regionNearest.distanceTo(destination);
                if (distance < minDistance) {
                    minDistance = distance;
                    nearest = regionNearest;
                }
            }
        }

        // 如果划分深度足够，或者当前区域足够小，则直接返回最近点
        if (depth >= SOME_THRESHOLD || points.size() <= SOME_SMALL_SIZE) {
            return nearest;
        }

        return nearest;
    }

    public static Point findNearestPoint(Point destination, List<Point> points) {
        return findNearestRecursive(destination, points, 0);
    }

    public static void main(String[] args) {
        // 创建一些经纬度点（示例）
        List<Point> points = new ArrayList<>();
        // ... 添加点 ...

        // 目的地经纬度
        Point destination =null;

        // 查找距离目的地最近的点
        Point nearestPoint = findNearestPoint(destination, points);

        // 输出结果
        if (nearestPoint != null) {
            System.out.println("Nearest point to destination: " + nearestPoint.longitude + ", " + nearestPoint.latitude);
        } else {
            System.out.println("No points found.");
        }

    }

}

