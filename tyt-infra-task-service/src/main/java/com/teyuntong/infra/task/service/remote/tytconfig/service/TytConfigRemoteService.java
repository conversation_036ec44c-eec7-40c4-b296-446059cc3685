package com.teyuntong.infra.task.service.remote.tytconfig.service;


import com.teyuntong.inner.export.service.client.tytconfig.dto.ConfigRpcDto;
import com.teyuntong.inner.export.service.client.tytconfig.service.ConfigRpcService;
import com.teyuntong.inner.export.service.client.tytconfig.vo.ConfigRpcVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;


@Service
@FeignClient(name = "tyt-inner-export-service", path = "inner-export", contextId = "configRpcService", fallbackFactory = TytConfigRemoteService.TytConfigRemoteServiceFallbackFactory.class)
public interface TytConfigRemoteService extends ConfigRpcService {


    @Slf4j
    @Component
    class TytConfigRemoteServiceFallbackFactory implements FallbackFactory<TytConfigRemoteService> {
        @Override
        public TytConfigRemoteService create(Throwable cause) {
            return new TytConfigRemoteService() {
                @Override
                public ConfigRpcVo getByName(String s) {
                    log.error(cause.getMessage());
                    return null;
                }

                @Override
                public Integer getIntValue(String s) {
                    log.error(cause.getMessage());
                    return null;
                }

                @Override
                public Integer getIntValue(String s, int i) {
                    log.error(cause.getMessage());
                    return null;
                }

                @Override
                public String getStringValue(String s) {
                    log.error(cause.getMessage());
                    return null;
                }

                @Override
                public String getStringValue(String s, String s1) {
                    log.error(cause.getMessage());
                    return null;
                }

                @Override
                public boolean save(ConfigRpcDto configRpcDto) {
                    log.error(cause.getMessage());
                    return false;
                }

                @Override
                public boolean update(ConfigRpcDto configRpcDto) {
                    log.error(cause.getMessage());
                    return false;
                }

                @Override
                public boolean deleteByName(String s) {
                    log.error(cause.getMessage());
                    return false;
                }
            };
        }
    }
}
