package com.teyuntong.infra.task.service.biz.dispatch.service.impl;

import cn.hutool.core.lang.Opt;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.teyuntong.infra.task.service.biz.dispatch.entity.SpecialCarOfflinePaymentDO;
import com.teyuntong.infra.task.service.biz.dispatch.mapper.SpecialCarOfflinePaymentMapper;
import com.teyuntong.infra.task.service.biz.dispatch.service.SpecialCarOfflinePaymentService;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.entity.TransportDispatchDO;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.mapper.TransportDispatchMapper;
import com.teyuntong.infra.task.service.biz.message.center.entity.EmployeeMessage;
import com.teyuntong.infra.task.service.biz.message.center.mapper.EmployeeMessageMapper;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SpecialCarOfflinePaymentServiceImpl implements SpecialCarOfflinePaymentService {

    @Autowired
    private SpecialCarOfflinePaymentMapper offlinePaymentMapper;

    @Autowired
    private TransportDispatchMapper transportDispatchMapper;

    @Autowired
    private EmployeeMessageMapper employeeMessageMapper;

    @Override
    public void offlinePaymentMessage() {
        String jobParam = XxlJobHelper.getJobParam();
        log.info("线下付款消息提醒请求参数:【{}】", jobParam);

        Integer overHour = 48;
        if (StringUtils.isNotBlank(jobParam)) {
            overHour = Integer.valueOf(jobParam);
        }

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.HOUR_OF_DAY, -overHour);
        Date hourAgo = calendar.getTime();
        LambdaQueryWrapper<SpecialCarOfflinePaymentDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpecialCarOfflinePaymentDO::getOfflinePayment, 1);
        queryWrapper.eq(SpecialCarOfflinePaymentDO::getPaymentProgress, 1);
        queryWrapper.eq(SpecialCarOfflinePaymentDO::getSendMessageStatus, 0);
        queryWrapper.le(SpecialCarOfflinePaymentDO::getUpdateTime, hourAgo);

        Long totalNum = offlinePaymentMapper.selectCount(queryWrapper);
        log.info("线下付款消息提醒消息数量待生成:【{}】", totalNum);
        if (Objects.isNull(totalNum) || totalNum <= 0) {
            return;
        }

        while (Objects.nonNull(totalNum) && totalNum > 0) {
            log.info("线下付款消息提醒消息数量已生成:【{}】", totalNum);
            Page<SpecialCarOfflinePaymentDO> page = new Page<>(1, 1000);
            IPage<SpecialCarOfflinePaymentDO> offlinePaymentPage = offlinePaymentMapper.selectPage(page, queryWrapper);
            List<SpecialCarOfflinePaymentDO> offlinePaymentDOList = offlinePaymentPage.getRecords();

            if (CollectionUtils.isEmpty(offlinePaymentDOList)) {
                break;
            }

            // 查询调度人
            List<Long> idList = offlinePaymentDOList.stream().map(SpecialCarOfflinePaymentDO::getId).toList();
            List<Long> dispatchIdList = offlinePaymentDOList.stream().map(SpecialCarOfflinePaymentDO::getDispatchId).toList();
            List<TransportDispatchDO> transportDispatchDOList = transportDispatchMapper.selectBatchIds(dispatchIdList);
            Map<Long, TransportDispatchDO> transportDispatchDOMap = Opt.ofEmptyAble(transportDispatchDOList).stream().flatMap(List::stream).collect(Collectors.toMap(TransportDispatchDO::getId, Function.identity()));

            List<EmployeeMessage> messageList = Lists.newArrayList();
            for (SpecialCarOfflinePaymentDO offlinePaymentDO : offlinePaymentDOList) {
                EmployeeMessage message = new EmployeeMessage();
                message.setTitle("线下支付未申请提醒");
                message.setPushUserId(0L);
                message.setPushUserName("系统");
                message.setCtime(new Date());
                message.setOperationTime(new Date());
                message.setType(4);

                TransportDispatchDO transportDispatchDO = transportDispatchDOMap.get(offlinePaymentDO.getDispatchId());
                if (Objects.nonNull(transportDispatchDO)) {
                    message.setContent(String.format("货源%s需要进行线下支付申请，请尽快操作", transportDispatchDO.getSrcMsgId()));
                    message.setUserId(transportDispatchDO.getDispatcherId());
                    message.setUserName(transportDispatchDO.getDispatcherName());
                }
                messageList.add(message);
            }

            // 发送消息
            if (CollectionUtils.isNotEmpty(messageList)) {
                employeeMessageMapper.batchInsert(messageList);
            }

            // 更新状态
            if (CollectionUtils.isNotEmpty(idList)) {
                offlinePaymentMapper.batchUpdateStatus(idList, 1);
            }

            totalNum = offlinePaymentPage.getTotal();
        }
    }
}
