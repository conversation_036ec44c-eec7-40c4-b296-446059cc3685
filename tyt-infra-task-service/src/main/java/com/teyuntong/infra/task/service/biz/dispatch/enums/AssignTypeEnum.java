package com.teyuntong.infra.task.service.biz.dispatch.enums;

import lombok.Getter;

/**
 * 指派类型枚举：1-专车指派，2-好差货指派
 *
 * <AUTHOR>
 * @since 2025-01-11 15:04
 */
@Getter
public enum AssignTypeEnum {
    SPECIAL_CAR_ASSIGN(1, "专车指派"),
    GOOD_POOR_GOODS_ASSIGN(2, "好差货指派")
    ;
    private Integer code;
    private String name;
    AssignTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
