package com.teyuntong.infra.task.service.remote.trade.service;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.trade.service.client.hbwj.service.HBWJRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024/07/26 13:41
 */
@FeignClient(name = "tyt-trade-service", contextId = "hBWJRemoteService", path = "trade", fallbackFactory = HBWJRemoteService.HBWJRemoteServiceFallbackFactory.class)
public interface HBWJRemoteService extends HBWJRpcService {

    @Component
    class HBWJRemoteServiceFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<HBWJRemoteService> {
        protected HBWJRemoteServiceFallbackFactory() {
            super(true, HBWJRemoteService.class);
        }
    }
}
