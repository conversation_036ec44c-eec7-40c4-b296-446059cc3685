package com.teyuntong.infra.task.service.remote.goods;

import com.teyuntong.goods.service.client.publish.service.TransportDirectPublishRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;


@Service
@FeignClient(name = "tyt-goods-service", path = "goods", contextId = "transportDirectPublishRemoteService",
        fallbackFactory = TransportDirectPublishRemoteService.TytTransportMainRemoteFallbackFactory.class)
public interface TransportDirectPublishRemoteService extends TransportDirectPublishRpcService {

    @Component
    class TytTransportMainRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<TransportDirectPublishRemoteService> {
        protected TytTransportMainRemoteFallbackFactory() {
            super(true, TransportDirectPublishRemoteService.class);
        }
    }
}
