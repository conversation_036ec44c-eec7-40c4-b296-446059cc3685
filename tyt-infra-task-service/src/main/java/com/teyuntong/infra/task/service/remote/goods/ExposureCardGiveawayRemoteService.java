package com.teyuntong.infra.task.service.remote.goods;

import com.teyuntong.goods.service.client.goodsrecord.service.ExposureCardGiveawayRpcService;
import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;

/**
 * 查询曝光卡发放
 *
 * <AUTHOR>
 * @since 2024-11-16 16:51
 */
@Service
@FeignClient(name = "tyt-goods-service", path = "goods", contextId = "exposureCardGiveawayRemoteService",
        fallbackFactory = ExposureCardGiveawayRemoteService.ExposureCardGiveawayFallbackFactory.class)
public interface ExposureCardGiveawayRemoteService extends ExposureCardGiveawayRpcService {
    class ExposureCardGiveawayFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<ExposureCardGiveawayRemoteService> {
        protected ExposureCardGiveawayFallbackFactory() {
            super(true, ExposureCardGiveawayRemoteService.class);
        }
    }
}
