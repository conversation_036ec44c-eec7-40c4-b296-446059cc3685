package com.teyuntong.infra.task.service.biz.message.center.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.teyuntong.infra.common.message.bean.*;
import com.teyuntong.infra.common.message.config.MessageCenterProperties;
import com.teyuntong.infra.common.message.service.MessageCenterService;
import com.teyuntong.infra.common.redis.utils.RedisUtil;
import com.teyuntong.infra.common.rocketmq.core.RocketMqProducer;
import com.teyuntong.infra.common.rocketmq.message.MqMessage;
import com.teyuntong.infra.common.rocketmq.message.MqMessageFactory;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.entity.TytTransportMain;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.service.TransportMainService;
import com.teyuntong.infra.task.service.biz.message.center.entity.MessageCenterMqDeliverFailDO;
import com.teyuntong.infra.task.service.biz.message.center.enums.DeliverStatusEnum;
import com.teyuntong.infra.task.service.biz.message.center.mapper.MessageCenterMapper;
import com.teyuntong.infra.task.service.biz.message.center.service.MessageService;
import com.teyuntong.infra.task.service.biz.user.abtest.service.AbtestConfigService;
import com.teyuntong.infra.task.service.biz.user.info.mybatis.entity.User;
import com.teyuntong.infra.task.service.biz.user.info.mybatis.entity.UserCallPhoneRecordDO;
import com.teyuntong.infra.task.service.biz.user.info.mybatis.mapper.UserCallPhoneRecordMapper;
import com.teyuntong.infra.task.service.biz.user.info.service.UserService;
import com.teyuntong.infra.task.service.common.mq.pojo.NotifyOpenTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.UUID;


@Service("messageService")
@Slf4j
public class MessageServiceImpl implements MessageService {


    @Autowired
    private MessageCenterMapper messageCenterMapper;

    @Autowired
    private MessageCenterProperties messageCenterProperties;

    @Autowired
    private RocketMqProducer producer;
    @Autowired
    private MqMessageFactory mqMessageFactory;

    @Autowired
    private MessageCenterService messageCenterService;

    @Autowired
    private UserCallPhoneRecordMapper userCallPhoneRecordMapper;

    @Autowired
    private TransportMainService transportMainService;

    @Autowired
    private AbtestConfigService abtestConfigService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private UserService userService;

    private static final Integer MAX_SEND_NBR = 3;

    @Override
    public void handleDeliverFail() {
        log.info("消息中心投递失败消息，定时任务处理开始");
        List<MessageCenterMqDeliverFailDO> list = messageCenterMapper.selectDeliverFail();
        if (CollectionUtil.isNotEmpty(list)) {
            for (MessageCenterMqDeliverFailDO message : list) {
                log.info("消息中心投递失败消息，消息内容：{}", message);
                try {
                    if (message.getSendNbr().intValue() < MAX_SEND_NBR) {
                        String messageContent = message.getMessageContent();
                        MessageCenterData messageCenterData = JSONUtil.toBean(messageContent, MessageCenterData.class);
                        //重新投递消息
                        MqMessage mqMessage = mqMessageFactory.create(messageCenterProperties.getMqTopic(), messageCenterProperties.getMqTag(), message.getMessageSerialNum(), messageCenterData);
                        producer.sendNormal(mqMessage);
                        //更新投递状态
                        updateDeliver(message, DeliverStatusEnum.FINISHED);
                    } else {
                        updateDeliver(message, DeliverStatusEnum.DEAD);
                    }
                } catch (Exception e) {
                    if (message.getSendNbr().intValue() < MAX_SEND_NBR) {
                        updateDeliver(message, DeliverStatusEnum.UNDO);
                    } else {
                        updateDeliver(message, DeliverStatusEnum.FAILED);
                    }

                }
            }
        }
        log.info("消息中心投递失败消息，定时任务处理结束");
    }

    private void updateDeliver(MessageCenterMqDeliverFailDO message, DeliverStatusEnum deliverStatusEnum) {
        MessageCenterMqDeliverFailDO update = new MessageCenterMqDeliverFailDO();
        update.setId(message.getId());
        update.setDeliverStatus(deliverStatusEnum.getCode());
        update.setSendNbr(message.getSendNbr() + 1);
        messageCenterMapper.updateDeliverFail(update);
    }

    @Override
    public void handleDeliverFailTest() throws InterruptedException {

        NewsMessagePush newsMessagePush = messageCenterService.createNewsMessageForAllPush("NewsMessage测试消息", "NewsMessage测试消息内容", "NewsMessage测试消息标题", 586762L);
        NotifyMessagePush notifyMessagePush = messageCenterService.createNotifyMessageForAllPush("NotifyMessage测试消息", "NotifyMessage测试消息内容", 586762L, NotifyOpenTypeEnum.app.getCode().toString(), "");

        ShortMessageBean shortMessageBean = new ShortMessageBean();
        shortMessageBean.setCellPhone("18612411401");
        shortMessageBean.setContent("测试短信发送");

        messageCenterService.sendMultiMessage(shortMessageBean, newsMessagePush, notifyMessagePush);

        log.info("handleDeliverFailTest信息参数1={},{},{}", newsMessagePush, notifyMessagePush, shortMessageBean);

        Thread.sleep(3000);

        String callbackUrl = messageCenterProperties.getCallbackUrl();
        if (StringUtils.isNotBlank(callbackUrl)) {
            String messageSerialNum = UUID.randomUUID().toString() + messageCenterProperties.getSuffix();
            MessageCenterData centerData = new MessageCenterData(messageCenterProperties.getSuffix(), shortMessageBean, newsMessagePush, notifyMessagePush);
            centerData.setVersion("2.0");
            MqMessageCenterBean mqMessageCenterBean = new MqMessageCenterBean();
            mqMessageCenterBean.setMessageSerialNum(messageSerialNum);
            mqMessageCenterBean.setMessageContent(JSONUtil.toJsonStr(centerData));

            HttpUtil.post(callbackUrl, JSONUtil.toJsonStr(mqMessageCenterBean));
            log.info("handleDeliverFailTest信息参数2={},{}", messageCenterProperties, centerData);
        }
    }

    @Override
    public void noOrderTransportRiskPush() {
        // 获取当前时间
        Date now = new Date();
        // 定义日期格式化为 "yyyy-MM-dd" 的格式
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        // 格式化当前时间为年月日字符串
        String formattedDateNow = dateFormat.format(now);

        // 计算start时间：当前时间往前推40分钟
        Calendar startCal = Calendar.getInstance();
        startCal.setTime(now);
        startCal.add(Calendar.MINUTE, -40);
        Date start = startCal.getTime();

        // 计算end时间：当前时间往前推30分钟
        Calendar endCal = Calendar.getInstance();
        endCal.setTime(now);
        endCal.add(Calendar.MINUTE, -30);
        Date end = endCal.getTime();

        List<UserCallPhoneRecordDO> userCallRecordsByTime = userCallPhoneRecordMapper.getUserCallRecordsByTime(start, end);
        if (userCallRecordsByTime.isEmpty()) {
            log.info("noOrderTransportRiskPush 开始时间：{}, 结束时间{} 未查询到通话记录", start, end);
        }

        for (UserCallPhoneRecordDO userCallPhoneRecordDO : userCallRecordsByTime) {
            if (userCallPhoneRecordDO == null || userCallPhoneRecordDO.getUserId() == null || userCallPhoneRecordDO.getTsId() == null) {
                continue;
            }
            TytTransportMain transportMainForId = transportMainService.getTransportMainForId(userCallPhoneRecordDO.getTsId());
            if (transportMainForId == null
                    || transportMainForId.getExcellentGoods() == null || transportMainForId.getExcellentGoods() != 1
                    || transportMainForId.getPublishType() == null || transportMainForId.getPublishType() != 1
                    || transportMainForId.getInfoStatus() == null || !transportMainForId.getInfoStatus().equals("0")
                    || transportMainForId.getUserId() == null ) {
                //非优车电议未成交货源
                continue;
            }

            String todayCountKey = "no_order_risk_push:today_count:" + formattedDateNow + ":" + transportMainForId.getUserId();
            String LastPushKey = "no_order_risk_push:last_push_time:" + formattedDateNow + ":" + transportMainForId.getUserId();

            boolean needPush = false;
            Integer todayCountTimes = redisUtil.getInt(todayCountKey);
            if (todayCountTimes == null || todayCountTimes <= 0) {
                //今天没push过
                redisUtil.set(todayCountKey, 1, Duration.ofDays(1));
                redisUtil.set(LastPushKey, 1, Duration.ofHours(2));
                needPush = true;
            } else if (todayCountTimes < 3) {
                //今天push过，但是已push总次数小于3次
                Integer lastPush = redisUtil.getInt(LastPushKey);
                if (lastPush == null) {
                    //近两小时内没push过
                    redisUtil.increment(todayCountKey, 1);
                    redisUtil.set(LastPushKey, 1, Duration.ofHours(2));
                    needPush = true;
                }
            }

            if (needPush) {
                Integer noOrderRiskPush = abtestConfigService.getUserType("no_order_risk_push_abtest", transportMainForId.getUserId());
                NotifyMessagePush notifyMessagePush = null;
                ShortMessageBean shortMessageBean = null;
                if (noOrderRiskPush == null || noOrderRiskPush == 0) {
                    //使用非实锤飞单用户规则
                    notifyMessagePush = messageCenterService.createNotifyMessageForGoodsPush("线上成交有保障，赠送优车电议发货次数", "线上成交有保障，赠送优车电议发货次数", transportMainForId.getUserId(), NotifyOpenTypeEnum.app.getCode().toString(), "");
                } else {
                    //使用实锤飞单用户规则
                    notifyMessagePush = messageCenterService.createNotifyMessageForGoodsPush("线下成交没有保障，并可能会关闭优车电议货源功能，请谨慎操作", "线下成交没有保障，并可能会关闭优车电议货源功能，请谨慎操作", transportMainForId.getUserId(), NotifyOpenTypeEnum.app.getCode().toString(), "");

                    User user = userService.getByUserId(transportMainForId.getUserId());
                    String cellPhone = null;
                    if (user != null && StringUtils.isNoneBlank(user.getCellPhone())) {
                        cellPhone = user.getCellPhone();
                    }
                    if (cellPhone != null) {
                        shortMessageBean = new ShortMessageBean();
                        shortMessageBean.setCellPhone(cellPhone);
                        shortMessageBean.setContent("线下成交没有保障，并可能会关闭优车电议货源功能，请谨慎操作");
                    }
                }
                messageCenterService.sendMultiMessage(shortMessageBean, null, notifyMessagePush);
            }

        }

    }

}
