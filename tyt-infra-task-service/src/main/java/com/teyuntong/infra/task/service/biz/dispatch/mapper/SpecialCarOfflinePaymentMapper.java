package com.teyuntong.infra.task.service.biz.dispatch.mapper;

import com.teyuntong.infra.task.service.biz.dispatch.entity.SpecialCarOfflinePaymentDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 专车线下付款表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-06-19
 */
@Mapper
public interface SpecialCarOfflinePaymentMapper extends BaseMapper<SpecialCarOfflinePaymentDO> {

    void batchUpdateStatus(@Param("idList") List<Long> idList, @Param("status") Integer status);

}
