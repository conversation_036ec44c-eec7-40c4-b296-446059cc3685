package com.teyuntong.infra.task.service.common.oss;

import cn.hutool.core.util.RandomUtil;
import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.common.auth.CredentialsProvider;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import com.aliyun.oss.common.comm.SignVersion;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.ObjectMetadata;
import com.teyuntong.infra.task.service.common.config.AliOSSProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.*;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.StringJoiner;
import com.teyuntong.infra.task.service.common.enums.FileTypeEnum;

/**
 * oss上传工具类
 *
 * <AUTHOR>
 * @since 2024/08/14 16:53
 */
@Slf4j
@Component
public class AliOSSClient {

    @Autowired
    private AliOSSProperties aliOssProperties;

    @Value("${spring.profiles.active}")
    private String activeProfile;

    private OSS ossClient;

    @PostConstruct
    private void init() {
        String endpoint = aliOssProperties.getEndpoint();
        String accessKeyId = aliOssProperties.getAccessKeyId();
        String accessKeySecret = aliOssProperties.getAccessKeySecret();
        String region = aliOssProperties.getRegion();


        CredentialsProvider credentialProvider = new DefaultCredentialProvider(accessKeyId, accessKeySecret);
        ClientBuilderConfiguration clientBuilderConfiguration = new ClientBuilderConfiguration();

        clientBuilderConfiguration.setSignatureVersion(SignVersion.V4);
        ossClient = OSSClientBuilder.create()
                .endpoint(endpoint)
                .credentialsProvider(credentialProvider)
                .clientConfiguration(clientBuilderConfiguration)
                .region(region)
                .build();
    }

    /**
     * 返回oss客户端
     */
    public OSS getOssClient() {
        if (ossClient == null) {
            init();
        }
        return ossClient;
    }


    @PreDestroy
    private void close() {
        if (ossClient != null) {
            ossClient.shutdown();
        }
    }

    /**
     * 文件上传，该方法会覆盖原始文件，注意 finalKey 唯一性.
     *
     * @param input    文件流
     * @param fileKey  文件路径及名称
     * @param download 是否直接下载
     * @return String 下载链接
     */
    public String upload(InputStream input, String fileKey, boolean download) {

        String contentType = FileTypeEnum.getContentType(fileKey);

        String fileName = new File(fileKey).getName();
        ObjectMetadata objectMetadata = new ObjectMetadata();
        objectMetadata.setContentType(contentType);
        if (download) {
            //可以直接下载的
            objectMetadata.setContentDisposition("attachment;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
        } else {
            //直接浏览器打开的
            objectMetadata.setContentDisposition("inline;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
        }

        try {
            getOssClient().putObject(aliOssProperties.getBucketName(), fileKey, input, objectMetadata);
        } catch (Exception e) {
            log.info("上传oss失败，", e);
            return null;
        }

        return aliOssProperties.getDomain() + fileKey;
    }

    /**
     * 文件上传，该方法会覆盖原始文件，注意 finalKey 唯一性
     *
     * @param dataByte 文件byte
     * @param fileKey  文件路径及名称
     * @param download 是否直接下载
     * @return String 下载链接
     */
    public String upload(byte[] dataByte, String fileKey, boolean download) {
        String result = null;

        try (InputStream input = new ByteArrayInputStream(dataByte)) {
            result = this.upload(input, fileKey, download);
        } catch (IOException e) {
            log.info("上传oss失败，", e);
        }

        return result;
    }


    /**
     * 下载阿里云文件.
     */
    public void downloadOss(String url, OutputStream output) throws IOException {
        URL urlObj = new URL(url);

        OSSObject ossObj = this.ossClient.getObject(urlObj, null);
        try (InputStream input = ossObj.getObjectContent()) {
            IOUtils.copy(input, output);
        }
    }

    /**
     * 下载阿里云文件.
     */
    public byte[] downloadOssBytes(String url) throws IOException {
        byte[] imgBytes = null;
        try (ByteArrayOutputStream output = new ByteArrayOutputStream()) {
            this.downloadOss(url, output);
            imgBytes = output.toByteArray();
        }
        return imgBytes;
    }

    // ======================================================================

    /**
     * 返回上传文件的全路径及文件名，格式: /${env}/${app_name}/${type_name}/${yyyy}/${MM}/${dd}/userId_时间戳_fileSize
     * 如：/pro(dev、test、release)/manageInvoice/company/2024/08/14/16609_1699437786000_34345346.png
     */
    public String getFullFileName(Long userId, String appName, String typeName, String fileName) {
        StringJoiner sj = new StringJoiner("/");
        sj.add(activeProfile);
        sj.add(appName);
        sj.add(typeName);
        sj.add(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd")));
        String newFileName = userId + "_" + System.currentTimeMillis() + "_" + RandomUtil.randomInt(100000, 999999);
        sj.add(newFileName + "." + getFileExtension(fileName));
        return sj.toString();
    }

    /**
     * 获取文件的扩展名
     *
     * @param filename 文件名
     * @return 文件的扩展名
     */
    private String getFileExtension(String filename) {
        Path path = Paths.get(filename);
        String fileName = path.getFileName().toString();
        int dotIndex = fileName.lastIndexOf('.');
        if (dotIndex > 0 && dotIndex < fileName.length() - 1) {
            return fileName.substring(dotIndex + 1);
        }
        return "";
    }

}
