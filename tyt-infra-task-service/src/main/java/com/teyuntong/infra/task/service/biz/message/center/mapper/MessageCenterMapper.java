package com.teyuntong.infra.task.service.biz.message.center.mapper;

import com.teyuntong.infra.task.service.biz.message.center.entity.MessageCenterMqDeliverFailDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 消息中心 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-22
 */
@Mapper
public interface MessageCenterMapper {

    /**
     * 获取投递失败的MQ消息记录
     *
     * @return 列表
     */
    List<MessageCenterMqDeliverFailDO> selectDeliverFail();

    /**
     * 更新投递失败的MQ消息记录
     *
     * @param messageCenterMqDeliverFailDO 投递失败参数
     * @return 更新数量
     */
    int updateDeliverFail(MessageCenterMqDeliverFailDO messageCenterMqDeliverFailDO);

}
