package com.teyuntong.infra.task.service.biz.user.info.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 用户附属表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-11
 */
@Getter
@Setter
@TableName("tyt_user_sub")
public class UserSubDO {

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * user_id
     */
    private Long userId;

    /**
     * 系统实名身份证验证标识0非系统实名 1系统实名 默认是0 如用户不在此表中默认是0
     */
    private Integer verifyFlag;

    /**
     * 发货条数限制类别 0 日 1周  2月 3 年 
     */
    private Integer sendTptType;

    /**
     * 发货条数限制数量 
     */
    private Integer sendTptNumber;

    /**
     * 维护人
     */
    private String maintainMan;

    /**
     * 个推CID
     */
    private String cid;

    /**
     * 客户端标识1PC 2ANDROID 3IOS 4APAD 5IPAD 6WEB
     */
    private Integer clientSign;

    /**
     * 通知栏角标新消息数
     */
    private Integer notifyBadge;

    /**
     * 新消息数
     */
    private Integer newMsgNbr;

    /**
     * 修改时间
     */
    private Date utime;

    /**
     * 大板车身份标签
     */
    private String bcarIdentityLables;

    /**
     * 设备车身份标签
     */
    private String scarIdentityLables;

    /**
     * 审核身份大板车标签
     */
    private String auditBcarIdentityLables;

    /**
     * 审核身份设备标签
     */
    private String auditScarIdentityLables;

    /**
     * 我的货源菜单1-仅有电话车 2-货主仅有APP发货货源 3-全有
     */
    private Integer myGoodsMenu;

    /**
     * 手机设备ID
     */
    private String deviceId;

    /**
     * 钱包密码
     */
    private String pocketPwd;

    /**
     * 钱包密码状态 1：未设置 2: 已设置并启用 3：已设置未启用
     */
    private Integer pocketPwdStatus;

    /**
     * 用户发布货源成交个数
     */
    private Integer dealNum;

    /**
     * 平台货源发布次数
     */
    private Integer publishNum;

    /**
     * 用户分众类型
     */
    private Integer userGroup;

    /**
     * 用户绑定的设备号
     */
    private String bindCliendid;

    /**
     * 1: 未绑定 2：已绑定
     */
    private Integer bindStatus;

    /**
     * 二级权益开始时间
     */
    private Date level2BigingTime;

    /**
     * 车主版极光推送id
     */
    private String carDeviceId;

    /**
     * 货主版极光推送id
     */
    private String goodsDeviceId;

    /**
     * 车主版通知栏角标新消息数
     */
    private Integer carNotifyBadge;

    /**
     * 货主版通知栏角标新消息数
     */
    private Integer goodsNotifyBadge;

    /**
     * 车主版新消息数
     */
    private Integer carNewMsgNbr;

    /**
     * 货主版新消息数
     */
    private Integer goodsNewMsgNbr;

    /**
     * 用户车方违约次数
     */
    private Integer carBreakNum;

    /**
     * 用户货方违约次数
     */
    private Integer goodsBreakNum;
}
