package com.teyuntong.infra.task.service.biz.goods.refresh.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.NamedThreadFactory;
import com.alibaba.fastjson.JSON;
import com.teyuntong.infra.common.redis.utils.RedisUtil;
import com.teyuntong.infra.common.rocketmq.core.RocketMqProducer;
import com.teyuntong.infra.common.rocketmq.message.MqMessage;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.entity.TransportMainExtendDO;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.entity.TytTransport;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.entity.TytTransportMain;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.service.TransportMainService;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.service.TransportService;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.service.TransportVaryService;
import com.teyuntong.infra.task.service.biz.goods.refresh.bean.ResendRouteMessageDTO;
import com.teyuntong.infra.task.service.biz.goods.refresh.constant.TransportRefreshConstant;
import com.teyuntong.infra.task.service.biz.goods.refresh.entity.StickLog;
import com.teyuntong.infra.task.service.biz.goods.refresh.enums.OftenRouteMQTagEnum;
import com.teyuntong.infra.task.service.biz.goods.refresh.service.RefreshStickService;
import com.teyuntong.infra.task.service.biz.goods.refresh.service.StickLogService;
import com.teyuntong.infra.task.service.common.mq.config.MessageTopicProperties;
import com.teyuntong.infra.task.service.utils.TimeUtil;
import com.teyuntong.infra.task.service.utils.TytUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/08/06 13:55
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RefreshStickServiceImpl implements RefreshStickService {

    private final StickLogService stickLogService;
    private final TransportService transportService;
    private final TransportMainService transportMainService;
    private final TransportVaryService transportVaryService;
    private final RocketMqProducer rocketMqProducer;
    private final MessageTopicProperties messageTopicProperties;
    private final RedisUtil redisUtil;
    private final StringRedisTemplate redisTemplate;

    private final ThreadPoolExecutor executor = new ThreadPoolExecutor(4, 10, 30,
            TimeUnit.SECONDS, new LinkedBlockingQueue<>(10000), new NamedThreadFactory("stick-task-", false));

    @Override
    public void process() {
        // 因网络原因，可能重复执行，加一个分布式锁，缓存10s
        Boolean bool = redisUtil.setIfAbsent(TransportRefreshConstant.GOODS_REFRESH_STICK_LOCK, 1, Duration.ofSeconds(30));
        if (!bool) {
            log.info("定时重发--未获取到分布式锁，不执行");
            return;
        }

        List<StickLog> stickLogList = stickLogService.getResendStickLog(TimeUtil.today(), new Date());
        log.info("定时重发--需要重发的数据为【{}】", stickLogList.size());
        if (CollectionUtils.isEmpty(stickLogList)) {
            return;
        }

        List<CompletableFuture<Void>> futures = stickLogList.stream()
                .map(tytStickLog -> CompletableFuture.runAsync(() -> processOne(tytStickLog), executor))
                .toList();

        // 等待所有任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        log.info("定时重发任务结束");
    }

    private void processOne(StickLog tytStickLog) {
        try {
            Long tsId = resendGoods(tytStickLog.getSourceTsId());
            if (tsId != 0L) {
                tytStickLog.setTsId(tsId);
                tytStickLog.setStatus("1");
                tytStickLog.setTopNum(tytStickLog.getTopNum() == null ? 1 : tytStickLog.getTopNum() + 1);
                log.info("定时重发--批次【{}】,货物ID【{}】置顶重发成功", tytStickLog.getBatchNo(), tytStickLog.getSourceTsId());
            } else {
                tytStickLog.setStatus("2");
                log.warn("定时重发--批次【{}】,货物ID【{}】置顶重发失败", tytStickLog.getBatchNo(), tytStickLog.getSourceTsId());
            }
        } catch (Exception e) {
            tytStickLog.setStatus("2");
            log.error("定时重发--批次【{}】,货物ID【{}】置顶重发失败，异常：", tytStickLog.getBatchNo(), tytStickLog.getSourceTsId(), e);
        }
        tytStickLog.setMtime(new Date());
        stickLogService.updateById(tytStickLog);
    }

    /**
     * 货源置顶处理
     *
     * @param oldTsId transport id
     * @return true成功 false失败
     */
    private Long resendGoods(Long oldTsId) {
        TytTransport transport = transportService.getTransportDO(oldTsId);
        if (transport == null || transport.getStatus() != 1) {
            return 0L;
        }
        TytTransportMain transportMain = transportMainService.getById(transport.getSrcMsgId());
        // 无效货源不刷新
        if (transportMain == null || transportMain.getStatus() != 1) {
            return 0L;
        }
        Date now = new Date();
        // 昨天货源
        if (transportMain.getCtime().before(DateUtil.beginOfDay(now))) {
            return 0L;
        }

        // 开始构建新货源信息
        TytTransport newTransport = new TytTransport();
        BeanUtils.copyProperties(transportMain, newTransport);
        /* 修改原信息值生成一条新数据 */
        newTransport.setId(null);
        newTransport.setStatus(1);
        newTransport.setCtime(now);
        newTransport.setMtime(now);
        newTransport.setPubTime(TimeUtil.formatTime(now));
        newTransport.setDisplayType("1"); //默认显示
        newTransport.setIsDisplay(1); //默认显示
        /* 重发次数，为了解决老版PC的序号问题 */
        int resendCounts = (newTransport.getResendCounts() == null ? 0 : newTransport.getResendCounts()) + 1;
        newTransport.setResendCounts(resendCounts);
        if (resendCounts > 0) {
            newTransport.setPcOldContent("[" + resendCounts + "]." + newTransport.getPcOldContent().replaceAll("\\[[0-9]{1,}\\]\\.", ""));
        }
        Long flag = updateDatabase(oldTsId, newTransport);
        if (flag == 0L) {
            return 0L;
        }

        //重新发送常跑路线计算推送
        // 2025.02.14 货源自动刷新不再发送常跑路线mq
        /*try {
            ResendRouteMessageDTO routeMessage = this.createResendRouteMessage(newTransport, oldTsId);
            MqMessage mqMessage = new MqMessage(messageTopicProperties.getOftenRouteTopic(),
                    OftenRouteMQTagEnum.RESEND_ROUTE.getCode(),
                    routeMessage.getId().toString(),
                    JSON.toJSONBytes(routeMessage));
            rocketMqProducer.sendNormal(mqMessage);
        } catch (Exception e) {
            log.error("send_ResendRoute_error : ", e);
        }*/

        // 如果刷新成功，把刷新次数记录到redis
        saveRefreshTimeToRedis(newTransport);

        return newTransport.getId();
    }

    private Long updateDatabase(Long oldTsId, TytTransport newTransport) {
        log.info("transport表旧数据置为无效，货源id：{}", oldTsId);
        TytTransport updateTransport = new TytTransport();
        updateTransport.setId(oldTsId);
        updateTransport.setStatus(0);
        updateTransport.setMtime(new Date());
        boolean update = transportService.updateById(updateTransport);
        if (!update) {
            log.warn("transport表旧数据置为无效失败，货源id：{}", oldTsId);
            return 0L;
        }

        // 信息变化表添加数据
        log.info("信息变化表添加数据，新增transport表，更新transportMain表，货源id：{}", oldTsId);
        transportVaryService.save(oldTsId, 0);

        // 新增transport & transportExtend
        transportService.saveTransportAndExtend(newTransport, transportService.getExtendByTsId(oldTsId));

        // 更新main & mainExtend
        TytTransportMain updateTransportMain = new TytTransportMain();
        updateTransportMain.setId(newTransport.getSrcMsgId());
        updateTransportMain.setResendCounts(newTransport.getResendCounts());
        transportMainService.updateMainAndExtend(updateTransportMain, new TransportMainExtendDO());

        log.info("重发货物ID【{}】成功，新货物ID【{}】：", oldTsId, newTransport.getId());
        return 1L;
    }

    /**
     * 系统自动重发货源重新计算常跑路线
     */
    private ResendRouteMessageDTO createResendRouteMessage(TytTransport newTransport, Long tsId) {
        ResendRouteMessageDTO resendRouteVo = new ResendRouteMessageDTO();
        resendRouteVo.setOldTransportId(tsId);
        resendRouteVo.setId(newTransport.getId());
        resendRouteVo.setTaskContent(newTransport.getTaskContent());
        resendRouteVo.setTel(newTransport.getTel());
        resendRouteVo.setPubTime(newTransport.getPubTime());
        resendRouteVo.setNickName(newTransport.getNickName());
        resendRouteVo.setCtime(newTransport.getCtime());
        resendRouteVo.setSrcMsgId(newTransport.getSrcMsgId());
        resendRouteVo.setSimilarityFirstId(newTransport.getSimilarityFirstId());
        resendRouteVo.setPrice(newTransport.getPrice());
        resendRouteVo.setUserId(newTransport.getUserId());
        resendRouteVo.setPubDate(newTransport.getPubDate());
        resendRouteVo.setStartProvinc(newTransport.getStartProvinc());
        resendRouteVo.setStartCity(newTransport.getStartCity());
        resendRouteVo.setStartArea(newTransport.getStartArea());
        resendRouteVo.setDestProvinc(newTransport.getDestProvinc());
        resendRouteVo.setDestCity(newTransport.getDestCity());
        resendRouteVo.setDestArea(newTransport.getDestArea());
        resendRouteVo.setWeight(newTransport.getWeight());
        return resendRouteVo;
    }

    /**
     * 6710需求，按时间段刷新也有次数上限，需要记录每个小时的刷新次数
     * 货源某个时间段的刷新次数，value = 每小时的刷新次数，逗号隔开
     */
    private void saveRefreshTimeToRedis(TytTransport transport) {
        try {
            String refreshPeriodNumCacheKey = TytUtil.joinRedisKey(TransportRefreshConstant.GOODS_REFRESH_PERIOD_COUNT, transport.getSrcMsgId().toString());
            String refreshPeriodNumCacheValue = redisTemplate.opsForValue().get(refreshPeriodNumCacheKey);
            int hour = DateUtil.hour(transport.getCtime(), true);

            if (refreshPeriodNumCacheValue == null) {
                int[] periodNumArray = new int[24];
                periodNumArray[hour] = 1;
                refreshPeriodNumCacheValue = Arrays.stream(periodNumArray).mapToObj(String::valueOf).collect(Collectors.joining(","));
            } else {
                String[] split = refreshPeriodNumCacheValue.split(",");
                int num = (split[hour] == null ? 0 : Integer.parseInt(split[hour])) + 1;
                split[hour] = String.valueOf(num);
                refreshPeriodNumCacheValue = String.join(",", split);
            }
            // 不能使用redisUtil，因为redisUtil使用JSON序列化器，存的格式会变成："\"0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0\""
            redisTemplate.opsForValue().set(refreshPeriodNumCacheKey, refreshPeriodNumCacheValue, Duration.ofHours(24));
        } catch (Exception e) {
            log.error("saveRefreshTimeToRedis_error : ", e);
        }
    }

}
