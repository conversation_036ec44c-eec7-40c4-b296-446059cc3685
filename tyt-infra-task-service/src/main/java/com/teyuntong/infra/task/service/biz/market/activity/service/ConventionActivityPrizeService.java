package com.teyuntong.infra.task.service.biz.market.activity.service;


import com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.ConventionActivityPrizeDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 履约活动冲单奖品配置 服务类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-11-08
 */
public interface ConventionActivityPrizeService {

    List<ConventionActivityPrizeDO> getByActivityId(@Param("activityId") long activityId,
                                                    @Param("type") int type);
}
