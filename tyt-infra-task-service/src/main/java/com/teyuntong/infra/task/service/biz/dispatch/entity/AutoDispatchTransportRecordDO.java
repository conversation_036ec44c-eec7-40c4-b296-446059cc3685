package com.teyuntong.infra.task.service.biz.dispatch.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 可自动指派货源记录表
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-12-12
 */
@Getter
@Setter
@TableName("tyt_auto_dispatch_transport_record")
public class AutoDispatchTransportRecordDO {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 货源ID
     */
    private Long srcMsgId;

    /**
     * 自动派单配置表ID
     */
    private Long configId;

    /**
     * 派单状态：0-未派单，1-已派单
     */
    private Integer dispatchStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date modifyTime;
}
