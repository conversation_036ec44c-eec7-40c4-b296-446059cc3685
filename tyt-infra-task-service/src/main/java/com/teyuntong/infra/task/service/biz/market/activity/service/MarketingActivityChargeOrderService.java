package com.teyuntong.infra.task.service.biz.market.activity.service;


import com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.MarketingActivityChargeOrderDO;

import java.util.List;

/**
 * <p>
 * 冲单活动阶段时间表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-22
 */
public interface MarketingActivityChargeOrderService {

    List<MarketingActivityChargeOrderDO> getByActivityId(Long activityId);
}
