package com.teyuntong.infra.task.service.biz.customerservice.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 客服投诉记录表
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-09-29
 */
@Getter
@Setter
@TableName("cs_complaint_record")
public class CsComplaintRecord {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 电话号码
     */
    private String cellPhone;

    /**
     * 投诉人电话
     */
    private String complaintPhone;

    /**
     * 投诉人身份  1 车主2货主
     */
    private Integer complaintAuth;

    /**
     * 记录来源     1呼入 2呼出  3在线客服   4微信客服
     */
    private Integer recordSource;

    /**
     * 业务类型  存code
     */
    private Long busiType;

    /**
     * 业务ID
     */
    private String busiId;

    /**
     * 问题一级分类
     */
    private Long quesOneCategory;

    /**
     * 二级分类
     */
    private Long quesTwoCategory;

    /**
     * 三级分类
     */
    private Long quesThreeCategory;

    /**
     * 四级分类
     */
    private Long quesFourCategory;

    /**
     * 问题描述
     */
    private String quesContent;

    /**
     * 沟通结果
     */
    private String dealResult;

    /**
     * 备注
     */
    private String remark;

    /**
     * 投诉状态 1：未完成  2：已完成 
     */
    private Integer recordState;

    private Date ctime;

    /**
     * 更新时间
     */
    private Date mtime;

    /**
     * 处理组
     */
    private Long opGroup;

    /**
     * 处理人ID
     */
    private Long opUserId;

    /**
     * 处理人
     */
    private String opUser;

    /**
     * 呼入呼出手机号
     */
    private String callPhone;

    /**
     * 被投诉人电话
     */
    private String passComplaintPhone;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 被投诉人身份 1 车主2货主
     */
    private Integer passComplaintAuth;

    /**
     * 被投诉人账号
     */
    private String passComplaintNum;

    /**
     * 订单编号
     */
    private Long orderId;

    /**
     * 投诉类型
     */
    private Integer complaintType;

    /**
     * 投诉来源 1.400进线 2.线下报备 3.货源投诉 4.意见反馈 5.异常上报 6.订单投诉
     */
    private Integer complaintSource;

    /**
     * 工单类型 1.新建投诉 2.返单投诉
     */
    private Integer workOrderType;

    /**
     * 返单关联号
     */
    private Long returnOrderNo;

    /**
     * 纠纷类型
     */
    private Integer disputeType;

    /**
     * 放空公里数
     */
    private Integer emptyingKm;

    /**
     * 货源id
     */
    private Long srcMsgId;

    /**
     * 装车状态 1.运输中2.已到达已卸货3.已到达未卸货4.车方未出发5.出发未到装货地6.车方已到装货地
     */
    private Integer entruckingStatus;

    /**
     * 结单时间
     */
    private Date finishTime;

    /**
     * 工单状态 1.待处理2.处理中3.持续跟进4.工单升级5.工单上报6.搁置7.结单 8.已撤销 9.追偿订单
     */
    private Integer workOrderStatus;

    /**
     * 上一次沟通时间
     */
    private Date lastCommTime;

    /**
     * 分配人
     */
    private String distributer;

    /**
     * 分配/接收时间
     */
    private Date divisionTime;

    /**
     * 车方回访状态 1未回访 2已回访
     */
    private Integer carRvisitStatus;

    /**
     * 货方回访状态 1未回访 2已回访
     */
    private Integer goodsRvisitStatus;

    /**
     * 沟通状态：1首次响应 2继续沟通 3出具方案 4达成一致 5协助处理 6催促投诉
     */
    private Integer communicationState;

    /**
     * 工单对应的业务id
     */
    private Long serviceId;

    /**
     * 一级分类名称
     */
    private String quesOneCategoryName;

    /**
     * 二级分类名称
     */
    private String quesTwoCategoryName;

    /**
     * 三级分类名称
     */
    private String quesThreeCategoryName;

    /**
     * 四级分类名称
     */
    private String quesFourCategoryName;

    /**
     * 首次响应时间
     */
    private Date firstResponseTime;

    /**
     * 货方是否佩服 1：是  2：否
     */
    private Integer goodsCompensate;

    /**
     * 货主赔付金额 单位分
     */
    private Long goodsMoney;

    /**
     * 货站赔付金额 单位分
     */
    private Long goodsStationMoney;

    /**
     * 货站未赔付原因
     */
    private String goodsReason;

    /**
     * 平台是否赔付 1：是  2：否
     */
    private Integer platformCompensate;

    /**
     * 平台赔付金额 单位分
     */
    private Long platformMoney;

    /**
     * 平台未赔付原因
     */
    private String platformReason;

    /**
     * 车方判责满意度 1满意 2 一般 3不满意 4未告知
     */
    private Integer carSatisfied;

    /**
     * 货方判责满意度 1满意 2 一般 3不满意 4未告知
     */
    private Integer goodsSatisfied;

    /**
     * 是否是优车货源（0:否 1：是）
     */
    private Integer excellentGoods;

    /**
     * 运单号
     */
    private String tsOrderNo;

    /**
     * 发货渠道（1货主；2调度客服；3个人货主）
     */
    private Integer sourceType;

    /**
     * 升级渠道（1:工商 2:媒体 3:诉讼 4:公安 5:公司内部）
     */
    private Integer upgradeType;

    /**
     * 升级投诉（1:是 2：否）
     */
    private Integer complaintsType;

    /**
     * 沟通对象 1：车方 2：货方 3：车方&货方
     */
    private Integer recordPart;

    /**
     * 用户端：1：APP  2：PC
     */
    private Integer clientSide;

    /**
     * 车方用户等级
     */
    private String carUserLevel;

    /**
     * 是否是专属客服（0:否 1:是）
     */
    private Integer isPersonalService;
}
