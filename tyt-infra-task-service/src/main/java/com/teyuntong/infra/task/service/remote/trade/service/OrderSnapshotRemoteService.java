package com.teyuntong.infra.task.service.remote.trade.service;

import com.teyuntong.infra.common.web.feign.fallback.ThrowOriginExceptionRemoteFallbackFactory;
import com.teyuntong.trade.service.client.orders.service.TransportOrderSnapshotRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/03/25 10:50
 */
@Service
@FeignClient(name = "tyt-trade-service", path = "trade", contextId = "ordersSnapshotRpcService", fallbackFactory = OrderSnapshotRemoteService.OrderRemoteFallbackFactory.class)
public interface OrderSnapshotRemoteService extends TransportOrderSnapshotRpcService {

    @Component
    class OrderRemoteFallbackFactory extends ThrowOriginExceptionRemoteFallbackFactory<OrderSnapshotRemoteService> {
        protected OrderRemoteFallbackFactory() {
            super(true, OrderSnapshotRemoteService.class);
        }
    }
}
