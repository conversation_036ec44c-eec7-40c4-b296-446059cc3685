package com.teyuntong.infra.task.service.biz.market.activity.service.impl;

import com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.MarketingActivityUserTargetNumDO;
import com.teyuntong.infra.task.service.biz.market.activity.mybatis.mapper.MarketingActivityUserTargetNumMapper;
import com.teyuntong.infra.task.service.biz.market.activity.service.MarketingActivityUserTargetNumService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 冲单活动用户目标单量 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-22
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class MarketingActivityUserTargetNumServiceImpl implements MarketingActivityUserTargetNumService {

    private final MarketingActivityUserTargetNumMapper marketingActivityUserTargetNumMapper;

    @Override
    public List<MarketingActivityUserTargetNumDO> getByActivityId(Long activityId, Long id) {
        return marketingActivityUserTargetNumMapper.getByActivityId(activityId, id);
    }

    @Override
    public void updateTargetNum(MarketingActivityUserTargetNumDO updateDo) {
        marketingActivityUserTargetNumMapper.updateById(updateDo);
    }

    @Override
    public MarketingActivityUserTargetNumDO getByActivityUserId(Long activityId, Long userId) {
        return marketingActivityUserTargetNumMapper.getByActivityUserId(activityId, userId);
    }
}
