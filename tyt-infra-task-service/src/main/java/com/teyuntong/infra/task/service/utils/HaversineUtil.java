package com.teyuntong.infra.task.service.utils;

/**
 * <AUTHOR>
 * @since 2024/03/23 15:55
 */
public class HaversineUtil {
    private static final int EARTH_RADIUS_KM = 6371; // 地球半径，单位：千米

    /**
     * 将角度转换为弧度
     * @param angle 角度
     * @return 弧度
     */
    private static double toRadians(double angle) {
        return Math.toRadians(angle);
    }

    /**
     * 使用Haversine公式计算两个经纬度点之间的距离
     * @param lat1 第一个点的纬度
     * @param lon1 第一个点的经度
     * @param lat2 第二个点的纬度
     * @param lon2 第二个点的经度
     * @return 两个点之间的距离，单位：千米
     */
    public static double distanceTo(double lat1, double lon1, double lat2, double lon2) {
        double dLat = toRadians(lat2 - lat1);
        double dLon = toRadians(lon2 - lon1);

        lat1 = toRadians(lat1);
        lat2 = toRadians(lat2);

        double a = Math.pow(Math.sin(dLat / 2), 2)
                + Math.pow(Math.sin(dLon / 2), 2) * Math.cos(lat1) * Math.cos(lat2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        double distance = EARTH_RADIUS_KM * c;

        return distance;
    }

    public static void main(String[] args) {
        double lat1 = 39.9075; // 北京的纬度
        double lon1 = 116.39723; // 北京的经度
        double lat2 = 31.2304; // 上海的纬度
        double lon2 = 121.4737; // 上海的经度

        double distance = distanceTo(lat1, lon1, lat2, lon2);
        System.out.println("Distance between Beijing and Shanghai: " + distance + " km");
    }
}
