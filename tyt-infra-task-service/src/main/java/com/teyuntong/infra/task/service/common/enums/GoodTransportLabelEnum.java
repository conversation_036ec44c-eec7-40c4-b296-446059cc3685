package com.teyuntong.infra.task.service.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 好货标签 11:好1，12:好2，13:好3，21:中1，22:中2，23:中3，31:差1，32:差2，0:不符合
 *
 * <AUTHOR>
 * @since 2025-07-03 19:27
 */
@Getter
@AllArgsConstructor
public enum GoodTransportLabelEnum {
    GOOD_1(11, "好1"),
    GOOD_2(12, "好2"),
    GOOD_3(13, "好3"),
    MEDIUM_1(21, "中1"),
    MEDIUM_2(22, "中2"),
    MEDIUM_3(23, "中3"),
    POOR_1(31, "差1"),
    POOR_2(32, "差2"),
    OTHER(0, "其他")
    ;

    private Integer code;
    private String name;
}
