package com.teyuntong.infra.task.service.common.mq.dto;

import com.teyuntong.infra.task.service.common.mq.pojo.MqBaseMessageBean;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @since 2024/09/06 13:09
 */
@Data
public class MqOperateFreightMsgDTO extends MqBaseMessageBean implements Serializable {

    public static final int OPERATE_TYPE_DISTRIBUTE = 1;

    public static final int OPERATE_TYPE_REFUND = 2;

    /**
     * 运单ID
     */
    private Long orderId;

    /**
     * 操作类型
     */
    private Integer operateType;

    /**
     * 退款原因
     */
    private String refundReason;
}
