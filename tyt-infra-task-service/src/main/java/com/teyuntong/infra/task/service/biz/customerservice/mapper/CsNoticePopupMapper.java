package com.teyuntong.infra.task.service.biz.customerservice.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.infra.task.service.biz.customerservice.entity.CsNoticePopup;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 客服系统弹窗通知 Mapper 接口
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-09-29
 */
@Mapper
public interface CsNoticePopupMapper extends BaseMapper<CsNoticePopupMapper> {
    /**
     * 发通知
     * @param noticePopup 弹窗信息
     * @return int
     */
    int addNotice(@Param("noticePopup") CsNoticePopup noticePopup);
}
