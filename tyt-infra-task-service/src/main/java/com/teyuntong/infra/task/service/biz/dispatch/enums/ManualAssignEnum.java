package com.teyuntong.infra.task.service.biz.dispatch.enums;

/**
 * 是否人工指派枚举类
 *
 * <AUTHOR>
 * @since 2024/08/02 13:58
 */
public enum ManualAssignEnum {

    NO(0,"否"),
    YES(1,"是");

    private int code;

    private String desc;

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    ManualAssignEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
