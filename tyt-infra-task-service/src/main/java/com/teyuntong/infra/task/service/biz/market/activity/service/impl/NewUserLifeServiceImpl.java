package com.teyuntong.infra.task.service.biz.market.activity.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSON;
import com.teyuntong.infra.common.web.feign.RemoteBusinessException;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.bean.BaseTransportSearchDTO;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.bean.TransportLabelJson;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.entity.LogTsSearchDO;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.entity.TytTransport;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.service.LogTsSearchService;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.service.TransportService;
import com.teyuntong.infra.task.service.biz.market.activity.service.NewUserLifeService;
import com.teyuntong.infra.task.service.biz.trade.infofee.service.TransportOrdersRiskService;
import com.teyuntong.infra.task.service.biz.user.car.pojo.ShortMessageBean;
import com.teyuntong.infra.task.service.biz.user.car.service.CallPhoneRecordService;
import com.teyuntong.infra.task.service.biz.user.coupon.enums.PopupTypeEnum;
import com.teyuntong.infra.task.service.biz.user.coupon.mybatis.entity.PopupSaveBean;
import com.teyuntong.infra.task.service.biz.user.coupon.mybatis.entity.TytNoticePopupTempl;
import com.teyuntong.infra.task.service.biz.user.coupon.service.NoticePopupService;
import com.teyuntong.infra.task.service.biz.user.coupon.service.PromoUserCouponService;
import com.teyuntong.infra.task.service.biz.user.coupon.service.TytNoticePopupTemplService;
import com.teyuntong.infra.task.service.biz.user.info.mybatis.entity.User;
import com.teyuntong.infra.task.service.biz.user.info.service.UserCallPhoneRecordService;
import com.teyuntong.infra.task.service.biz.user.info.service.UserService;
import com.teyuntong.infra.task.service.common.mq.service.MessageCenterPushService;
import com.teyuntong.infra.task.service.remote.tytconfig.service.TytConfigRemoteService;
import com.teyuntong.infra.task.service.utils.TimeUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2025/01/13 17:29
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class NewUserLifeServiceImpl implements NewUserLifeService {

    private final TytConfigRemoteService tytConfigRemoteService;
    private final StringRedisTemplate redisTemplate;
    private final UserService userService;
    private final UserCallPhoneRecordService userCallPhoneRecordService;
    private final PromoUserCouponService promoUserCouponService;
    private final MessageCenterPushService messageCenterPushService;
    private final TytNoticePopupTemplService tytNoticePopupTemplService;
    private final NoticePopupService noticePopupService;
    private final TransportOrdersRiskService transportOrdersRiskService;
    private final LogTsSearchService logTsSearchService;
    private final TransportService transportService;

    //新用户生命周期提醒开关
    private static final String NEW_USER_LIFE_SWITCH = "new_user_life_switch";
    private static final String NO_CALL_NEW_USER_SMS = "no_call_new_user_sms";
    private static final String NO_AUTH_NEW_USER_SMS = "no_auth_new_user_sms";
    private static final String NO_COUPON_NEW_USER_SMS = "no_coupon_new_user_sms";
    private static final String COUPON_NEW_USER_SMS = "coupon_new_user_sms";
    private static final String NEW_USER_NOTICE_REDIS_KEY = "activity:new:user:id";
    private static final String NEW_USER_LIFE_START_USER_ID = "new_user_life_start_user_id";
    private static final String NEW_USER_LIFE_GIVE_COUPON = "new_user_life_give_coupon";
    private static final BigDecimal HUNDRED = new BigDecimal(100);

    @Override
    public void newUserNotice() {
        Integer onoff = tytConfigRemoteService.getIntValue(NEW_USER_LIFE_SWITCH, 0);
        if (onoff == 0) {
            return;
        }
        Date startTime = null;
        Date endTime = TimeUtil.addMinute(new Date(), -60);

        String lastUserId = redisTemplate.opsForValue().get(NEW_USER_NOTICE_REDIS_KEY);
        Long userId = null;
        if (StringUtils.isNotBlank(lastUserId)){
            userId = Long.valueOf(lastUserId);
        }else {
            Integer startUserId = tytConfigRemoteService.getIntValue(NEW_USER_LIFE_START_USER_ID, 0);
            userId = startUserId.longValue();
            startTime = TimeUtil.addMinute(new Date(), -75);
        }
        List<User> users = userService.getByCtimeForNewUser(startTime, endTime, userId);
        if (CollectionUtils.isEmpty(users)){
            return;
        }
        for (User user : users) {
            sendSms(user);
        }
        userId = users.get(users.size()-1).getId();
        redisTemplate.opsForValue().set(NEW_USER_NOTICE_REDIS_KEY, userId.toString(),1, TimeUnit.HOURS);
    }

    @Override
    public void newUserPopupNotice() {
        Integer onoff = tytConfigRemoteService.getIntValue(NEW_USER_LIFE_SWITCH, 0);
        if (onoff == 0) {
            return;
        }
        Date startTime = TimeUtil.weeHours(TimeUtil.addDay(new Date(), -29),0);
        Date endTime = TimeUtil.weeHours(TimeUtil.addDay(new Date(), -1),1);
        //获取注册2-30内的用户
        Integer startUserId = tytConfigRemoteService.getIntValue(NEW_USER_LIFE_START_USER_ID, 0);
        Long userId = startUserId.longValue();
        while (true){
            List<User> users = userService.getByCtimeForNewUser(startTime, endTime, userId);
            if (CollectionUtils.isEmpty(users)){
                break;
            }
            for (User user : users) {
                //查询用户是否拨打过电话
                Integer callCount = userCallPhoneRecordService.getUserCallRecordsCount(user.getId(),user.getCtime(),new Date());
                //下发弹窗
                if (callCount == 0){
                    sendNoticePopup(user.getId(), PopupTypeEnum.未拨打电话新用户弹窗提醒);
                }
            }
            userId = users.get(users.size()-1).getId();
        }
    }

    @Override
    public void newUserGuaranteePopupNotice() {
        Integer onoff = tytConfigRemoteService.getIntValue(NEW_USER_LIFE_SWITCH, 0);
        if (onoff == 0) {
            return;
        }
        Date startTime = TimeUtil.weeHours(TimeUtil.addDay(new Date(), -20),0);
        Date endTime = TimeUtil.weeHours(TimeUtil.addDay(new Date(), -20),1);
        Integer startUserId = tytConfigRemoteService.getIntValue(NEW_USER_LIFE_START_USER_ID, 0);
        Long userId = startUserId.longValue();
        while (true){
            List<User> users = userService.getByCtimeForNewUser(startTime, endTime, userId);
            if (CollectionUtils.isEmpty(users)){
                break;
            }
            for (User user : users) {
//                if (user.getId() % 4 == 2 || user.getId() % 4 == 3) {
                    Integer callCount = userCallPhoneRecordService.getUserCallRecordsCount(user.getId(),user.getCtime(),new Date());
                    if (callCount > 0){
                        //查询用户是否履约过
                        int orderCount = transportOrdersRiskService.getHonourOrderCount(user.getId(),user.getCtime(),new Date());
                        //下发弹窗
                        if (orderCount == 0){
                            sendNoticePopup(user.getId(), PopupTypeEnum.已首活未首履新用户弹窗提醒);
                        }
                    }
//                }
            }
            userId = users.get(users.size()-1).getId();
        }
    }

    private void sendNoticePopup(Long userId, PopupTypeEnum popupType){
        TytNoticePopupTempl popupTmpl = tytNoticePopupTemplService.getByType(popupType.getType1(), popupType.getType2());
        PopupSaveBean popupSaveBean = new PopupSaveBean();
        popupSaveBean.setCarPopup(1);
        popupSaveBean.setReceiveId(userId);
        noticePopupService.savePopup(popupSaveBean, popupTmpl);
    }

    private void sendSms(User user) {
        //用户是否首活过
        Integer callCount = userCallPhoneRecordService.getUserCallRecordsCount(user.getId(),user.getCtime(),new Date());
        if (callCount == 0){
            String content = promoUserCouponService.getSmsTmpl(NO_CALL_NEW_USER_SMS);
            sendMqSms(user.getCellPhone(),content);
            return;
        }
        //用户是否实名认证
        if (user.getVerifyPhotoSign() == 0){
            String content = promoUserCouponService.getSmsTmpl(NO_AUTH_NEW_USER_SMS);
            sendMqSms(user.getCellPhone(),content);
            return;
        }

//        if (user.getId() % 4 == 2 || user.getId() % 4 == 3){
            //用户是否有订金优惠券
            Integer activityId = tytConfigRemoteService.getIntValue(NEW_USER_LIFE_GIVE_COUPON, 0);
            if (activityId > 0){
                BigDecimal orderCouponAmount = promoUserCouponService.getOrderCouponAmount(user.getId(), activityId);
                if (orderCouponAmount != null && orderCouponAmount.compareTo(BigDecimal.ZERO) > 0){
                    String content = promoUserCouponService.getSmsTmpl(COUPON_NEW_USER_SMS);
                    content = StringUtils.replaceEach(content, new String[] { "${amount}"}, new String[] {orderCouponAmount.toString()});
                    sendMqSms(user.getCellPhone(),content);
                    return;
                }
            }
            String content = promoUserCouponService.getSmsTmpl(NO_COUPON_NEW_USER_SMS);
            sendMqSms(user.getCellPhone(),content);
//        }
    }

    private void sendMqSms(String cellPhone , String content) {
        try {
            //发送短信
            ShortMessageBean shortMessage = messageCenterPushService.createShortMessage(cellPhone, content, "新注册用户");
            messageCenterPushService.sendMultiMessage(shortMessage, null, null);
        } catch (RemoteBusinessException e) {
            log.error("新用户发短信失败，", e);
        }
    }

    /**
     * 新用户推荐货源推送
     * 条件：新户自注册当日起，从次日起，连续3天，每日推送1条货源短信
     * 逻辑：推送与用户最近一次搜索的出发地（市级）、目的地（市级，若是省或全国则以对应省或全国为准）一致的、在线的、有价货源质量分最高的货源
     * 时间：每天早上10点
     */
    @Override
    public void pushRecommendedGoods() {
        Integer onoff = tytConfigRemoteService.getIntValue(NEW_USER_LIFE_SWITCH, 0);
        if (onoff == 0) {
            return;
        }
        Date endTime = DateUtil.beginOfDay(new Date());
        Date startTime = DateUtil.offsetDay(endTime, -3);
        Integer startUserId = tytConfigRemoteService.getIntValue(NEW_USER_LIFE_START_USER_ID, 0);
        Long userId = startUserId.longValue();
        while (true) {
            List<User> users = userService.getByCtimeForNewUser(startTime, endTime, userId);
            if (CollectionUtils.isEmpty(users)) {
                break;
            }
            for (User user : users) {
                try {
                    sendRecommendedGoodsMsg(user);
                } catch (Exception e) {
                    log.error("新用户推荐货源推送 异常，用户id:{}，", user.getId(), e);
                }
            }
            userId = users.get(users.size() - 1).getId();
        }
    }

    /**
     * 推送推荐货源短信
     */
    private void sendRecommendedGoodsMsg(User user) {
        log.info("新用户推荐货源推送，用户id:{}", user.getId());
        // 查询用户最近一次找货大厅搜索
        LogTsSearchDO logSearch = logTsSearchService.getUserLastSearch(user.getId());
        if (logSearch != null) {

            // 根据搜索查询有价货源
            BaseTransportSearchDTO searchDTO = new BaseTransportSearchDTO();
            if (StringUtils.isNotBlank(logSearch.getStartCoord())) {
                String[] coordArray = logSearch.getStartCoord().split("_");
                searchDTO.setStartCoordX(new BigDecimal(coordArray[0]).multiply(HUNDRED).longValue());
                searchDTO.setStartCoordY(new BigDecimal(coordArray[1]).multiply(HUNDRED).longValue());
                searchDTO.setStartRange(logSearch.getStartRange() * 100);
            }
            if (StringUtils.isNotBlank(logSearch.getDestCoord())) {
                String[] coordArray = logSearch.getDestCoord().split("_");
                searchDTO.setDestCoordX(new BigDecimal(coordArray[0]).multiply(HUNDRED).longValue());
                searchDTO.setDestCoordY(new BigDecimal(coordArray[1]).multiply(HUNDRED).longValue());
                searchDTO.setDestRange(logSearch.getStartRange() * 100);
            }
            searchDTO.setStartProvinc(logSearch.getStartProvinc());
            searchDTO.setStartCity(logSearch.getStartCity());
            searchDTO.setDestProvinc(logSearch.getDestProvinc());
            searchDTO.setDestCity(logSearch.getDestCity());
            List<TytTransport> tytTransports = transportService.searchTransportForNewUser(searchDTO);

            // 获取货源质量分最高的货源
            TytTransport maxScoreTransport = tytTransports.stream()
                    .map(t -> {
                        TransportLabelJson labelJson = JSON.parseObject(t.getLabelJson(), TransportLabelJson.class);
                        return new AbstractMap.SimpleEntry<>(t,
                                labelJson == null || labelJson.getGoodsModelScore() == null ? BigDecimal.ZERO : labelJson.getGoodsModelScore());
                    })
                    .max(Comparator.comparingDouble(t -> t.getValue().doubleValue()))
                    .map(AbstractMap.SimpleEntry::getKey).orElse(null);
            if (maxScoreTransport != null) {
                // 推送短信
                String url = tytConfigRemoteService.getStringValue("tyt_server_picture_url", "http://www.teyuntong.net")
                        + "/jump.html?t=c&jp=cgd&viewSource=13&id=" + maxScoreTransport.getSrcMsgId();
                String smsContent = "有从" + maxScoreTransport.getStartCity() + maxScoreTransport.getStartArea() + "——"
                        + maxScoreTransport.getDestCity() + maxScoreTransport.getDestArea() + "的货源上新了，抓紧抢订，点击 " + url + " 查看";
                sendMqSms(user.getCellPhone(), smsContent);
                log.info("新用户推荐货源推送 成功，用户id:{}，货源id:{}", user.getId(), maxScoreTransport.getSrcMsgId());
            } else {
                log.info("新用户推荐货源推送，用户id:{}，无货源", user.getId());
            }
        }
    }

}
