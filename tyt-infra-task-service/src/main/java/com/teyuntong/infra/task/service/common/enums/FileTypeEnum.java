package com.teyuntong.infra.task.service.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum FileTypeEnum {
    /**
     * 文件后缀类型
     */
    BMP(".bmp", "image/*"),
    GIF(".gif", "image/*"),
    JPEG(".jpeg", "image/*"),
    JPG(".jpg", "image/*"),
    PNG(".png", "image/*"),
    XLS(".xls", "application/vnd.ms-excel"),
    XLSX(".xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"),
    DOC(".doc", "application/msword"),
    DOCX(".docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"),
    PPT(".ppt", "application/vnd.ms-powerpoint"),
    PPTX(".pptx", "application/vnd.openxmlformats-officedocument.presentationml.presentation"),
    PDF(".pdf", "application/pdf"),
    MDB(".mdb", "application/octet-stream"),
    OTHER("", "application/octet-stream"),

    ;

    private final String suffix;
    private final String contentType;


    public static String getContentType(String fileName) {
        FileTypeEnum resultEnum = FileTypeEnum.OTHER;

        int index = fileName.lastIndexOf(".");

        String suffixTmp = null;

        if (index >= 0) {
            suffixTmp = fileName.substring(index).toLowerCase();
        }

        if (StringUtils.isNotBlank(suffixTmp)) {

            for (FileTypeEnum oneEnum : FileTypeEnum.values()) {
                String oneSuffix = oneEnum.getSuffix();

                if (suffixTmp.equalsIgnoreCase(oneSuffix)) {
                    resultEnum = oneEnum;
                    break;
                }
            }

        }

        return resultEnum.getContentType();
    }

}
