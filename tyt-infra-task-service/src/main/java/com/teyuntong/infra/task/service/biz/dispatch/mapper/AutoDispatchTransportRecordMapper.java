package com.teyuntong.infra.task.service.biz.dispatch.mapper;

import com.teyuntong.infra.task.service.biz.dispatch.entity.AutoDispatchTransportRecordDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 可自动指派货源记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-12-12
 */
@Mapper
public interface AutoDispatchTransportRecordMapper extends BaseMapper<AutoDispatchTransportRecordDO> {

    int countDispatchedBySrcMsgId(@Param("srcMsgId") Long srcMsgId);

    int countBySrcMsgIdList(@Param("srcMsgIds") List<Long> srcMsgIds);
}
