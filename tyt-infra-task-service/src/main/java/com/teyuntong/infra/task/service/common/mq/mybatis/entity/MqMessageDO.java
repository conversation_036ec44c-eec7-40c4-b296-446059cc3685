package com.teyuntong.infra.task.service.common.mq.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <p>
 * 存储发送到mq的处理消息,用于保证每条消息都得到正确的处理
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-01-20
 */
@Getter
@Setter
@TableName("tyt_mq_message")
public class MqMessageDO {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 消息序列号，每个消息有一个唯一的序列号，用于唯一标示一条消息
     */
    private String messageSerialNum;

    /**
     * 消息的原始完整内容
     */
    private String messageContent;

    /**
     * 消息的处理状态，1：未处理 2：已处理 3: 处理失败，4连续失败后状态
     */
    private Integer dealStatus;

    private Date createTime;

    private Date updateTime;

    /**
     * 1：退款 2：提现 3：异常 4：成交(车主与货主成交) 5：信息费
     */
    private Integer messageType;

    /**
     * 失败后消费次数
     */
    private Integer sendNbr;
}
