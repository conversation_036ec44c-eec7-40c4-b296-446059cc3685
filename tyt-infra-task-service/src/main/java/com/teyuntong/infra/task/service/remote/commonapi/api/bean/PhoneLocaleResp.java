package com.teyuntong.infra.task.service.remote.commonapi.api.bean;

import java.io.Serializable;

/**
 * 归属地返回对象
 * Created by duanwc on 17/8/1.
 */
public class PhoneLocaleResp implements Serializable {
    private static final long serialVersionUID = -1;
    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 运营商
     */
    private String operation;
    /**
     * 状态码 1-成功 2-失败
     */
    private Integer status;
    /**
     * 版本
     */
    private String version;

    /**
     * code码
     */
    private int code;
    /**
     * code码对应的信息
     */
    private String message;


    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    @Override
    public String toString() {
        return "PhoneLocale{" +
                "province='" + province + '\'' +
                ", city='" + city + '\'' +
                ", operation='" + operation + '\'' +
                ", status=" + status +
                ", version='" + version + '\'' +
                ", code=" + code +
                ", message='" + message + '\'' +
                '}';
    }
}
