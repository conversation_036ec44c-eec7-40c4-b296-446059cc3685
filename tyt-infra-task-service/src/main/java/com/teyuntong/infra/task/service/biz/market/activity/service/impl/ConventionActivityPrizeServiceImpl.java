package com.teyuntong.infra.task.service.biz.market.activity.service.impl;

import com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.ConventionActivityPrizeDO;
import com.teyuntong.infra.task.service.biz.market.activity.mybatis.mapper.ConventionActivityPrizeMapper;
import com.teyuntong.infra.task.service.biz.market.activity.service.ConventionActivityPrizeService;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import lombok.RequiredArgsConstructor;

import java.util.List;

/**
 * <p>
 * 履约活动冲单奖品配置 服务实现类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-11-08
 */

@Service
@Slf4j
@RequiredArgsConstructor
public class ConventionActivityPrizeServiceImpl implements ConventionActivityPrizeService {

    private final ConventionActivityPrizeMapper conventionActivityPrizeMapper;

    @Override
    public List<ConventionActivityPrizeDO> getByActivityId(long activityId, int type) {
        return conventionActivityPrizeMapper.getByActivityId(activityId, type);
    }
}
