package com.teyuntong.infra.task.service.biz.dispatch.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <p>
 * 专车派单通知调度表
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-09-19
 */
@Getter
@Setter
@TableName("tyt_special_car_dispatch_notify")
public class SpecialCarDispatchNotifyDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 专车派单id(tyt_special_car_dispatch表id)
     */
    private Long dispatchId;

    /**
     * 货源ID
     */
    private Long tsId;

    /**
     * 发货用户类型：1-调度发货，2-普通用户发货
     */
    private Integer publishUserType;

    /**
     * 调度公司id(tyt_dispatch_company表id)
     */
    private Long dispatchCompanyId;

    /**
     * 调度人员ID
     */
    private Long dispatchUserId;

    /**
     * 调度人员姓名
     */
    private String dispatchUserName;

    /**
     * 调度人员手机号
     */
    private String dispatchCellPhone;

    /**
     * 调度人员钉钉手机号
     */
    private String dispatchDingTalkPhone;

    /**
     * 派单失败通知次数
     */
    private Integer notifyCount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;
}
