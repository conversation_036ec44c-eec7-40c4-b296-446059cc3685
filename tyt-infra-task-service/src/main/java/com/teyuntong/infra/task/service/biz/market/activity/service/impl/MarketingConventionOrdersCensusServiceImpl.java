package com.teyuntong.infra.task.service.biz.market.activity.service.impl;

import com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.MarketingConventionOrdersCensusDO;
import com.teyuntong.infra.task.service.biz.market.activity.mybatis.mapper.MarketingConventionOrdersCensusMapper;
import com.teyuntong.infra.task.service.biz.market.activity.service.MarketingConventionOrdersCensusService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 冲单活动轮次奖品 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-22
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class MarketingConventionOrdersCensusServiceImpl implements MarketingConventionOrdersCensusService {

    private final MarketingConventionOrdersCensusMapper marketingConventionOrdersCensusMapper;

    @Override
    public MarketingConventionOrdersCensusDO getActivityLevel(Long activityId, Integer rankLevel, Integer stage) {
        return marketingConventionOrdersCensusMapper.getActivityLevel(activityId, rankLevel, stage);
    }
}
