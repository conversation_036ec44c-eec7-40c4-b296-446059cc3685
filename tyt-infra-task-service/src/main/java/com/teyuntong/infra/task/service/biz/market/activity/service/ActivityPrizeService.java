package com.teyuntong.infra.task.service.biz.market.activity.service;


import com.teyuntong.infra.task.service.biz.market.activity.dto.ActivityGradeBean;

import java.util.List;

/**
 * <p>
 * 活动奖品表 服务类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-09-30
 */
public interface ActivityPrizeService {

    Long getByPrize(Integer prize);

    List<ActivityGradeBean> getActivityGradeBeanByActivityId(Long activityId);
}
