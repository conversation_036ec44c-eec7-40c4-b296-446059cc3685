package com.teyuntong.infra.task.service.biz.dispatch.mapper;

import com.teyuntong.infra.task.service.biz.dispatch.entity.TransportAutoDispatchConfigDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 非专车货源自动派单配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-12-12
 */
@Mapper
public interface TransportAutoDispatchConfigMapper extends BaseMapper<TransportAutoDispatchConfigDO> {

    List<TransportAutoDispatchConfigDO> selectAllEnableConfig(@Param("start") Integer start,
                                                              @Param("pageSize") Integer pageSize);

    TransportAutoDispatchConfigDO selectByRoute(@Param("startCity") String startCity, @Param("destCity") String destCity);
}
