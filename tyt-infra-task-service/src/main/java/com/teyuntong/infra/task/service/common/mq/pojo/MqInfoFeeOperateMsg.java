package com.teyuntong.infra.task.service.common.mq.pojo;

import lombok.Data;
import java.io.Serializable;

@Data
public class MqInfoFeeOperateMsg extends MqBaseMessageBean implements Serializable {

    /**
     * 操作方式 1:支付信息费 2:装货完成支付信息费 3:货方发起信息费退款 4:车方同意退款 5:车主拒绝退款
     * 6:车主发起信息费冻结 7:车方信息费解冻 8:车主异常上报 9:货方异常上报 10:信息费即将7天自动支付 11:
     */
    private Integer opStatus;
    /**
     * 货主id
     */
    private Long shipperUserId;
    /**
     * 车主ID
     */
    private Long carOwnerUserId;

    /**
     * 货源Id tyt_transport_main表主键id
     */
    private Long tsId;
    /**
     * 运单编号 tyt_transport_orders表ts_order_no
     */
    private String tsOrderNo;

    /**
     * 订单id  tyt_transport_orders主键Id
     */
    private Long orderId;

    /**
     * tyt_transport_orders的pay_no 和 tyt_old_order表order_id
     */
    private String payNo;

    /**
     * 出发地
     */
    private String startPoint;

    /**
     * 目的地
     */
    private String destPoint;
    /**
     * 货物内容
     */
    private String taskContent;

    /**
     * 操作金额
     */
    private String amount;

    /**
     * 退款方式 1 全额退款 2:部分退款
     */
    private Integer refundType;

    /**
     * 平台服务费
     */
    private String infoFeeServiceFee;

    /**
     * 技术服务费
     */
    private String tecServiceFee;

    /**
     * 技术服务费单号
     */
    private String technicalServiceNo;

}
