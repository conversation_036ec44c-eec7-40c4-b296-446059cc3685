package com.teyuntong.infra.task.service.biz.message.center.mapper;

import com.teyuntong.infra.task.service.biz.message.center.entity.EmployeeMessage;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 员工消息表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-10-23
 */
@Mapper
public interface EmployeeMessageMapper extends BaseMapper<EmployeeMessage> {

    void batchInsert(@Param("messageList") List<EmployeeMessage> messageList);
}
