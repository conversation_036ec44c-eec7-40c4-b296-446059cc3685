package com.teyuntong.infra.task.service.common.mq.dto;

import com.teyuntong.infra.task.service.common.mq.pojo.MqBaseMessageBean;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2024/09/06 13:15
 */
@Data
public class MqOrderOperateStatusMsgDTO extends MqBaseMessageBean implements Serializable {

    private Long orderId;
    private int orderStatus;
    private int costStatus;
    private int operateType;
    private int dataType;
    private Date createTime;
    private int delFlag;
}
