package com.teyuntong.infra.task.service.biz.customerservice.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 客服系统弹窗通知模板
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-09-29
 */
@Getter
@Setter
@TableName("cs_notice_popup_tmpl")
public class CsNoticePopupTmpl {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 通知标识
     */
    private String code;

    /**
     * 标题
     */
    private String title;

    /**
     * 通知内容
     */
    private String content;

    /**
     * 是否有效 0无效 1有效
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;
}
