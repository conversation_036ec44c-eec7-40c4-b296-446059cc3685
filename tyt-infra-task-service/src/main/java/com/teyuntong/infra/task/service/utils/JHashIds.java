package com.teyuntong.infra.task.service.utils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class JHashIds {
    private static final String DEFAULT_ALPHABET = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890";
    private static final String DEFAULT_SALT = "";
    private static final String DEFAULT_SEPARATORS = "cfhistuCFHISTU";
    private static final double SEP_DIV_THRESHOLD = 3.5D;
    private static final int GUARD_DIV = 12;
    private static final int MIN_ALPHABET_LENGTH = 16;
    private final char[] usedSeparators;
    private final int minHashLength;
    private final char[] usedSalt;
    private final char[] usedAlphabet;
    private final char[] usedGuards;
    private final Pattern guardPattern;
    private final Pattern splitPattern;

    public JHashIds(String salt, Integer minHashLength, String alphabet) {
        this.usedSalt = salt != null && !salt.isEmpty()?salt.toCharArray():"".toCharArray();
        this.minHashLength = minHashLength.intValue();
        char[] prepUsedAlphabet = makeUniqueAlphabet(alphabet != null?alphabet:"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890");
        char[] prepUsedSeparators = "cfhistuCFHISTU".toCharArray();
        if(prepUsedAlphabet.length < 16) {
            throw new RuntimeException("error: alphabet must contain at least X unique characters");
        } else if(findCharInCharArray(prepUsedAlphabet, ' ') != -1) {
            throw new RuntimeException("error: alphabet cannot contain spaces");
        } else {
            int guardCount;
            int g;
            for(guardCount = 0; guardCount < prepUsedSeparators.length; ++guardCount) {
                g = findCharInCharArray(prepUsedAlphabet, prepUsedSeparators[guardCount]);
                if(g == -1) {
                    prepUsedSeparators[guardCount] = 32;
                } else {
                    prepUsedAlphabet[g] = 32;
                }
            }

            prepUsedAlphabet = removeCharFromCharArray(prepUsedAlphabet, ' ');
            prepUsedSeparators = consistentShuffle(removeCharFromCharArray(prepUsedSeparators, ' '), this.usedSalt);
            if(prepUsedSeparators.length > 0 || (double)(prepUsedAlphabet.length / prepUsedSeparators.length) > 3.5D) {
                guardCount = Math.max((int)Math.ceil((double)prepUsedAlphabet.length / 3.5D), 2);
                if(guardCount > prepUsedSeparators.length) {
                    g = guardCount - prepUsedSeparators.length;
                    char[] a = Arrays.copyOf(prepUsedSeparators, prepUsedSeparators.length + g);
                    System.arraycopy(prepUsedAlphabet, 0, a, prepUsedSeparators.length, g);
                    prepUsedSeparators = a;
                    prepUsedAlphabet = Arrays.copyOfRange(prepUsedAlphabet, g, prepUsedAlphabet.length);
                } else {
                    prepUsedSeparators = Arrays.copyOfRange(prepUsedSeparators, 0, guardCount);
                }
            }

            prepUsedAlphabet = consistentShuffle(prepUsedAlphabet, this.usedSalt);
            guardCount = (int)Math.ceil((double)prepUsedAlphabet.length / 12.0D);
            if(prepUsedAlphabet.length < 3) {
                this.usedGuards = Arrays.copyOf(prepUsedSeparators, guardCount);
                prepUsedSeparators = Arrays.copyOfRange(prepUsedSeparators, guardCount, prepUsedSeparators.length);
            } else {
                this.usedGuards = Arrays.copyOf(prepUsedAlphabet, guardCount);
                prepUsedAlphabet = Arrays.copyOfRange(prepUsedAlphabet, guardCount, prepUsedAlphabet.length);
            }

            this.usedAlphabet = prepUsedAlphabet;
            this.usedSeparators = prepUsedSeparators;
            String var10 = new String(this.usedGuards);
            String var11 = new String(this.usedAlphabet);
            String s = new String(this.usedSeparators);
            this.guardPattern = Pattern.compile("^([" + var11 + "]*[" + var10 + "])?([" + var11 + s + "]+)([" + var10 + "][" + var11 + "]*)?$");
            this.splitPattern = Pattern.compile("[" + s + "]");
        }
    }

    private static char[] removeCharFromCharArray(char[] input, char removeChar) {
        int newArraySize = input.length;
        char[] result = input;
        int writePos = input.length;

        for(int var5 = 0; var5 < writePos; ++var5) {
            char c = result[var5];
            if(c == removeChar) {
                --newArraySize;
            }
        }

        result = new char[newArraySize];
        writePos = 0;
        char[] var9 = input;
        int var10 = input.length;

        for(int var7 = 0; var7 < var10; ++var7) {
            char c1 = var9[var7];
            if(c1 != removeChar) {
                result[writePos++] = c1;
            }
        }

        return result;
    }

    private static char[] makeUniqueAlphabet(String alphabet) {
        LinkedHashSet characters = new LinkedHashSet();
        char[] result = alphabet.toCharArray();
        int pos = result.length;

        for(int var4 = 0; var4 < pos; ++var4) {
            char character = result[var4];
            characters.add(Character.valueOf(character));
        }

        result = new char[characters.size()];
        pos = 0;

        Character var7;
        for(Iterator var6 = characters.iterator(); var6.hasNext(); result[pos++] = var7.charValue()) {
            var7 = (Character)var6.next();
        }

        return result;
    }

    private static int calcNumbersHashInt(long... numbers) {
        int numbersHashInt = 0;

        for(int i = 0; i < numbers.length; ++i) {
            numbersHashInt = (int)((long)numbersHashInt + numbers[i] % (long)(i + 100));
        }

        return numbersHashInt;
    }

    public String encodeHex(String hex) {
        if(!hex.matches("^[0-9a-fA-F]+$")) {
            return "";
        } else {
            Matcher matcher = Pattern.compile("[\\w\\W]{1,12}").matcher(hex);
            ArrayList matched = new ArrayList();

            while(matcher.find()) {
                matched.add(Long.valueOf(Long.parseLong("1" + matcher.group(), 16)));
            }

            long[] numbers = new long[matched.size()];

            for(int i = 0; i < matched.size(); ++i) {
                numbers[i] = ((Long)matched.get(i)).longValue();
            }

            return this.encode(numbers);
        }
    }

    public String decodeHex(String hashid) {
        long[] numbers = this.decode(hashid);
        StringBuilder sb = new StringBuilder();
        long[] var4 = numbers;
        int var5 = numbers.length;

        for(int var6 = 0; var6 < var5; ++var6) {
            long number = var4[var6];
            sb.append(Long.toHexString(number).substring(1));
        }

        return sb.toString();
    }

    public String encode(long... numbers) {
        if(0 == numbers.length) {
            return "";
        } else {
            long[] var2 = numbers;
            int var3 = numbers.length;

            for(int var4 = 0; var4 < var3; ++var4) {
                long number = var2[var4];
                if(number < 0L) {
                    return "";
                }
            }

            return this._encode(numbers);
        }
    }

    private String _encode(long[] numbers) {
        int numbersHashInt = calcNumbersHashInt(numbers);
        char[] alphabet = Arrays.copyOf(this.usedAlphabet, this.usedAlphabet.length);
        char lottery = alphabet[numbersHashInt % alphabet.length];
        StringBuilder resultBuilder = new StringBuilder("" + lottery);
        char[] buffer = new char[1 + this.usedSalt.length + this.usedAlphabet.length];
        buffer[0] = lottery;

        int hashPos;
        for(int resultChars = 0; resultChars != numbers.length; ++resultChars) {
            System.arraycopy(this.usedSalt, 0, buffer, 1, this.usedSalt.length);
            System.arraycopy(alphabet, 0, buffer, 1 + this.usedSalt.length, alphabet.length);
            alphabet = consistentShuffle(alphabet, Arrays.copyOfRange(buffer, 0, alphabet.length));
            char[] currentSize = hash(numbers[resultChars], alphabet);
            resultBuilder.append(currentSize);
            if(resultChars + 1 < numbers.length) {
                numbers[resultChars] %= (long)(currentSize[0] + 1);
                hashPos = (int)(numbers[resultChars] % (long)this.usedSeparators.length);
                resultBuilder.append(this.usedSeparators[hashPos]);
            }
        }

        char[] var17 = resultBuilder.toString().toCharArray();
        int var18 = var17.length;
        if(var17.length < this.minHashLength) {
            hashPos = this.minHashLength - var17.length - (this.minHashLength - var17.length) / 2;
            char[] minResultSizeBuffer = new char[this.minHashLength];
            System.arraycopy(var17, 0, minResultSizeBuffer, hashPos, var17.length);
            int writeFrontPos = hashPos - 1;
            int writeEndPos = hashPos + var17.length;
            int guardIndex = (numbersHashInt + minResultSizeBuffer[hashPos]) % this.usedGuards.length;
            minResultSizeBuffer[writeFrontPos--] = this.usedGuards[guardIndex];
            ++var18;
            if(writeEndPos < minResultSizeBuffer.length) {
                guardIndex = (numbersHashInt + minResultSizeBuffer[hashPos + 1]) % this.usedGuards.length;
                minResultSizeBuffer[writeEndPos++] = this.usedGuards[guardIndex];
                ++var18;
            }

            int halfLength = this.usedAlphabet.length / 2;

            while(var18 < this.minHashLength) {
                alphabet = consistentShuffle(alphabet, alphabet);
                int readFrontPos = 2 * halfLength - 1;

                int readEndPos;
                for(readEndPos = 0; writeFrontPos >= 0 && readFrontPos >= halfLength; ++var18) {
                    minResultSizeBuffer[writeFrontPos--] = alphabet[readFrontPos--];
                }

                while(writeEndPos < minResultSizeBuffer.length && readEndPos < halfLength) {
                    minResultSizeBuffer[writeEndPos++] = alphabet[readEndPos++];
                    ++var18;
                }
            }

            var17 = minResultSizeBuffer;
        }

        return new String(var17);
    }

    public long[] decode(String input) {
        return input != null && !input.isEmpty()?this._decode(input):new long[0];
    }

    private long[] _decode(String input) {
        Hashes hashes = this.getHashes(input);
        if(hashes == null) {
            return new long[0];
        } else {
            char[] alphabet = Arrays.copyOf(this.usedAlphabet, this.usedAlphabet.length);
            long[] result = new long[hashes.hashes.length];
            char[] buffer = new char[1 + this.usedSalt.length + alphabet.length];
            System.arraycopy(this.usedSalt, 0, buffer, 1, this.usedSalt.length);
            buffer[0] = hashes.lottery;

            for(int i = 0; i < hashes.hashes.length; ++i) {
                System.arraycopy(alphabet, 0, buffer, 1 + this.usedSalt.length, alphabet.length);
                alphabet = consistentShuffle(alphabet, Arrays.copyOfRange(buffer, 0, alphabet.length));
                result[i] = unhash(hashes.hashes[i].toCharArray(), alphabet);
            }

            return result;
        }
    }

    private Hashes getHashes(String input) {
        Matcher matcher = this.guardPattern.matcher(input);
        if(matcher.matches()) {
            String[] split = this.splitPattern.split(matcher.group(2));
            if(split.length > 0) {
                char lottery = split[0].charAt(0);
                split[0] = split[0].substring(1);
                return new Hashes(lottery, split);
            }
        }

        return null;
    }

    private static char[] hash(long input, char[] alphabet) {
        StringBuilder resultBuilder = new StringBuilder();

        do {
            resultBuilder.append(alphabet[(int)(input % (long)alphabet.length)]);
            input /= (long)alphabet.length;
        } while(input != 0L);

        return resultBuilder.reverse().toString().toCharArray();
    }

    private static long unhash(char[] input, char[] alphabet) {
        long number = 0L;

        for(int i = 0; i < input.length; ++i) {
            int pos = findCharInCharArray(alphabet, input[i]);
            number += (long)pos * (long)Math.pow((double)alphabet.length, (double)(input.length - i - 1));
        }

        return number;
    }

    private static int findCharInCharArray(char[] charArray, char c) {
        for(int i = 0; i < charArray.length; ++i) {
            if(c == charArray[i]) {
                return i;
            }
        }

        return -1;
    }

    public static char[] consistentShuffle(char[] alphabet, char[] salt) {
        if(salt != null && salt.length != 0) {
            char[] resultAlphabet = Arrays.copyOf(alphabet, alphabet.length);
            char[] tempAlphabet = new char[resultAlphabet.length];
            int i = resultAlphabet.length - 1;
            int v = 0;

            for(int p = 0; i > 0; ++v) {
                v %= salt.length;
                p += salt[v];
                int j = (salt[v] + v + p) % i;
                tempAlphabet[j] = resultAlphabet[i];
                System.arraycopy(resultAlphabet, 0, tempAlphabet, 0, j);
                System.arraycopy(resultAlphabet, j + 1, tempAlphabet, j + 1, resultAlphabet.length - j - 1);
                resultAlphabet[i] = resultAlphabet[j];
                System.arraycopy(tempAlphabet, 0, resultAlphabet, 0, i);
                System.arraycopy(tempAlphabet, i + 1, resultAlphabet, i + 1, tempAlphabet.length - i - 1);
                --i;
            }

            return resultAlphabet;
        } else {
            return alphabet;
        }
    }

    public String getVersion() {
        return "1.0.1";
    }

    private static class Hashes {
        private final char lottery;
        private final String[] hashes;

        private Hashes(char lottery, String[] hashes) {
            this.lottery = lottery;
            this.hashes = hashes;
        }
    }
}
