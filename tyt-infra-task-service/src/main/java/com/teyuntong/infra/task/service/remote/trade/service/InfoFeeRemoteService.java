package com.teyuntong.infra.task.service.remote.trade.service;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.trade.service.client.infofee.service.InfoFeeRpcService;
import com.teyuntong.trade.service.client.orders.service.OrdersRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
* 添加 InfoFeeRpcService Remote
* <AUTHOR>
* @since 2024/7/25 15:39
*/
@Service
@FeignClient(name = "tyt-trade-service", path = "trade", contextId = "infoFeeRpcService", fallbackFactory = InfoFeeRemoteService.InfoFeeFallbackFactory.class)
public interface InfoFeeRemoteService extends InfoFeeRpcService {

    @Component
    class InfoFeeFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<InfoFeeRemoteService> {
        protected InfoFeeFallbackFactory() {
            super(true, InfoFeeRemoteService.class);
        }
    }
}
