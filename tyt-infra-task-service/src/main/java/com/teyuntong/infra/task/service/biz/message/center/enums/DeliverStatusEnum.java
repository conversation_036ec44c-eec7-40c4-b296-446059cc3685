package com.teyuntong.infra.task.service.biz.message.center.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * 消息的处理状态
 * @date 2024/8/19 15:43
 */
public enum DeliverStatusEnum {
    UNDO((byte) 1, "未处理"),
    FINISHED((byte) 2, "已处理"),
    FAILED((byte) 3, "处理失败"),
    DEAD((byte) 4, "多次失败死信"),

    ;

    @Getter
    private Byte code;

    @Getter
    private String zhName;

    private DeliverStatusEnum(Byte code, String zhName) {
        this.code = code;
        this.zhName = zhName;
    }

}
