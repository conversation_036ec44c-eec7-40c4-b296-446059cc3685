package com.teyuntong.infra.task.service.remote.commonapi.api.bean;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Builder
@Data
public class AutoCallTaskRequest {

    /**
     * 电话内容
     */
    @JsonProperty("taskCallValue")
    private String taskCallValue;

    /**
     * 任务名称
     */
    @JsonProperty("taskName")
    private String taskName;

    /**
     * 手机号List
     */
    @JsonProperty("callTelList")
    private List<String> callTelList;

}