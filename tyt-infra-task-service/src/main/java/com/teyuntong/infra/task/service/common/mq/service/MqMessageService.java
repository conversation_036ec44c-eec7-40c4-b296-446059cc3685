package com.teyuntong.infra.task.service.common.mq.service;

import com.teyuntong.infra.task.service.common.mq.mybatis.entity.MqMessageDO;
import com.teyuntong.infra.task.service.common.mq.pojo.MqBaseMessageBean;
import com.teyuntong.infra.task.service.common.mq.pojo.MqInfoFeeOperateMsg;

import java.util.List;

/**
 * 消息中心生产者
 */
public interface MqMessageService {

    void saveAndSendMqMessage(MqBaseMessageBean messageBean);

    /**
     * 发送特运通业务消息(只发送消息，不入库)
     *
     * <AUTHOR>
     * @param messageBean 消息对象
     * @return void
     */
    void sendTytMqMessage(MqMessageDO messageBean, Long delayTime);

    /**
     * 发送满帮开放平台消息(只发送消息，不入库)
     *
     * <AUTHOR>
     * @param messageBean 消息对象
     * @return void
     */
    void sendMbMqMessage(MqMessageDO messageBean, Long delayTime);

    /**
     * 根据状态获取消息列表
     *
     * <AUTHOR>
     * @param mqStatus 消息状态
     * @return java.util.List<com.teyuntong.infra.task.service.common.mq.mybatis.entity.MqMessageDO>
     */
    List<MqMessageDO> getMqMessageByStatus(Integer mqStatus, String startTime, String endTime);

    /**
     * 修改发送次数+1
     *
     * <AUTHOR>
     * @param messageSerailNum 消息序列号
     * @return void
     */
    void updateMqMessageSendNbr(String messageSerailNum);

    /**
     * 修改消息状态
     *
     * <AUTHOR>
     * @param messageSerailNum 消息序列号
     * @param mqMessageStatus 消息状态
     * @return void
     */
    void updateMqMessageStatus(String messageSerailNum, Integer mqMessageStatus);

    void saveAndSendMqMessage(MqInfoFeeOperateMsg mqInfoFeeOperateMsg, Integer thirdpartyPlatformType) ;



    void updateDealFailMqMessage(int messageType,String startTime, String endTime);


    /**

    /**
     * 保存消息
     * @param t
     * @param messageType
     * @return
     */
    <T> MqMessageDO saveMqMessage(T t,Integer messageType);

    /**
     * 发送消息
     * @return
     */
    <T> void sendMessage(T t, String messageSerialNum, long delayTime);


    <T> void sendMessageForCenter(T t, String topic, String tag, String messageSerialNum, long delayTime);
}
