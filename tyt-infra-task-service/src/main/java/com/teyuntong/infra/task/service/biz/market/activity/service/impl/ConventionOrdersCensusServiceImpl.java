package com.teyuntong.infra.task.service.biz.market.activity.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.teyuntong.infra.task.service.biz.goods.callphone.service.AppCallLogService;
import com.teyuntong.infra.task.service.biz.market.activity.constant.MarketConstant;
import com.teyuntong.infra.task.service.biz.market.activity.dto.ConventionRank;
import com.teyuntong.infra.task.service.biz.market.activity.dto.ExposureUsedCountBean;
import com.teyuntong.infra.task.service.biz.market.activity.dto.UserOrdersBean;
import com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.*;
import com.teyuntong.infra.task.service.biz.market.activity.mybatis.mapper.ConventionOrdersCensusMapper;
import com.teyuntong.infra.task.service.biz.market.activity.service.*;
import com.teyuntong.infra.task.service.biz.trade.infofee.service.TransportOrdersRiskService;
import com.teyuntong.infra.task.service.biz.trade.infofee.service.TransportOrdersService;
import com.teyuntong.infra.task.service.biz.user.info.mybatis.entity.ApiDataUserCreditInfoTwoDO;
import com.teyuntong.infra.task.service.biz.user.info.mybatis.entity.User;
import com.teyuntong.infra.task.service.biz.user.info.service.ApiDataUserCreditInfoTwoService;
import com.teyuntong.infra.task.service.biz.user.info.service.UserService;
import com.teyuntong.infra.task.service.biz.user.permission.pojo.PermissionChangeType;
import com.teyuntong.infra.task.service.biz.user.permission.pojo.PermissionGainTypeNewEnum;
import com.teyuntong.infra.task.service.biz.user.permission.service.ExposurePermissionUsedRecordService;
import com.teyuntong.infra.task.service.biz.user.permission.service.UserPermissionService;
import com.teyuntong.infra.task.service.remote.tytconfig.service.TytConfigRemoteService;
import com.teyuntong.infra.task.service.remote.user.service.UserPermissionRemoteService;
import com.teyuntong.infra.task.service.utils.TimeUtil;
import com.teyuntong.user.service.client.permission.dto.GainPermissionRpcDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 履约活动统计表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-22
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class ConventionOrdersCensusServiceImpl implements ConventionOrdersCensusService {

    private final TytConfigRemoteService tytConfigRemoteService;

    private final MarketingActivityService marketingActivityService;

    private final TransportOrdersRiskService transportOrdersRiskService;

    private final UserPermissionRemoteService userPermissionRemoteService;

    private final ConventionOrdersCensusMapper conventionOrdersCensusMapper;

    private final ApiDataUserCreditInfoTwoService apiDataUserCreditInfoTwoService;

    private final MarketingActivityChargeOrderService marketingActivityChargeOrderService;

    private final MarketingActivityUserTargetNumService marketingActivityUserTargetNumService;

    private final MarketingConventionOrdersCensusService marketingConventionOrdersCensusService;

    private final ConventionActivityPrizeService conventionActivityPrizeService;

    private final UserPermissionService userPermissionService;

    private final ExposurePermissionUsedRecordService exposurePermissionUsedRecordService;

    private final TransportOrdersService transportOrdersService;

    private final MarketingConventionCarouselService marketingConventionCarouselService;

    private final UserService userService;

    private final AppCallLogService appCallLogService;

    private static final String ROUND = "round";
    private static final String STARTTIME = "startTime";
    private static final String ENDTIME = "endTime";

    @Override
    public void beginNewRank() {
        Date now = new Date();
        //查询开启的冲单奖
        for (MarketingActivityDO marketingActivityDO : marketingActivityService.getByType(24)) {
            //查询需要开启新阶段的配置
            for (MarketingActivityChargeOrderDO chargeOrderDO : marketingActivityChargeOrderService.getByActivityId(marketingActivityDO.getId())) {
                Date startTime = chargeOrderDO.getStartTime();
                Date yesterday = TimeUtil.addDay(startTime, -1);

                //是同一天。表示需要开启下一阶段
                if (DateUtil.isSameDay(now, yesterday)) {
                    //轮次
                    Integer round = chargeOrderDO.getRound();

                    Date firstDayOfMonth = TimeUtil.firstDayOfMonth(new Date(), -1);
                    Date lastDayOfMonth = TimeUtil.lastDayOfMonth(new Date(), -1);
                    Long indexId = 0L;
                    while (true) {
                        List<MarketingActivityUserTargetNumDO> activityUserTargetNumDOS = marketingActivityUserTargetNumService.getByActivityId(marketingActivityDO.getId(), indexId);
                        if (!activityUserTargetNumDOS.isEmpty()) {
                            for (MarketingActivityUserTargetNumDO targetNumDO : activityUserTargetNumDOS) {
                                indexId = targetNumDO.getId();
                                try {
                                    //判断用户上阶段是否参与
                                    ConventionOrdersCensusDO lastOrderCensusDo = conventionOrdersCensusMapper.getByActivityId(marketingActivityDO.getId(), targetNumDO.getActivityUserId(), round - 1);
                                    if (ObjectUtil.isNotNull(lastOrderCensusDo)) {
                                        //查询用户等级
                                        ApiDataUserCreditInfoTwoDO userCreditInfoTwoDO = apiDataUserCreditInfoTwoService.getByUserId(targetNumDO.getActivityUserId());
                                        if (ObjectUtil.isNotNull(userCreditInfoTwoDO)) {
                                            Integer rankLevel = userCreditInfoTwoDO.getRankLevel();
                                            rankLevel = ObjectUtil.isNotNull(rankLevel) ? rankLevel : 0;
                                            //查询符合条件的轮次奖品配置
                                            MarketingConventionOrdersCensusDO ordersCensusDO = getOrderCensus(marketingActivityDO.getId(),
                                                    targetNumDO.getActivityUserId(), rankLevel, firstDayOfMonth, lastDayOfMonth);
                                            if (ObjectUtil.isNotNull(ordersCensusDO)) {
                                                //更新目标奖品值
                                                updateTargetNum(targetNumDO.getId(), ordersCensusDO, round, now);
                                                //创建下一阶段统计数据
                                                saveCensus(marketingActivityDO.getId(), targetNumDO.getActivityUserId(), round, ordersCensusDO, now);
                                            }
                                        }
                                    }
                                } catch (Exception e) {
                                    log.error("beginNewRank error:", e);
                                }
                            }
                        } else {
                            break;
                        }
                    }
                }
            }
        }
    }

    @Override
    public void issueRankReward() {
        Date now = new Date();
        //查询开启的冲单奖
        for (MarketingActivityDO marketingActivityDO : marketingActivityService.getByType(24)) {
            //查询需要开启新阶段的配置
            for (MarketingActivityChargeOrderDO chargeOrderDO : marketingActivityChargeOrderService.getByActivityId(marketingActivityDO.getId())) {
                Date endTime = chargeOrderDO.getEndTime();
                Date tomorrow = TimeUtil.addDay(endTime, 1);

                //是同一天。表示需要开始发奖
                if (DateUtil.isSameDay(now, tomorrow)) {
                    //轮次
                    Integer round = chargeOrderDO.getRound();

                    Long indexId = 0L;
                    while (true) {
                        List<ConventionOrdersCensusDO> ordersCensusDOS = conventionOrdersCensusMapper.getByUserIdActivityId(marketingActivityDO.getId(), round, indexId);
                        if (!ordersCensusDOS.isEmpty()) {
                            for (ConventionOrdersCensusDO censusDO : ordersCensusDOS) {
                                indexId = censusDO.getId();
                                try {
                                    //查询活动目标信息
                                    MarketingActivityUserTargetNumDO targetNumDO = marketingActivityUserTargetNumService.getByActivityUserId(marketingActivityDO.getId(), censusDO.getUserId());
                                    if (ObjectUtil.isNotNull(targetNumDO)) {
                                        //比较对应阶段单量是否达标
                                        Integer roundTimes = censusDO.getRoundTimes();
                                        boolean isQualify = false;
                                        if (roundTimes == 1) {
                                            if (ObjectUtil.isNotNull(censusDO.getOrdersNum())
                                                    && ObjectUtil.isNotNull(targetNumDO.getOneTargetNum())
                                                    && censusDO.getOrdersNum() >= targetNumDO.getOneTargetNum()) {
                                                isQualify = true;
                                            }
                                        } else if (roundTimes == 2) {
                                            if (ObjectUtil.isNotNull(censusDO.getOrdersNum())
                                                    && ObjectUtil.isNotNull(targetNumDO.getTwoTargetNum())
                                                    && censusDO.getOrdersNum() >= targetNumDO.getTwoTargetNum()) {
                                                isQualify = true;
                                            }
                                        } else if (roundTimes == 3) {
                                            if (ObjectUtil.isNotNull(censusDO.getOrdersNum())
                                                    && ObjectUtil.isNotNull(targetNumDO.getThreeTargetNum())
                                                    && censusDO.getOrdersNum() >= targetNumDO.getThreeTargetNum()) {
                                                isQualify = true;
                                            }
                                        } else if (roundTimes == 4) {
                                            if (ObjectUtil.isNotNull(censusDO.getOrdersNum())
                                                    && ObjectUtil.isNotNull(targetNumDO.getFourTargetNum())
                                                    && censusDO.getOrdersNum() >= targetNumDO.getFourTargetNum()) {
                                                isQualify = true;
                                            }
                                        }
                                        if (isQualify) {
                                            //发放奖励
                                            GainPermissionRpcDTO gainPermissionRpcDTO = new GainPermissionRpcDTO();
                                            gainPermissionRpcDTO.setUserId(censusDO.getUserId());
                                            gainPermissionRpcDTO.setGoodsId(censusDO.getQualifyGoodsId());
                                            gainPermissionRpcDTO.setGainType(8);
                                            gainPermissionRpcDTO.setSendType(0);
                                            gainPermissionRpcDTO.setChangeType(8);
                                            gainPermissionRpcDTO.setActivityId(censusDO.getActivityId());
                                            userPermissionRemoteService.gainPermission(gainPermissionRpcDTO);

                                            //更新发奖状态
                                            ConventionOrdersCensusDO updateOrdersCensusDo = new ConventionOrdersCensusDO();
                                            updateOrdersCensusDo.setId(censusDO.getId());
                                            updateOrdersCensusDo.setStatus(2);
                                            updateOrdersCensusDo.setMtime(now);
                                            conventionOrdersCensusMapper.updateById(updateOrdersCensusDo);
                                        }
                                    }
                                } catch (Exception e) {
                                    log.error("issueRankReward error:", e);
                                }
                            }
                        } else {
                            break;
                        }
                    }
                }
            }
        }
    }

    @Override
    public void givePermissionForConvention() {
        Integer isConventionOrdersCensusOn = tytConfigRemoteService.getIntValue(MarketConstant.CONVENTION_ORDERS_CENSUS_TASK_ONOFF, 1);
        if (isConventionOrdersCensusOn == null || isConventionOrdersCensusOn!=1){
            log.info("老冲单奖活动开关未打开");
            return;
        }
        String allGtvActivityId = tytConfigRemoteService.getStringValue(MarketConstant.EXPOSE_ALL_ACTIVITY_ID_NEW); //冲单活动id
        if (StringUtils.isBlank(allGtvActivityId) || Long.parseLong(allGtvActivityId)<=0){
            return ;
        }
        MarketingActivityDO activity = marketingActivityService.getById(Long.parseLong(allGtvActivityId));
        if (activity == null || activity.getStatus() == 2){ //活动停用的话，不执行
            return;
        }
        Date endTimes = TimeUtil.addDay(activity.getEndTime(),1);
        if (new Date().after(endTimes)){ //如果当前时间大于活动结束时间+1天，（活动结束后凌晨发奖,所以往后延一天）
            return;
        }
        String roundTimeConfig = tytConfigRemoteService.getStringValue(MarketConstant.ACTIVITY_CONVENTION_ROUNDTIME_JSON);
        if (StringUtils.isNotBlank(roundTimeConfig)) {
            JSONArray roundArray = JSON.parseArray(roundTimeConfig);
            roundArray.sort(Comparator.comparing(st ->((JSONObject)st).getString(ROUND)));
            Date now = new Date();
            int round = 0;
            for (int i=0;i<roundArray.size();i++) {
                JSONObject r = roundArray.getJSONObject(i);
                Date startTime = r.getDate(STARTTIME);
                Date endTime = r.getDate(ENDTIME);
                if (startTime.before(now) && endTime.after(now)) {
                    round = r.getIntValue(ROUND)-1;
                    break;
                }
                if (round==0
                        && i==(roundArray.size()-1)
                        && endTime.before(now)) {
                    round = roundArray.size();
                }
            }
            log.info("givePermission4ConventionOrdersCensus 老冲单奖排名和发奖定时任务 目前活动场次："+round);
            if (round>0) {
                this.giveExposurePermission(Long.parseLong(allGtvActivityId),round);
            }
        }
    }

    @Override
    public void conventionCarousel() {
        Date date = TimeUtil.addDay(-1);
        Date startTime = TimeUtil.weeHours(date,0);
        Date endTime = TimeUtil.weeHours(date,1);
        int count = 0;
        //获取前一日曝光卡使用数量
        List<ExposureUsedCountBean> usedBean = exposurePermissionUsedRecordService.getUsedCount(startTime, endTime);
        for (ExposureUsedCountBean bean : usedBean) {
            //获取用户前一日单量和订单金额
            UserOrdersBean ordersBean = transportOrdersService.getUserOrdersCountAndAmount(bean.getUserId(), startTime, endTime);
            if (ordersBean == null || ordersBean.getOrderNum() <=0){
                continue;
            }
            MarketingConventionCarouselDO carouselDO = new MarketingConventionCarouselDO();
            carouselDO.setUserId(bean.getUserId());
            User user = userService.getByUserId(bean.getUserId());
            if (StringUtils.isNotBlank(user.getTrueName())){
                carouselDO.setName(user.getTrueName().charAt(0) + "老板");
            }else {
                carouselDO.setName("王老板");
            }
            carouselDO.setOrdersCount(ordersBean.getOrderNum());
            carouselDO.setOrdersFeeCount(ordersBean.getAmountSum().intValue()/100);
            carouselDO.setExposureUsedCount(bean.getUsedCount());
            carouselDO.setCallCount(appCallLogService.getYesterdayIncomingLines(bean.getUserId()));
            carouselDO.setCreateTime(new Date());
            carouselDO.setModifyTime(new Date());
            count ++;
            marketingConventionCarouselService.saveCarousel(carouselDO);
            if (count>=50){
                return;
            }
        }


    }

    private void giveExposurePermission(long activityId, int round) {
        List<ConventionActivityPrizeDO> qualifyGoods = conventionActivityPrizeService.getByActivityId(activityId,1);
        List<ConventionActivityPrizeDO> rankGoods = conventionActivityPrizeService.getByActivityId(activityId,2);
        List<ConventionRank> rank4ConventionOrdersCensus = conventionOrdersCensusMapper.getRank4ConventionOrdersCensus(round,activityId);
        log.info("givePermission4ConventionOrdersCensus 曝光活动排名和发奖定时任务 获取排名数据个数："+rank4ConventionOrdersCensus.size());
        rank4ConventionOrdersCensus.forEach(r -> {
            try {
                long qualifyGoodsId = 0L;
                String qualifyGoodsName = "未中奖";
                for (ConventionActivityPrizeDO qualifyGood : qualifyGoods) {
                    if (r.getOrdersNum()>=qualifyGood.getMinNums() && r.getOrdersNum()<=qualifyGood.getMaxNums()){
                        qualifyGoodsId = qualifyGood.getGoodsId();
                        qualifyGoodsName = qualifyGood.getGoodsName();
                        break;
                    }
                }
                if (qualifyGoodsId>0){
                    userPermissionService.givePermission(r.getUserId(),qualifyGoodsId,"task-zengsong",activityId, PermissionGainTypeNewEnum.活动获取, PermissionChangeType.赠送);
                }
                long rankGoodsId = 0L;
                String rankGoodsName = "未中奖";
                for (ConventionActivityPrizeDO rankGood : rankGoods) {
                    if (r.getRank()>=rankGood.getMinNums() && r.getRank()<=rankGood.getMaxNums()){
                        rankGoodsId = rankGood.getGoodsId();
                        rankGoodsName = rankGood.getGoodsName();
                        break;
                    }
                }
                if (rankGoodsId>0){
                    Thread.sleep(1000);
                    userPermissionService.givePermission(r.getUserId(),rankGoodsId,"task-zengsong",activityId, PermissionGainTypeNewEnum.活动获取, PermissionChangeType.赠送);
                }
                //修改发奖状态
                conventionOrdersCensusMapper.updateStatus(r.getRank(),qualifyGoodsId,qualifyGoodsName,rankGoodsId,rankGoodsName,r.getUserId(),round,activityId);
            } catch (Exception e) {
                log.error("givePermission4ConventionOrdersCensus 曝光活动排名和发奖定时任务异常，" ,e);
            }
        });
    }

    private MarketingConventionOrdersCensusDO getOrderCensus(Long activityId, Long userId, Integer rankLevel,
                                                             Date firstDayOfMonth, Date lastDayOfMonth) {
        int stage = 1;
        String stringValue = tytConfigRemoteService.getStringValue(MarketConstant.SPRINT_ORDER_ACTIVITY_ADDITIONAL_NUMBER);
        if (StringUtils.isNotBlank(stringValue)) {
            String[] split = stringValue.split(",");

            String minNum = tytConfigRemoteService.getStringValue(MarketConstant.SPRINT_ORDER_ACTIVITY_ORDER_MIN_NUM, "15,5,1");

            String[] minSplit = minNum.split(",");
            //根据履约单量适配阶段
            int honourOrderCount = transportOrdersRiskService.getHonourOrderCount(userId, firstDayOfMonth, lastDayOfMonth);
            if (rankLevel == 5) {
                if (honourOrderCount >= Integer.parseInt(minSplit[0]) && honourOrderCount < Integer.parseInt(split[0])) {
                    stage = 2;
                } else if (honourOrderCount < Integer.parseInt(minSplit[0])) {
                    stage = 3;
                }
            } else if (rankLevel == 4) {
                if (honourOrderCount >= Integer.parseInt(minSplit[1]) && honourOrderCount < Integer.parseInt(split[1])) {
                    stage = 2;
                } else if (honourOrderCount < Integer.parseInt(minSplit[1])) {
                    stage = 3;
                }
            } else if(rankLevel == 3){
                if (honourOrderCount < Integer.parseInt(minSplit[2])) {
                    stage = 2;
                }
            }
            //查询用户所在的奖励档
            return marketingConventionOrdersCensusService.getActivityLevel(activityId, rankLevel, stage);
        }
        return null;
    }

    private void updateTargetNum(Long id, MarketingConventionOrdersCensusDO ordersCensusDO, Integer round, Date now) {
        //根据等级计算目标单量
        MarketingActivityUserTargetNumDO targetNumDO = new MarketingActivityUserTargetNumDO();
        targetNumDO.setId(id);
        String grade = ordersCensusDO.getLevel() + "-" + ordersCensusDO.getStage();
        if (round == 1) {
            targetNumDO.setOneGrade(grade);
            targetNumDO.setOnePrizeNum(ordersCensusDO.getOnePrize());
        } else if (round == 2) {
            targetNumDO.setTwoGrade(grade);
            targetNumDO.setTwoPrizeNum(ordersCensusDO.getTwoPrize());
        } else if (round == 3) {
            targetNumDO.setThreeGrade(grade);
            targetNumDO.setThreePrizeNum(ordersCensusDO.getThreePrize());
        } else if (round == 4) {
            targetNumDO.setFourGrade(grade);
            targetNumDO.setFourPrizeNum(ordersCensusDO.getFourPrize());
        }
        targetNumDO.setModifyTime(now);
        marketingActivityUserTargetNumService.updateTargetNum(targetNumDO);
    }

    private void saveCensus(Long activityId, Long userId, Integer round,
                            MarketingConventionOrdersCensusDO ordersCensusDO, Date now) {
        ConventionOrdersCensusDO byActivityId = conventionOrdersCensusMapper.getByActivityId(activityId, userId, round);
        if (ObjectUtil.isNotNull(byActivityId)) {
            return;
        }
        Long goodsId = 0L;
        if (round == 1) {
            goodsId = ordersCensusDO.getOneGoodsId();
        } else if (round == 2) {
            goodsId = ordersCensusDO.getTwoGoodsId();
        } else if (round == 3) {
            goodsId = ordersCensusDO.getThreeGoodsId();
        } else if (round == 4) {
            goodsId = ordersCensusDO.getFourGoodsId();
        }
        ConventionOrdersCensusDO insertDo = new ConventionOrdersCensusDO();
        insertDo.setActivityId(activityId);
        insertDo.setUserId(userId);
        insertDo.setRoundTimes(round);
        insertDo.setOrdersNum(0);
        insertDo.setQualifyGoodsId(goodsId);
        insertDo.setStatus(1);
        insertDo.setCtime(now);
        insertDo.setMtime(now);
        conventionOrdersCensusMapper.insert(insertDo);
    }
}
