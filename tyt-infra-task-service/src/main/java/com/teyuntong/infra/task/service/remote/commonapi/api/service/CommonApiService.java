package com.teyuntong.infra.task.service.remote.commonapi.api.service;


import com.teyuntong.infra.task.service.remote.commonapi.api.bean.AutoCallTaskRequest;
import com.teyuntong.infra.task.service.remote.commonapi.api.bean.CDRReq;
import com.teyuntong.infra.task.service.remote.commonapi.api.bean.CDRsResp;
import com.teyuntong.infra.task.service.remote.commonapi.api.bean.PhoneLocaleResp;

/**
 * CommonApi 调用
 */
public interface CommonApiService extends BaseApiService {

    CDRsResp getCDR(CDRReq req);

    void autoDeleteAutoCallTask();

    PhoneLocaleResp mobile(String mobile);

    /**
     * AI自动外呼
     * @param autoCallTaskRequest
     */
    Object autoCallTask(AutoCallTaskRequest autoCallTaskRequest);
    String getRecordUrl(String recordName, String callId);

}
