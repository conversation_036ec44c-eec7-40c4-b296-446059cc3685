package com.teyuntong.infra.task.service.remote.userTrase.service;

import com.teyuntong.user.trace.client.order.trace.OrderTraceRpcService;
import com.teyuntong.user.trace.client.order.trace.dto.OrderTraceDTO;
import com.teyuntong.user.trace.client.order.trace.dto.OrderTraceSaveDTO;
import com.teyuntong.user.trace.client.order.trace.vo.OrderTraceRpcVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
@FeignClient(name = "tyt-user-trace", path = "trace", contextId = "orderTraceRpcService", fallbackFactory = UserTraceRemoteService.UserTraceRemoteServiceFallbackFactory.class)
public interface UserTraceRemoteService extends OrderTraceRpcService {
    @Slf4j
    @Component
    class UserTraceRemoteServiceFallbackFactory implements FallbackFactory<UserTraceRemoteService> {

        @Override
        public UserTraceRemoteService create(Throwable cause) {
            return new UserTraceRemoteService() {

                @Override
                public List<OrderTraceRpcVo> getOrderTrace(OrderTraceDTO orderTraceDTO) {
                    log.error(cause.getMessage());
                    return null;
                }

                @Override
                public List<OrderTraceRpcVo> getAllTrace(Long orderId) {
                    log.error(cause.getMessage());
                    return null;
                }

                @Override
                public void saveOrderTrace(OrderTraceSaveDTO saveDto) {
                    log.error(cause.getMessage());
                }
            };
        }
    }
}
