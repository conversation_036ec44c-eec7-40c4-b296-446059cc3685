package com.teyuntong.infra.task.service.common.enums;

public enum PublishTypeEnum {
    /**
     * 电议
     */
    TEL(1, "tel"),
    /**
     * 一口价
     */
    BUYOUT(2, "buyout");

    private Integer publishType;

    private String publishTypeMsg;

    PublishTypeEnum(Integer publishType, String publishTypeMsg) {
        this.publishType = publishType;
        this.publishTypeMsg = publishTypeMsg;
    }

    public static String getPublishTypeMsgByType(Integer type) {
        for (PublishTypeEnum val : PublishTypeEnum.values()) {
            if (val.getPublishType() == type) {
                return val.getPublishTypeMsg();
            }
        }
        return null;
    }

    public Integer getPublishType() {
        return publishType;
    }

    public String getPublishTypeMsg() {
        return publishTypeMsg;
    }
}
