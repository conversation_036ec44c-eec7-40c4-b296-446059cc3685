package com.teyuntong.infra.task.service.biz.trade.infofee.service.impl;

import com.alibaba.fastjson.JSON;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.infra.task.service.biz.trade.enums.TytTradeErrorCode;
import com.teyuntong.infra.task.service.biz.trade.infofee.entity.TransportOrdersDO;
import com.teyuntong.infra.task.service.biz.trade.infofee.entity.TransportOrdersInvoiceDO;
import com.teyuntong.infra.task.service.biz.trade.infofee.mapper.TransportOrdersInvoiceMapper;
import com.teyuntong.infra.task.service.biz.trade.infofee.service.TransportOrdersInvoiceService;
import com.teyuntong.infra.task.service.biz.trade.infofee.service.TransportOrdersService;
import com.teyuntong.infra.task.service.remote.enterprise.WJOpenApiRemoteService;
import com.teyuntong.infra.task.service.remote.user.service.ThirdEnterpriseRemoteService;
import com.teyuntong.outer.export.service.client.invoice.hbwj.bean.WaybillThreeOffsetRequest;
import com.teyuntong.outer.export.service.client.invoice.hbwj.bean.WaybillThreeOffsetResp;
import com.teyuntong.trade.service.client.hbwj.resp.HBWJResponseStatusEnum;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 接单表 订单 开票订单 服务实现类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-06-19
 */

@Service
@Slf4j
public class TransportOrdersInvoiceServiceImpl implements TransportOrdersInvoiceService {

    @Autowired
    private TransportOrdersInvoiceMapper transportOrdersInvoiceMapper;

    @Autowired
    private TransportOrdersService transportOrdersService;

    @Autowired
    private ThirdEnterpriseRemoteService thirdEnterpriseRemoteService;

    @Autowired
    private WJOpenApiRemoteService wjOpenApiRemoteService;

    public void syncBatchToInvoice(List<TransportOrdersDO> batch) {
        if (batch == null || batch.isEmpty()) {
            return;
        }
        for (TransportOrdersDO order : batch) {
            TransportOrdersInvoiceDO invoiceOrder = transportOrdersInvoiceMapper.selectById(order.getId());
            if (invoiceOrder == null) {
                TransportOrdersInvoiceDO target = new TransportOrdersInvoiceDO();
                BeanUtils.copyProperties(order, target);
                // 插入新记录
                transportOrdersInvoiceMapper.insert(target);
            } else {
                BeanUtils.copyProperties(order, invoiceOrder);
                invoiceOrder.setThreeWaybillStatus(null);
                transportOrdersInvoiceMapper.updateById(invoiceOrder);
            }
        }
    }

    @Override
    public void syncTransportOrdersToInvoiceJob(Long maxId, Date timeAgo, Integer pageSize) {
        int count = 0;
        while (true) {
            // 获取最近5分钟内的订单数据
            List<TransportOrdersDO> ordersList = transportOrdersService.selectBatchByMinId(maxId, timeAgo, pageSize);
            if (ordersList == null || ordersList.isEmpty()) {
                log.info("syncTransportOrdersToInvoiceJo end sync  data num is {} ", count);
                return;
            }
            count += ordersList.size();
            // 每批处理
            syncBatchToInvoice(ordersList);
            maxId = ordersList.get(ordersList.size() - 1).getId();
            sleep("syncTransportOrdersToInvoiceJob 线程异常：");
        }

    }

    @Override
    public void syncThirdPartyOrderStatus(Long maxId, Date timeAgo, Integer pageSize) {
        Integer count = 0;
        while (true) {
            // 查询 three_waybill_status 不是 HAS_FINISH 或 HAS_CANCEL 的发票订单数据
            List<TransportOrdersInvoiceDO> ordersList = transportOrdersInvoiceMapper.selectUnfinishedOrders(maxId, timeAgo, pageSize);
            if (ordersList == null || ordersList.isEmpty()) {
                log.info("没有需要更新的三方运单状态");
                return;
            }
            count += ordersList.size();
            for (TransportOrdersInvoiceDO order : ordersList) {
                try {

                    String thirdWaybillStatus = queryThirdPartyStatus(order);
                    if (thirdWaybillStatus != null && !order.getThreeWaybillStatus().equals(thirdWaybillStatus)) {
                        // 更新状态到本地数据库
                        transportOrdersInvoiceMapper.updateInvoiceStatusById(order.getId(), thirdWaybillStatus);
                    }
                } catch (Exception e) {
                    log.error("调用三方接口失败，运单号: {}", order.getInvoiceThirdPartyNo(), e);
                }
                sleep("syncThirdPartyOrderStatus 线程异常：");
            }
            // 更新最大ID用于下一轮分页
            maxId = ordersList.get(ordersList.size() - 1).getId();
            log.info("syncThirdPartyOrderStatus 已处理 {} 条运单状态", count);
        }

    }

    /**
     * 线程休眠
     *
     * @param s 错误信息
     */
    private static void sleep(String s) {
        try {
            Thread.sleep(200);
        } catch (Exception e) {
            log.error(s, e);
        }
    }

    /**
     * 查询三方运单状态
     *
     * @param transportOrdersInvoiceDO 订单对象
     * @return 三方运单状态
     */
    @SneakyThrows
    private String queryThirdPartyStatus(TransportOrdersInvoiceDO transportOrdersInvoiceDO) {
        String invoiceThirdPartyNo = transportOrdersInvoiceDO.getInvoiceThirdPartyNo();
        if (StringUtils.isEmpty(transportOrdersInvoiceDO.getInvoiceThirdPartyNo())) {
            return "";

        }
        // 调用三方接口获取最新状态
        String userCode = thirdEnterpriseRemoteService.getUserCode(transportOrdersInvoiceDO.getUserId(), transportOrdersInvoiceDO.getInvoiceIssuerId()).getData().getUserCode();
        //查询支付单
        WaybillThreeOffsetRequest request = new WaybillThreeOffsetRequest();
        request.setWaybillCode(invoiceThirdPartyNo);
        request.setUserCode(userCode);
        WebResult<WaybillThreeOffsetResp> waybillThreeOffsetResult = wjOpenApiRemoteService.getWaybillThreeOffset(request);
        log.info("getWaybillThreeOffset orderId: {} res: {}", transportOrdersInvoiceDO.getId(), JSON.toJSONString(waybillThreeOffsetResult));
        if (!HBWJResponseStatusEnum.SUCCESS.getCode().toString().equals(waybillThreeOffsetResult.getCode())) {
            throw new BusinessException(TytTradeErrorCode.THIRD_INVOICE_REQUEST_REMARK_CAR);
        }
        return waybillThreeOffsetResult.getData().getWaybillStatus().getCode();
    }

}
