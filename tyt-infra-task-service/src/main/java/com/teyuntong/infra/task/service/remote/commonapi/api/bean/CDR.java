package com.teyuntong.infra.task.service.remote.commonapi.api.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CDR {

    /**
     * 接听时长
     */
    private String billDuration;

    /**
     * 接听时长(分钟)
     */
    private String billDurationMin;

    /**
     * 通话标识
     */
    private String callId;

    /**
     * 录音控制
     */
    private String callRecording;

    /**
     * 发起呼叫时间
     */
    private String callTime;

    /**
     * 呼叫类型
     */
    private String callType;

    /**
     * 通话记录创建时间
     */
    private String createTime;

    /**
     * 通话记录类型（callin\finish）
     */
    private String pushType;

    /**
     * 录音模式
     */
    private String recordMode;

    /**
     * 录音转存或转换文件名
     */
    private String recordName;

    /**
     * 录音地址
     */
    private String recordUrl;

    /**
     * 释放原因
     */
    private String releaseCause;

    /**
     * 释放方向
     */
    private String releaseDir;

    /**
     * 通话结束时间
     */
    private String releaseTime;

    /**
     * 每次请求唯一标识
     */
    private String requestId;

    /**
     * 通话状态（ANSWERED、BUSY、NO_ANSWER、REJECT等）
     */
    private String result;

    /**
     * 振铃开始时间
     */
    private String ringingTime;

    /**
     * 业务类型（AXB、AXYB、AXx）
     */
    private String serviceType;

    /**
     * 通话开始时间
     */
    private String startTime;

    /**
     * 绑定成功后返回的绑定关系id
     */
    private String subId;

    /**
     * telA
     */
    private String telA;

    /**
     * telB
     */
    private String telB;

    /**
     * telX
     */
    private String telX;

    /**
     * 总通话时长
     */
    private String totalDuration;

    /**
     * 额外的字段，以json格式的字符串来存储，在进行set方法时需传入json格式的字符串
     */
    private String userField;

    /**
     * 货源IDID
     */
    private Long srcMsgId;

    /**
     * 车方ID
     */
    private Long carUserId;

}