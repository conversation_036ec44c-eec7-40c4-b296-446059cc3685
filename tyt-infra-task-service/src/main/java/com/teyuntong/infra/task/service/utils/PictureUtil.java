package com.teyuntong.infra.task.service.utils;


import com.teyuntong.infra.task.service.biz.config.service.TytConfigService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/03/20 11:38
 */
@Component
@RequiredArgsConstructor
public class PictureUtil {


    private final TytConfigService tytConfigService;
    private static final String PREFIX_PICTURE = "prefix_picture";
    private static final String HTTP = "http";

    private static final String DEFAULT_URL = "http://newtest.teyuntong.net/rootdata";

    public String getPictureUrl(String pictureUrl) {
        if (StringUtils.isNotBlank(pictureUrl) && !pictureUrl.startsWith(HTTP)) {
            String serverUrl = tytConfigService.getStringValue("tyt_out_net_file_path_name_url", "https://api.teyuntong.net/manage_new/root_pic");
            return serverUrl + pictureUrl;
        }
        return pictureUrl;
    }
}
