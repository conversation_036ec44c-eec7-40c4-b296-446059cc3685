package com.teyuntong.infra.task.service.common.mq.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "mq-topic")
public class MessageTopicProperties {

    /**
     * 发送短信消息topic
     */
    private String messageCenterTopic;

    /**
     * 发送短信消息tag
     */
    private String messageCenterTag;

    /**
     * 常跑路线消息topic
     */
    private String oftenRouteTopic;
    /**
     * 常跑路线消息tag
     */
    private String oftenRouteTag;

    /**
     * 特运通mq消息topic
     */
    private String topic;
    /**
     * 特运通mq消息tag
     */
    private String tag;

    private String mbTopic;
    /**
     * 钉钉群通知topic
     */
    private String dingDingTopic;
    /**
     * 钉钉群通知tag
     */
    private String dingDingTag;


    /**
     * 满帮mq消息tag
     */
    private String mbTag;


    /**
     * 特运通订单消息topic
     */
    private String tytOrderTopic;

    /**
     * 特运通订单消息tag
     */
    private String tytOrderTag;

    /**
     * 满帮订单消息topic
     */
    private String mbOrderTopic;

    /**
     * 满帮订单消息tag
     */
    private String mbOrderTag;

}