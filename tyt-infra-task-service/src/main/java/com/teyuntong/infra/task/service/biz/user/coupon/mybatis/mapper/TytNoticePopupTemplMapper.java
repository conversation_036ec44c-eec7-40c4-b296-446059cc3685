package com.teyuntong.infra.task.service.biz.user.coupon.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.infra.task.service.biz.user.coupon.mybatis.entity.TytNoticePopupTempl;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 弹窗模板表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-15
 */
@Mapper
public interface TytNoticePopupTemplMapper extends BaseMapper<TytNoticePopupTempl> {

    TytNoticePopupTempl getByType(@Param("type1") int type1,@Param("type2") int type2);
}
