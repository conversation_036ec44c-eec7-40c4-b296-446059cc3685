package com.teyuntong.infra.task.service.common.config;

import com.teyuntong.infra.common.retrofit.interceptor.LogInfoInterceptor;
import okhttp3.OkHttpClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2024/01/17 11:25
 */
@Configuration
public class OkHttpClientConfig {

    @Bean
    public OkHttpClient.Builder builder() {
        return new OkHttpClient.Builder().addInterceptor(new LogInfoInterceptor());
    }
}
