package com.teyuntong.infra.task.service.biz.market.activity.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.MarketingActivityUserTargetNumDO;
import com.teyuntong.infra.task.service.biz.market.activity.dto.ActivityGradeBean;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 冲单活动用户目标单量 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-22
 */
@Mapper
public interface MarketingActivityUserTargetNumMapper extends BaseMapper<MarketingActivityUserTargetNumDO> {

    List<MarketingActivityUserTargetNumDO> getByActivityId(@Param("activityId") Long activityId, @Param("id") Long id);

    MarketingActivityUserTargetNumDO getByActivityUserId(@Param("activityId") Long activityId, @Param("userId") Long userId);

    List<ActivityGradeBean> getActivityUser(@Param("userIds") List<Long> userIds, @Param("activityId") Long activityId);
}
