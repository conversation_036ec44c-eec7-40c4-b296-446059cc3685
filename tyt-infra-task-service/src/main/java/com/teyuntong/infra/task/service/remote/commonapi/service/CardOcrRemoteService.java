package com.teyuntong.infra.task.service.remote.commonapi.service;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.inner.export.service.client.commonapi.service.CardOcrRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/08/13 14:14
 */
@Service
@FeignClient(name = "tyt-inner-export-service", path = "inner-export", contextId = "cardOcrRpcService", fallbackFactory = CardOcrRemoteService.CardOcrRemoteServiceFallbackFactory.class)
public interface CardOcrRemoteService extends CardOcrRpcService {
    @Component
    class CardOcrRemoteServiceFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<CardOcrRpcService> {
        protected CardOcrRemoteServiceFallbackFactory() {
            super(true, CardOcrRpcService.class);
        }
    }
}
