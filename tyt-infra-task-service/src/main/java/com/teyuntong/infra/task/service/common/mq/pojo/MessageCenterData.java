package com.teyuntong.infra.task.service.common.mq.pojo;

import com.teyuntong.infra.task.service.biz.user.car.pojo.ShortMessageBean;
import lombok.Data;

import java.util.UUID;

@Data
public class MessageCenterData extends MessageCenterBaseVo {

    /** 短信 **/
    private ShortMessageBean shortMessage;

    /** 通知 **/
    private NotifyMessagePush notifyMessage;

    /** 消息 **/
    private NewsMessagePush newsMessage;

    public MessageCenterData() {
        this.messageSerialNum = this.getUUID();
    }
    public MessageCenterData(String suffix) {
        String uuid = this.getUUID();
        this.messageSerialNum = uuid + suffix;
    }

    public MessageCenterData(String suffix, ShortMessageBean shortMessage,
                             NewsMessagePush newsMessage, NotifyMessagePush notifyMessage) {
        String uuid = this.getUUID();
        this.messageSerialNum = uuid + suffix;

        this.shortMessage = shortMessage;
        this.newsMessage = newsMessage;
        this.notifyMessage = notifyMessage;
    }

    private String getUUID() {
        return UUID.randomUUID().toString();
    }

}
