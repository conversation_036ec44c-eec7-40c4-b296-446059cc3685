package com.teyuntong.infra.task.service.common.sequence.service.impl;

import com.teyuntong.infra.task.service.common.sequence.entity.SequenceDO;
import com.teyuntong.infra.task.service.common.sequence.mapper.SequenceMapper;
import com.teyuntong.infra.task.service.common.sequence.service.SequenceService;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import lombok.RequiredArgsConstructor;

import javax.annotation.Resource;

/**
 * <p>
 * 序列表 服务实现类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-10-08
 */

@Service
@Slf4j
@RequiredArgsConstructor
public class SequenceServiceImpl implements SequenceService {
    @Resource
    private SequenceMapper sequenceMapper;

    @Override
    public Long updateGetNextSequenceNbr(String sequenceName) {
        sequenceMapper.addNumber(sequenceName);
        SequenceDO sequenceDO = sequenceMapper.selectByName(sequenceName);
        return sequenceDO.getNumber();
    }
}
