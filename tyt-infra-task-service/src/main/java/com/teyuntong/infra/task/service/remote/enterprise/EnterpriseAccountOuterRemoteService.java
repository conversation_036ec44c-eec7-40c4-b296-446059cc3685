package com.teyuntong.infra.task.service.remote.enterprise;

import com.teyuntong.infra.common.web.feign.fallback.LogAndReturnNullRemoteFallbackFactory;
import com.teyuntong.outer.export.service.client.enterprise.service.EnterpriseAccountOuterRpcService;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;


@FeignClient(name = "tyt-outer-export-service", path = "outer-export", contextId = "enterpriseAccountOuterRpcService", fallbackFactory = EnterpriseAccountOuterRemoteService.EnterpriseAccountRemoteFallbackFactory.class)
public interface EnterpriseAccountOuterRemoteService extends EnterpriseAccountOuterRpcService {

    @Component
    class EnterpriseAccountRemoteFallbackFactory extends LogAndReturnNullRemoteFallbackFactory<EnterpriseAccountOuterRemoteService> {
        protected EnterpriseAccountRemoteFallbackFactory() {
            super(true, EnterpriseAccountOuterRemoteService.class);
        }
    }
}
