package com.teyuntong.infra.task.service.common.error;

import com.teyuntong.infra.common.definition.error.ErrorCodeBase;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 特运通错误码ErrorCode
 * <p>
 * （1）总共8位，1 2 位保留
 * （2）3 4 位代表具体的子模块
 * （3）5 6 7 8 位表示具体的业务
 * <p>
 * TODO 修改文件名
 */
@AllArgsConstructor
@Getter
public enum InfraTaskErrorCode implements ErrorCodeBase {
    /**
     * 00012000   添加新错误码时变更后三位
     */
    BUSINESS_ERROR("00012000", "内部错误", "warn", false),
    ;

    private final String code;
    private final String msg;
    private final String logLevel;
    private final boolean success;
}
