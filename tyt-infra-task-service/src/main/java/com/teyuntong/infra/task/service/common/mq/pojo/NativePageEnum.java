package com.teyuntong.infra.task.service.common.mq.pojo;

import lombok.Getter;

/**
 * 打开本地连接地址
 */
public enum NativePageEnum {
    transport_search(1001, "找货"),
    goods_publish(2001, "发货"),

    goods_detail(2010, "货源详情"),
    user_identity_auth(3001, "实名认证"),
    business_auth(3002, "企业认证"),
    vip(8000, "购买会员"),
    my_goods(2050, "我的货源列表"),
    user_identity_auth_new(20001, "实名认证新"),
    signing_car(20002, "企业货源接单状态")

    ;

    @Getter
    private Integer code;

    @Getter
    private String msg;

    private NativePageEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

}
