package com.teyuntong.infra.task.service.biz.market.activity.service.impl;

import com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.MarketingActivityChargeOrderDO;
import com.teyuntong.infra.task.service.biz.market.activity.mybatis.mapper.MarketingActivityChargeOrderMapper;
import com.teyuntong.infra.task.service.biz.market.activity.service.MarketingActivityChargeOrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <p>
 * 冲单活动阶段时间表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-22
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class MarketingActivityChargeOrderServiceImpl implements MarketingActivityChargeOrderService {

    private final MarketingActivityChargeOrderMapper marketingActivityChargeOrderMapper;

    @Override
    public List<MarketingActivityChargeOrderDO> getByActivityId(Long activityId) {
        return marketingActivityChargeOrderMapper.getByActivityId(activityId);
    }
}
