package com.teyuntong.infra.task.service.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
@Slf4j
public class ObfuscateUtil {
    private static String DEFAULT_SALT = "HtrsyRvpYrvj";
    private static Integer MIN_HASH_LEN = Integer.valueOf(6);
    private static String DEFAULT_ALPHABETA = "ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890";
    private static JHashIds jhashIds;

    public ObfuscateUtil() {
    }

    public static String toObfuscatedId(Long inputId) {
        Validate.notNull(inputId);
        return jhashIds.encode(new long[]{inputId.longValue()});
    }

    public static Long fromObfuscatedId(String obfuscatedId) throws IllegalArgumentException {
        Validate.notEmpty(obfuscatedId);
        Long plainId = Long.valueOf(jhashIds.decode(obfuscatedId.toUpperCase())[0]);
        return plainId;
    }

    public static boolean isValidObfuscatedId(String obfuscatedOrderId) {
        if(StringUtils.isBlank(obfuscatedOrderId)) {
            return false;
        } else {
            try {
                fromObfuscatedId(obfuscatedOrderId.toUpperCase());
                return true;
            } catch (Exception var2) {
                log.warn("Invalid obcuscated id {}.", obfuscatedOrderId);
                return false;
            }
        }
    }

    static {
        jhashIds = new JHashIds(DEFAULT_SALT, MIN_HASH_LEN, DEFAULT_ALPHABETA);
    }
}
