package com.teyuntong.infra.task.service.common.enums;

import com.aliyun.openservices.ons.shaded.commons.lang3.StringUtils;

/**
 * tyt车长与集团车长对应关系
 */
public enum CarLengthEnum {
    CAR_LENGTH_ENUM_01("6.8", "6.8"),
    CAR_LENGTH_ENUM_02("8.6", "8.7"),
    CAR_LENGTH_ENUM_03("9.6", "9.6"),
    CAR_LENGTH_ENUM_04("10", "11.7"),
    CAR_LENGTH_ENUM_05("10.5", "11.7"),
    CAR_LENGTH_ENUM_06("11", "11.7"),
    CAR_LENGTH_ENUM_07("11.5", "11.7"),
    CAR_LENGTH_ENUM_08("12", "11.5"),
    CAR_LENGTH_ENUM_09("12.5", "12.5"),
    CAR_LENGTH_ENUM_10("13", "13"),
    CAR_LENGTH_ENUM_11("13.5", "13.7"),
    CAR_LENGTH_ENUM_12("13.75", "15"),
    CAR_LENGTH_ENUM_13("14", "15"),
    CAR_LENGTH_ENUM_14("15", "15"),
    CAR_LENGTH_ENUM_15("16", "17"),
    CAR_LENGTH_ENUM_16("17", "17.5"),
    CAR_LENGTH_ENUM_17("17.5", "17.5");

    private String tytCarLength;

    private String ymmCarLength;

    CarLengthEnum(String tytCarLength, String ymmCarLength) {
        this.tytCarLength = tytCarLength;
        this.ymmCarLength = ymmCarLength;
    }

    public String getTytCarLength() {
        return tytCarLength;
    }

    public String getYmmCarLength() {
        return ymmCarLength;
    }

    public static String getYmmCarLengthByTytCarLength(String tytCarLength) {
        if (StringUtils.isNotEmpty(tytCarLength) && tytCarLength.contains("以上")) {
            return null;
        }
        for (CarLengthEnum val : CarLengthEnum.values()) {
            if (Double.parseDouble(val.getTytCarLength()) == Double.parseDouble(tytCarLength)) {
                return val.getYmmCarLength();
            }
        }
        return null;
    }

    public static String getYmmCarLengthByCarLength(String carLength) {
        for (CarLengthEnum val : CarLengthEnum.values()) {
            if (Double.parseDouble(val.getYmmCarLength()) == Double.parseDouble(carLength)) {
                return val.getYmmCarLength();
            }
        }
        return null;
    }
}
