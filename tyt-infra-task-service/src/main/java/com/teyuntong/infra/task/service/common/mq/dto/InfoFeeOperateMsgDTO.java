package com.teyuntong.infra.task.service.common.mq.dto;

import com.teyuntong.infra.task.service.common.mq.pojo.MqBaseMessageBean;
import lombok.Data;

import java.io.Serializable;

/**
 * 发送操作订金MQ
 * <AUTHOR>
 * @since 2024/09/06 11:39
 */
@Data
public class InfoFeeOperateMsgDTO extends MqBaseMessageBean implements Serializable {

    private Integer opStatus;//操作方式 1:支付信息费 2:装货完成支付信息费 3:货方发起信息费退款 4:车方同意退款 5:车主拒绝退款
    //6:车主发起信息费冻结 7:车方信息费解冻 8:车主异常上报 9:货方异常上报 10:信息费即将7天自动支付 11:

    //气泡
    private Long shipperUserId; //货主id
    private Long carOwnerUserId;//车主ID

    //日志
    private Long tsId; //mainb表id
    private String tsOrderNo; //运单编号
    private Long orderId;//订单id
    private String payNo;//tyt_transport_orders的pay_no 和 tyt_old_order表order_id

    private String startPoint; //出发地
    private String destPoint;//目的地
    private String taskContent;// 货物内容
    private String amount;//金额
    private Integer refundType;//1 全额退款 2:部分退款

    private String infoFeeServiceFee;// 平台服务费

    //技术服务费
    private String tecServiceFee;

    private String technicalServiceNo;
}
