package com.teyuntong.infra.task.service.common.mq.pojo;

import com.teyuntong.infra.task.service.common.response.ResponseCode;
import lombok.Getter;

public enum ResponseEnum {
    /** 成功 **/

    request_error(403, "参数错误"),
    success(200, "请求成功"),

    rest_error(409, "api接口错误"),

    not_found(404, "没有找到资源"),

    sys_error(500, "系统异常"),
    sys_busy(502, "系统繁忙"),
    illegal_req(900, "非法请求"),
    class_type_error(901, "类型错误"),
    fallback(902, "fallback"),

    err_1001(1001, "基础参数缺失"),
    err_1002(1002, "无效签名"),
    err_1003(1003, "未登录"),
    err_1004(1004, "其他客户端登录"),

    ;

    @Getter
    private Integer code;
    @Getter
    private String msg;

    ResponseEnum(Integer code, String msg){
        this.code = code;
        this.msg = msg;
    }

    public ResponseCode info(){
        ResponseCode respCode = new ResponseCode(this.code, this.msg);
        return respCode;
    }

    public ResponseCode info(String reqMsg){

        ResponseCode respCode = new ResponseCode(this.code, reqMsg);

        return respCode;
    }

    public static void main(String[] args) {

        String respCode = "no_found1";

        ResponseEnum codeEnum = ResponseEnum.success;

        try {
            codeEnum = ResponseEnum.valueOf(respCode);
        } catch (IllegalArgumentException e) {

        }

        System.out.println(codeEnum);

    }

}
