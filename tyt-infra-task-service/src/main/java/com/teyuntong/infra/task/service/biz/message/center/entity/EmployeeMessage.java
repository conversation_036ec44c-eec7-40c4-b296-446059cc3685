package com.teyuntong.infra.task.service.biz.message.center.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 员工消息表
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-10-23
 */
@Getter
@Setter
@TableName("employee_message")
public class EmployeeMessage {

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 消息标题
     */
    private String title;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 备注说明
     */
    private String remarks;

    /**
     * 消息发送人ID
     */
    private Long pushUserId;

    /**
     * 消息发送人姓名
     */
    private String pushUserName;

    /**
     * 消息发送时间
     */
    private Date ctime;

    /**
     * 业务操作时间
     */
    private Date operationTime;

    /**
     * 消息类型 参见 表tyt_source表 employee_message_type
     */
    private Integer type;

    /**
     * 消息已读状态 0是未读取1是已读取
     */
    private Integer readStatus;

    /**
     * 已读时间
     */
    private Date readTime;

    /**
     * 消息隶属管理员ID
     */
    private Long userId;

    /**
     * 消息隶属管理员姓名
     */
    private String userName;

    /**
     * 预留字段1
     */
    private String c1;

    /**
     * 预留字段2
     */
    private String c2;

    /**
     * 预留字段3
     */
    private String c3;

    /**
     * 预留字段4
     */
    private String c4;

    /**
     * 预留字段5
     */
    private String c5;

    /**
     * 推送状态0是未推送,1是推送
     */
    private Integer pushStatus;

    /**
     * 推送时间
     */
    private Date pushTime;

    /**
     * 修改时间
     */
    private Date utime;

    /**
     * 消息状态0正常1是删除
     */
    private Integer status;

    /**
     * 消息模版ID
     */
    private Long tmplId;
}
