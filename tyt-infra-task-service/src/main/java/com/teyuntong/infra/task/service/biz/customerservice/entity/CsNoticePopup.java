package com.teyuntong.infra.task.service.biz.customerservice.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 客服系统弹窗通知
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-09-29
 */
@Getter
@Setter
@TableName("cs_notice_popup")
public class CsNoticePopup {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 弹窗内容
     */
    private String content;

    /**
     * 通知人id
     */
    private Long productionId;

    /**
     * 被通知人id
     */
    private Long receiveId;

    /**
     * 通知接收时间
     */
    private Date receiveTime;

    /**
     * 通知接收状态 1未接收 2已接受
     */
    private Integer receiveStatus;

    /**
     * 是否有效 0无效 1有效
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;
}
