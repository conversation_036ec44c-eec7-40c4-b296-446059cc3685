package com.teyuntong.infra.task.service.biz.market.activity.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.ConventionActivityPrizeDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 履约活动冲单奖品配置 Mapper 接口
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2024-11-08
 */
@Mapper
public interface ConventionActivityPrizeMapper extends BaseMapper<ConventionActivityPrizeDO> {

    List<ConventionActivityPrizeDO> getByActivityId(@Param("activityId")long activityId, @Param("type") int type);
}
