package com.teyuntong.infra.task.schedule.pay.order;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.teyuntong.infra.task.service.biz.config.service.TytConfigService;
import com.teyuntong.infra.task.service.biz.pay.service.PayRecordService;
import com.teyuntong.infra.task.service.biz.trade.infofee.entity.OldOrder;
import com.teyuntong.infra.task.service.biz.trade.infofee.mapper.OldOrderMapper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 检测超时未支付，或者支付时长过长的订单信息
 *
 * <AUTHOR>
 * @since 2024/8/5 11:46
 */
@Slf4j
@Component
public class CheckOrderIdSchedule {

	private static final long ONE_HOUR_IN_MILLI = 60 * 60 * 1000;

	public static final String REDIS_ORDER_IDS_KEY = "vip_pay_order_ids";

	public static final String MAX_NO_PAY_TIME_KEY = "max.no.pay.time.key";

	@Autowired
	private RedisTemplate<String, String> redisTemplate;

	@Autowired
	private OldOrderMapper oldOrderMapper;

	@Autowired
	private PayRecordService payRecordService;

	@Autowired
	private TytConfigService tytConfigService;

	@XxlJob("checkOrderId")
	public void checkOrderId() {
		long beginTime = System.currentTimeMillis();
		log.info("check orderId task begin...");
		String orderIdKey = REDIS_ORDER_IDS_KEY;
		// 获取待处理的订单id
		List<String> orderIdList = redisTemplate.opsForList().range(orderIdKey, 0, -1);
		List<OldOrder> overTimeOrderList = new ArrayList<>();
		long maxNoPayTime = Long.parseLong(tytConfigService.getStringValue(MAX_NO_PAY_TIME_KEY, "600000"));
        log.info("check orderId task orderIdList: {}, maxNoPayTime: {}", orderIdList, maxNoPayTime);
		if (CollectionUtils.isNotEmpty(orderIdList)) {
			// 查询一小时内的所有订单
			Map<String, OldOrder> onHourOrderMap = queryOrder();
			if (CollectionUtils.isNotEmpty(orderIdList)) {
				String curOrderId;
				OldOrder curOrder;
                for (String s : orderIdList) {
                    curOrderId = s;
                    curOrder = onHourOrderMap.get(curOrderId);
                    if (curOrder != null) {
                        boolean isOvertime = ((System.currentTimeMillis() - curOrder.getCtime().getTime()) >= maxNoPayTime);
                        // 是否支付成功
                        boolean isPaySuc = (curOrder.getStatus() == 2);
                        if (isPaySuc) {
                            isOvertime = ((curOrder.getMtime().getTime() - curOrder.getCtime().getTime()) >= maxNoPayTime);
                        }
                        log.info("check orderId task isOvertime: {}, isPaySuc: {}", isOvertime, isPaySuc);
                        // 如果当前订单支付完成并且时间没有超过10分钟，则直接从redis中删除
                        if (!isOvertime && isPaySuc) {
                            redisTemplate.opsForList().remove(orderIdKey, 0, curOrderId);
                        } else if (isOvertime) { // // 如果当前订单支付完成并且时间超过10分钟，则直接从redis中删除并且记录支付信息
                            redisTemplate.opsForList().remove(orderIdKey, 0, curOrderId);
                            overTimeOrderList.add(curOrder);
                        }
                    }
                }
			}
		}
		if (CollectionUtils.isNotEmpty(overTimeOrderList)) {
			payRecordService.saveOrders(overTimeOrderList);
		}
        log.info("check orderId task end, waste time is: {}", System.currentTimeMillis() - beginTime);
	}

	private Map<String, OldOrder> queryOrder() {
		Timestamp oneHourBefore = new Timestamp(System.currentTimeMillis() - ONE_HOUR_IN_MILLI);

		QueryWrapper<OldOrder> queryWrapper = new QueryWrapper<>();
		queryWrapper.gt("ctime", oneHourBefore);
		queryWrapper.orderByDesc("id");
		List<OldOrder> orderList = oldOrderMapper.selectList(queryWrapper);

        log.info("check orderId task orderList is: {}", orderList);
		Map<String, OldOrder> orderMap = new HashMap<>();
		if (CollectionUtils.isNotEmpty(orderList)) {
			OldOrder curOrder;
            for (OldOrder oldOrder : orderList) {
                curOrder = oldOrder;
                orderMap.put(String.valueOf(curOrder.getId()), curOrder);
            }
		}
        log.info("check orderId task orderMap is: {}", orderMap);
		return orderMap;
	}
}
