package com.teyuntong.infra.task.schedule.goods.goodsfee;

import com.teyuntong.infra.task.service.biz.goods.goodsfee.service.TransportQuotedPriceService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

/**
 * 车方报价货方5分钟内无响应通知车方定时任务
 */
@Slf4j
@Component
public class TransportQuotedPriceSchedule {

    public TransportQuotedPriceSchedule(TransportQuotedPriceService transportQuotedPriceService) {
        this.transportQuotedPriceService = transportQuotedPriceService;
    }

    private final TransportQuotedPriceService transportQuotedPriceService;

    @XxlJob("transportQuotedPriceFiveMinNoResponsesWarn")
    public void invoiceEnterpriseExpiredWarn() {
        log.info("车方报价货方5分钟内无响应通知车方定时任务开始");
        try {
            transportQuotedPriceService.transportQuotedPriceFiveMinNoResponsesWarn();
        } catch (Exception e) {
            log.error("车方报价货方5分钟内无响应提醒货方定时任务失败", e);
        }
        log.info("车方报价货方5分钟内无响应通知车方定时任务结束");
    }

    /**
     * 无价货源系统报价定时任务
     *
     * @return
     */
    @XxlJob("systemQuotedPrice")
    public ReturnT<String> systemQuotedPrice() {
        log.info("无价货源系统报价定时任务开始执行");
        XxlJobHelper.log("无价货源系统报价定时任务开始执行");
        StopWatch watch = new StopWatch();
        watch.start();
        try {
            transportQuotedPriceService.systemQuotedPrice();
        } catch (Exception e) {
            log.error("无价货源系统报价定时任务失败", e);
            XxlJobHelper.log("无价货源系统报价定时任务失败", e);
        }
        watch.stop();
        log.info("无价货源系统报价定时任务执行结束，耗时：{} ms", watch.getTotalTimeMillis());
        XxlJobHelper.log("无价货源系统报价定时任务执行结束，耗时：{} ms", watch.getTotalTimeMillis());
        return ReturnT.SUCCESS;
    }

}
