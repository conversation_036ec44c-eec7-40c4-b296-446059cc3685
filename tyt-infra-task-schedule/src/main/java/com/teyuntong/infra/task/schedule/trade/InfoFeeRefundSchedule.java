package com.teyuntong.infra.task.schedule.trade;

import com.teyuntong.infra.task.service.biz.trade.infofee.service.InfoFeeRefundService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024/03/29 17:30
 */
@Slf4j
@Component
public class InfoFeeRefundSchedule {

    @Autowired
    private InfoFeeRefundService infoFeeRefundService;

    /**
     * 将用户重复支付的进行退款
     */
    @XxlJob("autoRefundRepeatPay")
    public void autoRefundRepeatPay() {
        log.info("用户重复支付的进行退款启动.....");
        try {
            infoFeeRefundService.autoRefundRepeatPay();
            log.info("用户重复支付的进行退款结束.....");
        } catch (Exception e) {
            log.error("用户重复支付的进行退款异常", e);

        }
    }

}
