package com.teyuntong.infra.task.schedule.creeper;

import com.alibaba.fastjson.JSONObject;
import com.teyuntong.infra.task.service.biz.user.info.service.UserService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.List;

/**
 * 反爬定时任务
 *
 * <AUTHOR>
 * @since 2023/10/08 13:14
 */
@Component
@Slf4j
public class CreeperSchedule {

    @Autowired
    private UserService userService;

    /**
     * 反爬定时任务
     */
    @XxlJob("creeper")
    public ReturnT<String> creeper() {
        long now = System.currentTimeMillis();
        List<Long> userIdList;
        try {
            userIdList = userService.creeper();
        } catch (Exception e) {
            log.error("反爬定时任务执行失败", e);
            XxlJobHelper.log("反爬定时任务执行失败", e);
            return ReturnT.FAIL;
        }
        log.info("反爬定时任务耗时：" + (System.currentTimeMillis() - now));
        if (CollectionUtils.isNotEmpty(userIdList)) {
            XxlJobHelper.log("反爬定时发现新的爬虫用户：" + JSONObject.toJSONString(userIdList));
        } else {
            XxlJobHelper.log("反爬定时未发现新的爬虫用户");
        }
        XxlJobHelper.log("反爬定时任务耗时：" + (System.currentTimeMillis() - now));
        return ReturnT.SUCCESS;
    }
}
