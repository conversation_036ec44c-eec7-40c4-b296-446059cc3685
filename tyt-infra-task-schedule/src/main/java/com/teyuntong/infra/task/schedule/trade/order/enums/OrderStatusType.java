package com.teyuntong.infra.task.schedule.trade.order.enums;

/**
 * xyy
 * 类型
 *信息费状态：10待支付，15已支付，20已冻结，21拒绝退款，25异常上报，30退款中，35已退款，40已打款，45自动收款，50异常完成
 */
public enum OrderStatusType {
    INVALID(5,"货源撤销退款",212,"货源撤销退款"),
    TO_PAY(10, "待支付", 201,"订单尚未支付"),
    PAY(15, "已支付", 202,"该订单为已支付状态"),
    FREEZE(20, "已冻结", 203,"该订单已冻结"),
    REFUSE_REFUND(21, "拒绝退款", 204,"车方拒绝退款"),
    REFUSE_REFUND_GOODS(22, "拒绝退款", 204,"货方拒绝退款"),
    EX_REPORT(25, "异常上报", 205,"该订单已被异常上报"),
    REFUNDING(30, "退款中", 206,"货方已发起退款申请"),
    REFUNDED(35, "已退款", 207,"该订单已退款"),
    CONFIRM_PAY(40, "已打款", 208,"车方已确认打款"),
    AUTO_PAY(45, "自动收款", 209,"该订单已自动收款"),
    EX_HANDLE_COMPLETE(50, "异常处理完成", 211,"该运单已异常处理完成")
    ;

    /**
     * 状态值
     */
    private int status;
    /**
     * 前端显示
     */
    private String value;
    /**
     * 前端显示的code
     */
    private int failCode;
    /**
     * 后台显示
     */
    private String name;

    OrderStatusType(int status, String value, int failCode, String name) {
        this.status = status;
        this.value = value;
        this.failCode = failCode;
        this.name = name;
    }

    /**
     * 返回前端文案
     * @param status
     * @return
     */
    public static String getName( int status) {
        OrderStatusType[] oderStatusEnums = OrderStatusType.values();
        for (OrderStatusType orderStatusEnum : oderStatusEnums) {
            if (orderStatusEnum.status==status) {
                return orderStatusEnum.getName();
            }
        }
        return null;
    }

    /**
     * 返回前端code
     * @param status
     * @return
     */
    public static int getFailCode( int status) {
        OrderStatusType[] oderStatusEnums = OrderStatusType.values();
        for (OrderStatusType orderStatusEnum : oderStatusEnums) {
            if (orderStatusEnum.status==status) {
                return orderStatusEnum.getFailCode();
            }
        }
        return -1;
    }

    /**
     * @param status
     * @return
     */
    public static String getValue( int status) {
        OrderStatusType[] oderStatusEnums = OrderStatusType.values();
        for (OrderStatusType orderStatusEnum : oderStatusEnums) {
            if (orderStatusEnum.status==status) {
                return orderStatusEnum.getValue();
            }
        }
        return null;
    }

    public int getStatus() {
        return status;
    }

    public String getName() {
        return name;
    }

    public String getValue() {
        return value;
    }

    public int getFailCode() {
        return failCode;
    }
}
