package com.teyuntong.infra.task.schedule.market.activity;

import com.teyuntong.infra.task.service.biz.market.activity.service.NewUserLifeService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/01/13 16:52
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class NewUserLifeSchedule {

    private final NewUserLifeService userLifeService;

    /**
     * 新用户注册1小时通知
     */
    @XxlJob("NewUserRegisterOneHourNoticeSchedule")
    public void newUserNotice() {
        log.info("新用户注册1h找货提醒 start");
        try{
            userLifeService.newUserNotice();
        }catch (Exception e){
            log.error("新用户注册1h找货提醒 error：", e);
        }
        log.info("新用户注册1h找货提醒 end");
    }

    /**
     * 新用户促活弹窗提醒
     */
    @XxlJob("NewUserPopupNoticeSchedule")
    public void newUserPopupNotice() {
        log.info("新用户促活弹窗提醒 start");
        try{
            userLifeService.newUserPopupNotice();
        }catch (Exception e){
            log.error("新用户促活弹窗提醒 error：", e);
        }
        log.info("新用户促活弹窗提醒 end");
    }

    /**
     * 新用户保障弹窗提醒
     */
    @XxlJob("NewUserGuaranteePopupNoticeSchedule")
    public void newUserGuaranteePopupNotice() {
        log.info("新用户保障弹窗提醒 start");
        try{
            userLifeService.newUserGuaranteePopupNotice();
        }catch (Exception e){
            log.error("新用户保障弹窗提醒 error：", e);
        }
        log.info("新用户保障弹窗提醒 end");
    }


    /**
     * 新用户推荐货源推送
     */
    @XxlJob("pushRecommendedGoods")
    public void pushRecommendedGoods() {
        log.info("新用户推荐货源推送 start");
        try {
            userLifeService.pushRecommendedGoods();
        } catch (Exception e) {
            log.error("新用户推荐货源推送 error：", e);
        }
        log.info("新用户推荐货源推送 end");
    }

}
