package com.teyuntong.infra.task.schedule.dispatch;

import com.teyuntong.infra.task.service.biz.dispatch.service.NonSpecialCarDispatchService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

/**
 * 非专车货源自动指派
 *
 * <AUTHOR>
 * @since 2024-12-12 13:07
 */
@Slf4j
@Service
public class NonSpecialCarDispatchSchedule {
    @Autowired
    private NonSpecialCarDispatchService nonSpecialCarDispatchService;

    /**
     * 非专车货源自动指派
     *
     * @return
     */
    @XxlJob("nonSpecialCarAutoDispatch")
    public ReturnT<String> nonSpecialCarAutoDispatch() {
        try {
            XxlJobHelper.log("非专车货源自动指派开始执行");
            StopWatch watch = new StopWatch();
            watch.start();
            nonSpecialCarDispatchService.nonSpecialCarAutoDispatch();
            watch.stop();
            XxlJobHelper.log("非专车货源自动指派执行结束，耗时:{}", watch.getTotalTimeMillis());
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("非专车货源自动指派执行异常", e);
            XxlJobHelper.log("非专车货源自动指派执行异常", e);
        }
        return ReturnT.FAIL;
    }
}
