package com.teyuntong.infra.task.schedule.market.activity;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.teyuntong.infra.task.service.biz.market.activity.service.ConventionOrdersCensusService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.Date;

@Slf4j
@Component
@RequiredArgsConstructor
public class SprintOrderActivitySchedule {

    private final ConventionOrdersCensusService conventionOrdersCensusService;

    /**
     * 冲单奖自动开启新阶段活动
     */
    @XxlJob("SprintOrderActivityBeginNewRankSchedule")
    public void beginNewRank() {
        log.info("sprint order activity begin new rank begin");
        try{
            conventionOrdersCensusService.beginNewRank();
        }catch (Exception e){
            log.error("sprint order activity begin new rank error：", e);
        }
        log.info("sprint order activity begin new rank end");
    }

    /**
     * 冲单奖发阶段奖励
     */
    @XxlJob("SprintOrderActivityIssueRankRewardSchedule")
    public void issueRankReward() {
        log.info("sprint order activity issue rank reward begin");
        try{
            conventionOrdersCensusService.issueRankReward();
        }catch (Exception e){
            log.error("sprint order activity issue rank reward error：", e);
        }
        log.info("sprint order activity issue rank reward end");
    }

    /**
     * 老冲单奖活动排名和发奖定时任务
     */
    @XxlJob("givePermission4ConventionOrdersCensus")
    public void givePermission4ConventionOrdersCensus(){
        log.info("givePermission4ConventionOrdersCensus 老冲单奖排名和发奖定时任务 启动.....");
        try {
            conventionOrdersCensusService.givePermissionForConvention();
        }catch (Exception e){
            log.error("givePermission4ConventionOrdersCensus 老冲单奖排名和发奖定时任务 异常：", e);
        }
        log.info("givePermission4ConventionOrdersCensus 老冲单奖排名和发奖定时任务 结束....");
    }

    /**
     * 冲单活动跑马灯数据统计
     */
    @XxlJob("conventionCarousel")
    public void conventionCarousel(){
        log.info("conventionCarousel 冲单活动跑马灯数据统计表 启动.....");
        try {
            conventionOrdersCensusService.conventionCarousel();
        }catch (Exception e){
            log.error("conventionCarousel 冲单活动跑马灯数据统计表 异常：", e);
        }
        log.info("conventionCarousel 冲单活动跑马灯数据统计表 结束....");
    }



}
