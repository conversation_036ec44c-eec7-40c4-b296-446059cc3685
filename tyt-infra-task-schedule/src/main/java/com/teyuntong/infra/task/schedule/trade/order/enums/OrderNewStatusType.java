package com.teyuntong.infra.task.schedule.trade.order.enums;

/**
 * 新订单状态:5.待接单 10.待签署 15.待装货 20.待卸货/待收货 25.待收运费/待付运费 30.已完成 35.已取消
 */
public enum OrderNewStatusType {

    PENDING_ORDER(5, "待接单"),
    WAIT_SIGN_ORDER(10, "待签署"),
    WAIT_LOADING_ORDER(15, "待装货"),
    WAIT_UNLOAD_ORDER(20, "待卸货/待收货"),
    WAIT_PAY_FREIGHT(25, "待收运费/待付运费"),
    ORDER_FINISHED(30, "已完成"),
    ORDER_CANCELED(35, "已取消")
    ;

    /**
     * 状态值
     */
    private int status;

    /**
     * 状态名称
     */
    private String name;

    OrderNewStatusType(int status, String name) {
        this.status = status;
        this.name = name;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
