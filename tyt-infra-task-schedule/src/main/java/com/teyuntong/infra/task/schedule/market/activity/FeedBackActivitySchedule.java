package com.teyuntong.infra.task.schedule.market.activity;

import cn.hutool.core.util.ObjectUtil;
import com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.ConventionActivityDO;
import com.teyuntong.infra.task.service.biz.market.activity.mybatis.entity.MarketingActivityDO;
import com.teyuntong.infra.task.service.biz.market.activity.service.ActivityPrizeService;
import com.teyuntong.infra.task.service.biz.market.activity.service.ConventionActivityService;
import com.teyuntong.infra.task.service.biz.market.activity.service.MarketingActivityService;
import com.teyuntong.infra.task.service.biz.user.car.pojo.FeedbackNumBean;
import com.teyuntong.infra.task.service.biz.user.car.service.FeedBackUserService;
import com.teyuntong.infra.task.service.biz.user.permission.pojo.PermissionChangeType;
import com.teyuntong.infra.task.service.biz.user.permission.pojo.PermissionGainTypeNewEnum;
import com.teyuntong.infra.task.service.biz.user.permission.service.UserPermissionService;
import com.teyuntong.infra.task.service.utils.TimeUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 评价活动相关定时任务
 *
 * <AUTHOR>
 * @since 2024-10-28 10:59:58
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FeedBackActivitySchedule {

    private final MarketingActivityService marketingActivityService;

    private final ConventionActivityService conventionActivityService;

    private final UserPermissionService userPermissionService;

    private final ActivityPrizeService activityPrizeService;

    private final FeedBackUserService feedBackUserService;

    private static final Integer ACTIVITY_TYPE = 20;

    /**
     * 车方评价活动结束后发送权益
     */
    @XxlJob("FeedBackActivityGiveUserPermissionSchedule")
    public void feedBackActivityGiveUserPermission() {
        log.info("feedback activity give user permission begin");
        //获取前一天日期
        String time = TimeUtil.yesterDayToString();
        List<MarketingActivityDO> activityList = marketingActivityService.getEndActivity(ACTIVITY_TYPE);
        if (CollectionUtils.isEmpty(activityList)) {
            log.info("feedback activity give user permission, no end activity");
            return;
        }
        try {
            for (MarketingActivityDO activity : activityList) {
                String endTime = TimeUtil.formatDate(activity.getEndTime());
                if (!endTime.equals(time)) {
                    log.info("feedback activity give user permission, activity end");
                    continue;
                }
                while (true) {
                    //查询已获奖用户信息
                    List<ConventionActivityDO> tytConventionActivityList = conventionActivityService.getByActivityId(activity.getId());
                    if (CollectionUtils.isEmpty(tytConventionActivityList)) {
                        log.info("feedback activity give user permission, no user info");
                        break;
                    }
                    //发送权益
                    for (ConventionActivityDO conventionActivityDO : tytConventionActivityList) {
                        Long goodsId = activityPrizeService.getByPrize(conventionActivityDO.getPrize());
                        if (ObjectUtil.isNotNull(goodsId)) {
                            userPermissionService.givePermission(conventionActivityDO.getUserId(), goodsId,
                                    "carRateActivity", conventionActivityDO.getActivityId(), PermissionGainTypeNewEnum.赠送, PermissionChangeType.赠送);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("feedback activity give user permission error:", e);
        }
        log.info("feedback activity give user permission end");
    }

    /**
     * 车方评价活动计算用户好评单量
     */
    @XxlJob("FeedBackActivityConventionEvaluateNumSchedule")
    public void feedBackActivityConventionEvaluateNum() {
        log.info("feedback activity convention evaluate num begin");
        List<MarketingActivityDO> activityList = marketingActivityService.getByType(ACTIVITY_TYPE);
        if (CollectionUtils.isEmpty(activityList)) {
            log.info("feedback activity convention evaluate num, no activity");
            return;
        }
        try {
            for (MarketingActivityDO activity : activityList) {
                //获取车方评价单量
                List<FeedbackNumBean> feedbackNumBeans = feedBackUserService.getTimeUserNum(activity.getStartTime(), activity.getEndTime());
                if (CollectionUtils.isEmpty(feedbackNumBeans)) {
                    log.info("feedback activity convention evaluate num, no order");
                    continue;
                }
                conventionActivityService.updateEvaluateNum(feedbackNumBeans, activity.getId());
            }
        } catch (Exception e) {
            log.error("feedback activity convention evaluate num error:", e);
        }
        log.info("feedback activity convention evaluate num end");
    }
}
