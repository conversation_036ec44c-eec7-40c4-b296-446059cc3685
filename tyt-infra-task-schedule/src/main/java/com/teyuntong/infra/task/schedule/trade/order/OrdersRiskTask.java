package com.teyuntong.infra.task.schedule.trade.order;

import com.alibaba.fastjson.JSON;
import com.teyuntong.infra.task.service.biz.trade.constant.OrdersConstant;
import com.teyuntong.infra.task.service.biz.trade.enums.OrderNewStatusTypeEnum;
import com.teyuntong.infra.task.service.biz.trade.enums.OrderStatusTypeEnum;
import com.teyuntong.infra.task.service.biz.trade.infofee.bean.MqOperateFreightMsg;
import com.teyuntong.infra.task.service.biz.trade.infofee.entity.TransportOrdersDO;
import com.teyuntong.infra.task.service.biz.trade.infofee.service.TransportOrdersService;
import com.teyuntong.infra.task.service.biz.trade.utils.SerialNumUtil;
import com.teyuntong.infra.task.service.common.constant.MqOperateConstant;
import com.teyuntong.infra.task.service.common.mq.config.OrderCenterTagConstant;
import com.teyuntong.infra.task.service.common.mq.dto.MqOperateFreightMsgDTO;
import com.teyuntong.infra.task.service.common.mq.dto.MqOrderOperateStatusMsgDTO;
import com.teyuntong.infra.task.service.common.mq.dto.InfoFeeOperateMsgDTO;
import com.teyuntong.infra.task.service.common.mq.service.MqMessageService;
import com.teyuntong.infra.task.service.remote.tytconfig.service.TytConfigRemoteService;
import com.xxl.job.core.handler.annotation.XxlJob;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.util.List;
import java.util.Objects;

/**
 * 风控任务
 * <AUTHOR>
 * @since 2024/09/05 18:24
 */
@Slf4j
@Component
public class OrdersRiskTask {

    @Autowired
    private TransportOrdersService transportOrdersService;

    @Autowired
    private  StringRedisTemplate stringRedisTemplate;

    @Autowired
    private MqMessageService mqMessageService;

    final static String RISK_ORDER_CANCEL="risk_order_cancel";

    final static String REFUND_REASON="风控申诉到期，系统自动退款";

    @Autowired
    private TytConfigRemoteService tytConfigRemoteService;

    @XxlJob("orderRiskTimeOutTask")
    public void orderRiskTimeOutTask(){
        try {
            log.error("orderRiskTimeOutTask start");
            List<TransportOrdersDO> timeOutRiskOrders = transportOrdersService.getTimeOutRiskOrders();
            if (timeOutRiskOrders != null && !timeOutRiskOrders.isEmpty()) {
                for (TransportOrdersDO order : timeOutRiskOrders) {
                    updateRiskOrderStatus(order);
                }
                log.error("orderRiskTimeOutTask end");
            }
        } catch (Exception e) {
            log.error("orderRiskTimeOutTask error:", e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateRiskOrderStatus(TransportOrdersDO transportOrder) {
        String orderIDStr = stringRedisTemplate.opsForValue().get(RISK_ORDER_CANCEL + "_" + transportOrder.getId());
        if(!StringUtil.isEmpty(orderIDStr)){
            log.info("updateRiskOrderStatus orderId:{} exist", transportOrder.getId());
            return;
        }
        try {
            log.info("updateRiskOrderStatus  start orderId:{} ", transportOrder.getId());
            boolean isSuccess = Boolean.TRUE.equals(stringRedisTemplate.opsForValue().setIfAbsent(RISK_ORDER_CANCEL + "_" + transportOrder.getId(), transportOrder.getId() + "", Duration.ofMillis(10)));
            if(!isSuccess){
                return;
            }
            //支付过运费的单子更新未已取消，未支付的不变
            Integer freightStatus = transportOrder.getFreightStatus();
            if (freightStatus > 0) {
                freightStatus = 30;
            }
            transportOrdersService.updateTimeOutRiskOrders(REFUND_REASON , transportOrder.getPayAmount() , OrderStatusTypeEnum.REFUNDED.getStatus(), OrderNewStatusTypeEnum.ORDER_CANCELED.getStatus(), OrdersConstant.RISK_CONTROL_STATUS_6, freightStatus, transportOrder.getId() );
            //发送操作订金MQ
            InfoFeeOperateMsgDTO infoFeeOperateMsgDTO = saveInfoFeeOperateMsg( transportOrder);
            MqOperateFreightMsgDTO mqOperateFreightMsgDTO = null;
            //开票货源发送操作运费退还MQ
            if (transportOrder.getInvoiceTransport() == OrdersConstant.TRANSPORT_ORDERS_INVOICE_TRANSPORT_YES) {
                mqOperateFreightMsgDTO = saveMqOperateFreightMsgDTO(transportOrder);
            }
            //发送记录订单时间轴MQ
            MqOrderOperateStatusMsgDTO mqOrderOperateStatusMsgDTO  = saveOperateStatusMsgMessage(transportOrder.getId(), OrderStatusTypeEnum.REFUNDED.getStatus(),
                    OrderNewStatusTypeEnum.ORDER_CANCELED.getStatus(), 50);

            //发送消息
            if(Objects.nonNull(infoFeeOperateMsgDTO)){
                mqMessageService.sendMessage(infoFeeOperateMsgDTO,infoFeeOperateMsgDTO.getMessageSerailNum(),0);
            }
            if(Objects.nonNull(mqOperateFreightMsgDTO)){
                int freightSwitch = tytConfigRemoteService.getIntValue("trade_center_mq_freight_switch", 0);
                if (freightSwitch == 1){
                    mqMessageService.sendMessageForCenter(mqOrderOperateStatusMsgDTO, OrderCenterTagConstant.TRADE_CENTER_TOPIC, OrderCenterTagConstant.FREIGHT_OPERATE, mqOrderOperateStatusMsgDTO.getMessageSerailNum(), 0);
                }else {
                    mqMessageService.sendMessage(mqOperateFreightMsgDTO,mqOperateFreightMsgDTO.getMessageSerailNum(),0);
                }
            }
            if(Objects.nonNull(mqOrderOperateStatusMsgDTO)){
                int freightSwitch = tytConfigRemoteService.getIntValue("trade_center_mq_operate_status_switch", 0);
                if (freightSwitch == 1){
                    mqMessageService.sendMessageForCenter(mqOrderOperateStatusMsgDTO, OrderCenterTagConstant.TRADE_CENTER_TOPIC, OrderCenterTagConstant.ORDER_OPERATE, mqOrderOperateStatusMsgDTO.getMessageSerailNum(), 0);
                }else {
                    mqMessageService.sendMessage(mqOrderOperateStatusMsgDTO,mqOrderOperateStatusMsgDTO.getMessageSerailNum(),0);
                }

            }

            log.info("updateRiskOrderStatus  end orderId:{} ", transportOrder.getId());
        } catch (Exception e) {
            log.error("updateRiskOrderStatus error param: {} ", JSON.toJSONString(transportOrder), e);
        } finally {
            stringRedisTemplate.delete(RISK_ORDER_CANCEL + "_" + transportOrder.getId());
        }
    }

    private MqOrderOperateStatusMsgDTO saveOperateStatusMsgMessage(Long orderId, int costStatus, int orderStatus, int operateType) {

        MqOrderOperateStatusMsgDTO operateStatusMsg = new MqOrderOperateStatusMsgDTO();
        operateStatusMsg.setOrderId(orderId);
        operateStatusMsg.setCostStatus(costStatus);
        operateStatusMsg.setOrderStatus(orderStatus);
        operateStatusMsg.setOperateType(operateType);
        operateStatusMsg.setDelFlag(0);
        operateStatusMsg.setDataType(0);
        operateStatusMsg.setMessageType(MqOperateConstant.ORDER_OPERATE_STATUS_SYNC);
        operateStatusMsg.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
        int freightSwitch = tytConfigRemoteService.getIntValue("trade_center_mq_operate_status_switch", 0);
        if (freightSwitch != 1){
            mqMessageService.saveMqMessage(operateStatusMsg, MqOperateConstant.INFO_FEE_OPERATE_DEAL);
        }

        return operateStatusMsg;
    }

    private MqOperateFreightMsgDTO saveMqOperateFreightMsgDTO(TransportOrdersDO transportOrder) {

        MqOperateFreightMsgDTO operateFreightMsg = new MqOperateFreightMsgDTO();
        operateFreightMsg.setOrderId(transportOrder.getId());
        operateFreightMsg.setOperateType(MqOperateFreightMsg.OPERATE_TYPE_REFUND);
        operateFreightMsg.setRefundReason("风控单到时未申诉");
        operateFreightMsg.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
        operateFreightMsg.setMessageType(MqOperateConstant.ORDERS_FREIGHT_OPERATE_CODE);
        int freightSwitch = tytConfigRemoteService.getIntValue("trade_center_mq_freight_switch", 0);
        if (freightSwitch != 1) {
            mqMessageService.saveMqMessage(operateFreightMsg, MqOperateConstant.ORDERS_FREIGHT_OPERATE_CODE);
        }
        return operateFreightMsg;
    }

    private InfoFeeOperateMsgDTO saveInfoFeeOperateMsg(TransportOrdersDO transportOrder) {

        InfoFeeOperateMsgDTO operateMsg = new InfoFeeOperateMsgDTO();
        operateMsg.setMessageSerailNum(SerialNumUtil.generateSeriaNum());
        operateMsg.setMessageType(MqOperateConstant.INFO_FEE_OPERATE_DEAL);
        operateMsg.setOpStatus(15);
        operateMsg.setAmount(String.valueOf(transportOrder.getPayAmount()));
        operateMsg.setCarOwnerUserId(transportOrder.getPayUserId());
        operateMsg.setShipperUserId(transportOrder.getUserId());
        operateMsg.setStartPoint(transportOrder.getStartPoint());
        operateMsg.setDestPoint(transportOrder.getDestPoint());
        operateMsg.setTaskContent(transportOrder.getTaskContent());
        operateMsg.setTsId(transportOrder.getTsId());
        operateMsg.setTsOrderNo(transportOrder.getTsOrderNo());
        operateMsg.setOrderId(transportOrder.getId());
        operateMsg.setInfoFeeServiceFee("0.00");
        operateMsg.setRefundType(1);
        operateMsg.setTecServiceFee(String.valueOf(transportOrder.getTecServiceFee()));
        operateMsg.setTechnicalServiceNo(transportOrder.getTechnicalServiceNo());
        mqMessageService.saveMqMessage(operateMsg, MqOperateConstant.INFO_FEE_OPERATE_DEAL);
        return operateMsg;
    }

}
