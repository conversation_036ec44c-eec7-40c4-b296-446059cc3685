package com.teyuntong.infra.task.schedule.trade;

import com.teyuntong.infra.task.service.biz.config.service.TytConfigService;
import com.teyuntong.infra.task.service.biz.trade.monitor.service.MonitorTaskTmplService;
import com.teyuntong.infra.task.service.utils.TimeUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

@Slf4j
@Component
public class MonitorSchedule {

    @Autowired
    private MonitorTaskTmplService monitorTaskTmplService;

    @Autowired
    private TytConfigService tytConfigService;

    //监控任务是否开启的开关(1:开 2:关 )
    private static final String MONITOR_TASK_ENABLE = "monitor_task_enable";


    /**
     * @Description  根据监控任务模板扫描失败的记录并保存
     * <AUTHOR>
     * @Date  2024/8/14 10:16
     * @Param []
     * @return void
     **/
    @XxlJob("saveMonitorTaskFailRecord")
    public void saveMonitorTaskFailRecord(){
        Integer taskFlag = tytConfigService.getIntValue(MONITOR_TASK_ENABLE, 2);
        if(taskFlag != null && taskFlag.intValue() == 1) {
            log.info("扫描需要执行的监控任务开始.....", TimeUtil.formatDateTime(new Date()));
            try {
                monitorTaskTmplService.saveMonitorTaskFailRecord();
            } catch (Exception e) {
                e.printStackTrace();
                log.error("扫描需要执行的监控任务出现异常：", e.getMessage());
            }
            log.info("扫描需要执行的监控任务结束.....", TimeUtil.formatDateTime(new Date()));
        }
    }
    /**
     * @Description  发送失败记录给需要被通知的用户
     * <AUTHOR>
     * @Date  2024/8/14 11:16
     * @Param []
     * @return void
     **/
    @XxlJob("sendFailRecordMsgToUser")
    public void sendFailRecordMsgToUser(){

        //根据开关判断是否执行下面的程序  1:开 2:关
        Integer taskFlag = tytConfigService.getIntValue(MONITOR_TASK_ENABLE, 2);
        if(taskFlag != null && taskFlag.intValue() == 1)
        {
            log.info("------------------------------");
            log.info("发送失败记录给需要被通知的用户任务开始.....", TimeUtil.formatDateTime(new Date()));
            try {
                monitorTaskTmplService.sendFailRecordMsgToUser();
            }catch (Exception e){
                e.printStackTrace();
                log.error("发送失败记录给需要被通知的用户任务出现异常：", e.getMessage());
            }
            log.info("发送失败记录给需要被通知的用户任务结束.....", TimeUtil.formatDateTime(new Date()));
            log.info("------------------------------");
        }
    }
 }
