package com.teyuntong.infra.task.schedule.goods.addresslevel;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.aliyun.openservices.ons.shaded.commons.lang3.StringUtils;
import com.teyuntong.infra.task.service.biz.goods.addresslevel.service.GoodsAddressLevelRecordService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.baomidou.mybatisplus.core.toolkit.StringPool.COMMA;

/**
 * 定时调用高德添加五级地址
 * <AUTHOR>
 * @since 2024/01/18 13:45
 */
@Slf4j
@Component
public class GaoDeMapSchedule {

    @Autowired
    private GoodsAddressLevelRecordService goodsAddressLevelRecordService;

    @XxlJob("gaoDeRegeoJobHandler")
    public void gaoDeRegeoJobHandler() {
        try {
            DateTime start = null;
            DateTime end = null;
            String jobParam = XxlJobHelper.getJobParam();
            log.info("添加5级地址开始,参数：{}", jobParam);
            if (StringUtils.isNotBlank(jobParam)) {
                String[] split = jobParam.split(COMMA);
                start = DateUtil.parse(split[0]);

                if (split.length > 1) {
                    end = DateUtil.parse(split[1]);
                }
            }
            goodsAddressLevelRecordService.saveAddressLevel(start, end);
        } catch (Exception e) {
            log.error("获取5级地址失败", e);
        }

    }

}
