package com.teyuntong.infra.task.schedule.market.activity;

import com.teyuntong.infra.task.service.biz.market.activity.service.ConventionActivityService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;

/**
 * 车方评价活动结束后发送权益
 *
 * <AUTHOR>
 * @since 2024-09-30 14:20
 */
@Component
@Slf4j
public class GiveUserEquitySchedule {
    @Resource
    private ConventionActivityService conventionActivityService;

    @XxlJob("giveUserEquity")
    public ReturnT<String> giveUserEquity() {
        StopWatch watch = new StopWatch();
        watch.start();
        try {
            conventionActivityService.giveUserEquity();
        } catch (Exception e) {
            log.error("车方评价活动结束后发送权益异常：", e);
            XxlJobHelper.log("车方评价活动结束后发送权益异常：", e);
            return ReturnT.FAIL;
        }
        watch.stop();
        log.info("车方评价活动结束后发送权益执行结束，耗时:{}", watch.getTotalTimeMillis());
        XxlJobHelper.log("车方评价活动结束后发送权益执行结束，耗时:{}", watch.getTotalTimeMillis());
        return ReturnT.SUCCESS;
    }
}
