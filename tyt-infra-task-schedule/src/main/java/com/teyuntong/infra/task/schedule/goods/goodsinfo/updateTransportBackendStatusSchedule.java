package com.teyuntong.infra.task.schedule.goods.goodsinfo;

import com.teyuntong.infra.task.service.biz.goods.goodsinfo.service.TransportBackendService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 后台发货零点定时修改成取消
 * 每天0：10分执行
 *
 * <AUTHOR>
 * @since 2024-09-19 17:05
 */
@Component
@Slf4j
public class updateTransportBackendStatusSchedule {
    @Resource
    private TransportBackendService transportBackendService;

    @XxlJob("updateTransportBackendStatus")
    public ReturnT<String> updateTransportBackendStatus() {
        try {
            transportBackendService.updateTransportBackendStatus();
        } catch (Exception e) {
            log.error("updateTransportBackendStatus error:", e);
            XxlJobHelper.log("updateTransportBackendStatus error:", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }
}
