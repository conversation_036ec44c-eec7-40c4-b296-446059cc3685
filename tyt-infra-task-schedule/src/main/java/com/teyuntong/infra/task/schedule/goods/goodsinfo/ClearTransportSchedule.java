package com.teyuntong.infra.task.schedule.goods.goodsinfo;

import com.teyuntong.infra.task.service.biz.goods.goodsfee.service.GoodMasterMarkUpService;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.service.TransportService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 定期清除transport表7天前的数据
 *
 * <AUTHOR>
 * @since 2024/09/01 09:35
 */
@Slf4j
@Component
public class ClearTransportSchedule {

    @Autowired
    private TransportService transportService;

    @XxlJob("clearTransportHandler")
    public void clearTransportHandler() {
        String jobParam = XxlJobHelper.getJobParam();
        if (StringUtil.isBlank(jobParam)) {
            jobParam = "7";
        }
        long startTime = System.currentTimeMillis();
        try {
            log.info("定期清除transport之前的数据开始,参数：{}", jobParam);
            transportService.clearTransport(jobParam);
            log.info("定期清除transport之前的数据结束,参数：{}，耗时：{}", jobParam, System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("定期清除transport之前的数据错误,参数：{}", jobParam, e);
        }
    }

}
