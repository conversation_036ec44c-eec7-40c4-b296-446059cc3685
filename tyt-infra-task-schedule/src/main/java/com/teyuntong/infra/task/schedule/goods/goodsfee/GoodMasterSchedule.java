package com.teyuntong.infra.task.schedule.goods.goodsfee;

import com.teyuntong.infra.task.service.biz.goods.goodsfee.service.GoodMasterMarkUpService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 货源长时间未联系或未成交未成交，推送货主加价提醒
 * <AUTHOR>
 * @since 2024/04/01 09:35
 */
@Slf4j
@Component
public class GoodMasterSchedule {

    @Autowired
    private GoodMasterMarkUpService goodMasterMarkUpService;

    @XxlJob("goodMasterMarkUpHandler")
    public ReturnT goodMasterMarkUpHandler() {
        ReturnT returnT = new ReturnT();
        log.info("[BIZ]********************************  goodMasterMarkUpHandler请求 收到******************************************************");
        try {
            goodMasterMarkUpService.findNeedToNotify();
        } catch (Exception e) {
            log.error("[scheduled-Biz] goodMasterMarkUpHandler执行出现异常：{}", e);
            return returnT.FAIL;
        }
        return returnT.SUCCESS;
    }

}
