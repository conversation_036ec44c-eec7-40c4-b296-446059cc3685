package com.teyuntong.infra.task.schedule.goods;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.entity.TransportAfterOrderDataDO;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.entity.TytTransportMain;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.mapper.TransportAfterOrderDataMapper;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.mapper.TytTransportMainMapper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 更新表数据定时任务，用完下掉
 */
@Slf4j
@Component
public class UpdateTableSchedule {

    @Autowired
    private TransportAfterOrderDataMapper transportAfterOrderDataMapper;
    @Autowired
    private TytTransportMainMapper tytTransportMainMapper;


    /**
     * 更新tyt_transport_after_order_data表的user_id
     */
    @XxlJob("updateAfterOrderDataHandler")
    public void updateAfterOrderDataHandler() throws InterruptedException {
        log.info("更新tyt_transport_after_order_data表的user_id");
        Long startId = 0L;
        while (true) {
            QueryWrapper<TransportAfterOrderDataDO> queryWrapper = new QueryWrapper<>();
            queryWrapper.isNull("user_id");
            queryWrapper.ge("id", startId);
            queryWrapper.last("limit 200");
            queryWrapper.orderBy(true, true, "id");
            queryWrapper.select("id", "src_msg_id");
            List<TransportAfterOrderDataDO> orderDataList = transportAfterOrderDataMapper.selectList(queryWrapper);
            log.info("更新tyt_transport_after_order_data表，orderDataList size:{}", orderDataList.size());

            if (orderDataList.isEmpty()) {
                break;
            }

            QueryWrapper<TytTransportMain> tmpQueryWrapper = new QueryWrapper<>();
            tmpQueryWrapper.in("id", orderDataList.stream().map(TransportAfterOrderDataDO::getSrcMsgId).toList());
            tmpQueryWrapper.select("id", "user_id");
            List<TytTransportMain> transportList = tytTransportMainMapper.selectList(tmpQueryWrapper);
            Map<Long, Long> transportMap = transportList.stream().collect(Collectors.toMap(TytTransportMain::getId, TytTransportMain::getUserId));

            for (TransportAfterOrderDataDO transportAfterOrderDataDO : orderDataList) {
                TransportAfterOrderDataDO updateDataDO = new TransportAfterOrderDataDO();
                updateDataDO.setId(transportAfterOrderDataDO.getId());
                Long userId = transportMap.get(transportAfterOrderDataDO.getSrcMsgId());
                if (userId != null) {
                    updateDataDO.setUserId(userId);
                    transportAfterOrderDataMapper.updateById(updateDataDO);
                }

                startId = transportAfterOrderDataDO.getId();
            }
            Thread.sleep(1000);
        }

        log.info("更新tyt_transport_after_order_data表的user_id完成");

    }

}
