package com.teyuntong.infra.task.schedule.trade;

import com.teyuntong.infra.task.service.biz.trade.infofee.service.TransportOrdersService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
* 车主自动装货完成定时任务
* <AUTHOR>
* @since 2024/7/17 15:30
*/
@Slf4j
@Component
public class OrdersAutoLoadSchedule {

    @Autowired
    private TransportOrdersService transportOrdersService;

    /**
     * 车主自动装货完成定时任务
     * 每5分钟执行一次
     */
    @XxlJob("ordersAutoLoad")
    public void ordersAutoLoad() {
        log.info("车主自动装货完成定时任务启动.....");
        try {
             transportOrdersService.ordersAutoLoad();
            log.info("车主自动装货完成定时任务结束.....");
        } catch (Exception e) {
            log.error("车主自动装货完成定时任务异常", e);
        }
    }

    /**
     * 车主自动装货完成定时任务
     */
    @XxlJob("ordersAutoUnloadTask")
    public void ordersAutoUnloadTask() {
        log.info("ordersAutoUnloadTask start .....");
        try {
            transportOrdersService.ordersAutoUnloadTask();
            log.info("ordersAutoUnloadTask end.....");
        } catch (Exception e) {
            log.error("ordersAutoUnloadTask error", e);
        }
    }

}
