package com.teyuntong.infra.task.schedule.trade;

import com.teyuntong.infra.task.service.biz.trade.infofee.service.CarGoodDealNumService;
import com.teyuntong.infra.task.service.biz.trade.infofee.service.TytUserSubService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024/8/14 下午4:21
 */
@Slf4j
@Component
public class CarGoodDealNumSchedule {

    @Autowired
    private CarGoodDealNumService carGoodDealNumService;
    @Autowired
    private TytUserSubService tytUserSubService;
    @XxlJob("carAndGoodsDealNum")
    public void carAndGoodsDealNum() {
        log.info("calculate goods deal num begin");
        tytUserSubService.updateDealNum();
        log.info("calculate goods deal num end");
        log.info("calculate car goods deal num begin");
        carGoodDealNumService.updateCarGoodsDealNum();
        log.info("calculate car goods deal num end");
    }

}
