package com.teyuntong.infra.task.schedule.trade;

import com.teyuntong.infra.task.service.biz.trade.infofee.service.FollowRecordSyncService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class FollowRecordSyncSchedule {

    @Autowired
    private FollowRecordSyncService followRecordSyncService;

    /**
     * 历史跟单记录同步客服系统
     * {"quesOneCategory":14, "quesOneCategoryName":"服务请求", "quesTwoCategory":24, "quesTwoCategoryName":"账户相关", "quesThreeCategory":37, "quesThreeCategoryName":"修改手机号", "quesFourCategory":514, "quesFourCategoryName":"成功"}
     */
    @XxlJob("syncFollowRecord")
    public void syncFollowRecord() {
        String jobParam = XxlJobHelper.getJobParam();
        log.info("syncFollowRecord jobParam:{}", jobParam);
        try {
            followRecordSyncService.syncFollowRecord(jobParam);
            log.info("syncFollowRecord end");
        } catch (Exception e) {
            log.info("syncFollowRecord", e);
        }
    }

}
