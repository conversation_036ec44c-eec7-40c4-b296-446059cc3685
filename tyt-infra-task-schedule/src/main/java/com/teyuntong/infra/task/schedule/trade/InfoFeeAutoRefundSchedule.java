package com.teyuntong.infra.task.schedule.trade;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.teyuntong.infra.task.service.biz.config.service.TytConfigService;
import com.teyuntong.infra.task.service.biz.trade.infofee.bean.TransportOrderBean;
import com.teyuntong.infra.task.service.biz.trade.infofee.entity.TransportOrdersDO;
import com.teyuntong.infra.task.service.biz.trade.infofee.service.TransportOrdersService;
import com.teyuntong.infra.task.service.common.constant.Constant;
import com.teyuntong.infra.task.service.common.mq.service.MqMessageService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class InfoFeeAutoRefundSchedule {

    @Autowired
    private MqMessageService mqMessageService;

    @Autowired
    private TransportOrdersService transportOrdersService;

    @Autowired
    private TytConfigService tytConfigService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    private static final String REDIS_LOCK_CACHE_KEY="tyt_redis_lock_key_";

    /**
     * 自动退款完成
     */
    @XxlJob("infoFeeAutoRefund")
    public void infoFeeAutoRefund() {
        log.info("---------------------------");
        log.info("自动退款启动.....");
        try {
            List<TransportOrderBean> orders = transportOrdersService.getOrdersForAutoRefund();
            if (CollectionUtils.isNotEmpty(orders)) {
                for (TransportOrderBean order : orders) {
                    try {
                        int redisLockTimeout = tytConfigService.getIntValue(Constant.REDIS_LOCK_TIMEOUT_KEY, 10);
                        log.info("infoFee order infoFeeAutoRefund task get redis lock begin, order id is: {}", order.getId());
                        boolean isSuccess = Boolean.TRUE.equals(stringRedisTemplate.opsForValue().setIfAbsent(REDIS_LOCK_CACHE_KEY + 3 + "_" + order.getId(), order.getId() + "", Duration.ofMillis(redisLockTimeout)));
                        if (isSuccess) {
                            log.info("infoFee order infoFeeAutoRefund task get redis lock succeed, order id is: {}", order.getId());
                            TransportOrdersDO transportOrder = transportOrdersService.getById(order.getId());
                            Map<String, Object> map = transportOrdersService.updateOrderRefundFinish(transportOrder);
                            int n = Integer.parseInt(map.get("code").toString());
                            if (n != 200) {
                                log.info("order id: {},{}", order.getId(), map.get("msg"));
                            }
                        }
                    } catch (Exception e) {
                        log.error("【{}】运单自动退款操作异常：", order.getId(), e);
                    } finally {
                        log.info("infofee order infoFeeAutoRefund task release redis lock begin, order id is: {}", order.getId());
                        stringRedisTemplate.delete(REDIS_LOCK_CACHE_KEY + 3 + "_" + order.getId());
                    }
                }
            } else {
                log.info("没有需要自动退款的订单");
            }
            log.info("自动退款结束.....");
        } catch (Exception e) {
            log.error("自动退款异常：", e);
        }
    }

}