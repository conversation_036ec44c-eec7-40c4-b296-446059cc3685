package com.teyuntong.infra.task.schedule.goods.excellent;

import com.teyuntong.infra.task.service.biz.goods.excellent.service.ExcellentPriceCountService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 优车2.0电议配置任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ExcellentPriceCountSchedule {

    private final ExcellentPriceCountService excellentPriceCountService;

    /**
     * 月初定时发放，月初执行
     */
    @XxlJob("excellentPriceCountMonthlyGiveaway")
    public ReturnT<String> monthlyGiveaway() {
        try {
            log.info("优车2.0电议配置任务-月初定时发放 任务开始");
            excellentPriceCountService.monthlyGiveaway();
            log.info("优车2.0电议配置任务-月初定时发放 任务结束");
        } catch (Exception e) {
            log.error("优车2.0电议配置任务-月初定时发放 任务异常：", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 更新发放记录状态，每天0点执行
     */
    @XxlJob("excellentPriceCountUpdateStatus")
    public ReturnT<String> updateStatus() {
        try {
            log.info("优车2.0电议配置任务-更新发放记录状态 任务开始");
            excellentPriceCountService.updateStatus();
            log.info("优车2.0电议配置任务-更新发放记录状态 任务结束");
        } catch (Exception e) {
            log.error("优车2.0电议配置任务-更新发放记录状态 任务异常：", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }
}
