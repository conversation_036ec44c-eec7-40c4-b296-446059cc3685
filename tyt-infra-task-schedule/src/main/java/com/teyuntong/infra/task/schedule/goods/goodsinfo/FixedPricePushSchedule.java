package com.teyuntong.infra.task.schedule.goods.goodsinfo;

import com.teyuntong.infra.task.service.biz.goods.goodsinfo.service.TransportService;
import com.teyuntong.infra.task.service.utils.TimeUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

import static jodd.util.StringPool.COMMA;

/**
 * 定时推送转一口价提醒
 *
 * <AUTHOR>
 * @since 2024/09/01 09:35
 */
@Slf4j
@Component
public class FixedPricePushSchedule {

    @Autowired
    private TransportService transportService;

    /**
     * 定时推送转一口价提醒
     */
    @XxlJob("fixedPricePushHandler")
    public void fixedPricePushHandler() {
        // 当前任务的间隔时间
        String jobParam = XxlJobHelper.getJobParam();
        if (StringUtil.isBlank(jobParam)) {
            jobParam = "10";
        }
        long startTime = System.currentTimeMillis();
        try {
            XxlJobHelper.log("定时推送转一口价提醒开始,jobParam:{}", jobParam);
            log.info("定时推送转一口价提醒开始,jobParam:{}", jobParam);
            int count = transportService.fixedPricePush(Integer.parseInt(jobParam.trim()));
            log.info("定时推送转一口价提醒结束,参数:{},推送条数：{}，耗时：{}", jobParam, count, System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("定时推送转一口价提醒错误,参数:{}", jobParam, e);
            XxlJobHelper.log("定时推送转一口价提醒错误", e);
        }
    }

    /**
     * 一口价货源加价push提醒任务
     */
    @XxlJob("fixedPriceMarkupPushHandler")
    public void fixedPriceMarkupPushHandler() {
        String jobParam = XxlJobHelper.getJobParam();
        // 当前任务的间隔时间（分钟）
        Integer taskInterval = 5;
        // 推送间隔时间（分钟
        Integer pushInterval = 70;
        long startTime = System.currentTimeMillis();
        try {
            if (StringUtil.isNotBlank(jobParam)) {
                String[] split = jobParam.split(COMMA);
                taskInterval = Integer.parseInt(split[0].trim());
                pushInterval = Integer.parseInt(split[1].trim());
            }
            XxlJobHelper.log("一口价货源加价push提醒任务开始,jobParam:{}", jobParam);
            log.info("一口价货源加价push提醒任务开始,jobParam:{}", jobParam);
            int count = transportService.fixedPriceMarkupPush(taskInterval, pushInterval);
            log.info("一口价货源加价push提醒任务结束,参数:{},推送条数：{}，耗时：{}", jobParam, count, System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("一口价货源加价push提醒任务错误,参数:{}", jobParam, e);
            XxlJobHelper.log("一口价货源加价push提醒任务错误", e);
        }

    }


}
