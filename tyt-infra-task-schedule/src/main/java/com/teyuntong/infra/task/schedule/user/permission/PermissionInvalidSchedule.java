package com.teyuntong.infra.task.schedule.user.permission;

import com.teyuntong.infra.task.service.biz.user.permission.pojo.UserPermissionTypeEnum;
import com.teyuntong.infra.task.service.biz.user.permission.service.UserPermissionGainService;
import com.teyuntong.infra.task.service.biz.user.permission.service.UserPermissionService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 权益失效定时任务类
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PermissionInvalidSchedule {

    private final UserPermissionService userPermissionService;

    private final UserPermissionGainService userPermissionGainService;

    /**
     * 货会员次卡权益自动失效
     */
    @XxlJob("GoodsVipPermissionInvalidSchedule")
    public void goodsVipPermissionInvalid() {
        log.info("goods vip permission invalid schedule begin.....");
        try {
            Long startUserId = 0L;
            while (true) {
                List<Long> userIds = userPermissionGainService.getNextPageIdList(startUserId, UserPermissionTypeEnum.GOODS_NUM_NEW.getTypeId());
                if (CollectionUtils.isEmpty(userIds)) {
                    log.info("goods vip permission invalid schedule done ... ");
                    break;
                }
                startUserId = userIds.get(userIds.size() - 1);
                //权益过期
                userPermissionService.updateExpiredPermission(userIds, UserPermissionTypeEnum.GOODS_NUM_NEW.getTypeId());
            }
        } catch (Exception e) {
            log.error("goods vip permission invalid schedule error：", e);
        }
        log.info("goods vip permission invalid schedule end.....");
    }

    /**
     * 车小套餐权益失效
     */
    @XxlJob("CarNumNewPermissionInvalidSchedule")
    public void carNumNewPermissionInvalid() {
        log.info("car num new permission invalid schedule begin.....");
        try {
            userPermissionService.updateInvalidForSmallMeal();
        } catch (Exception e) {
            log.error("car num new permission invalid schedule error：", e);
        }
        log.info("car num new permission invalid schedule end.....");
    }

    /**
     * 车会员（100101）、货会员（100201）过期失效
     */
    @XxlJob("InvalidVipPermissionSchedule")
    public void invalidVipPermission() {
        log.info("invalid vip permission schedule begin.....");
        try {
            List<Long> userIds = userPermissionService.updateInvalidVipPermission();
            //存缓存
            if (CollectionUtils.isNotEmpty(userIds)){
                for (Long userId : userIds) {
                    userPermissionService.updateUserPermissionResult(userId);
                }
            }
        } catch (Exception e) {
            log.error("invalid vip permission schedule error：", e);
        }
        log.info("invalid vip permission schedule end.....");
    }

    /**
     * 车会员(100101)到期找货次卡push提醒
     */
    @XxlJob("CarVipInvalidPushSchedule")
    public void carVipInvalidPush() {
        log.info("car vip invalid push schedule begin.....");
        try {
            userPermissionService.callPhoneTimeNotice();
        } catch (Exception e) {
            log.error("car vip invalid push schedule error：", e);
        }
        log.info("car vip invalid push schedule end.....");
    }

    /**
     * 车找货次卡(100102)权益自动失效
     */
    @XxlJob("CarNumPermissionInvalidSchedule")
    public void carNumPermissionInvalid() {
        log.info("car num permission invalid schedule begin.....");
        try {
            Long startUserId = 0L;
            while (true) {
                List<Long> userIds = userPermissionGainService.getNextPageIdList(startUserId, UserPermissionTypeEnum.CAR_NUM.getTypeId());
                if (CollectionUtils.isEmpty(userIds)) {
                    log.info("car num permission invalid schedule done ... ");
                    break;
                }
                startUserId = userIds.get(userIds.size() - 1);
                //权益过期
                userPermissionService.updateExpiredPermission(userIds, UserPermissionTypeEnum.CAR_NUM.getTypeId());
            }
        } catch (Exception e) {
            log.error("car num permission invalid schedule error：", e);
        }
        log.info("car num permission invalid schedule end.....");
    }

    /**
     * 货发货次卡(100202)权益自动失效
     */
    @XxlJob("GoodsNumPermissionInvalidSchedule")
    public void goodsNumPermissionInvalid() {
        log.info("goods num permission invalid schedule begin.....");
        try {
            Long startUserId = 0L;
            while (true) {
                List<Long> userIds = userPermissionGainService.getNextPageIdList(startUserId, UserPermissionTypeEnum.GOODS_NUM.getTypeId());
                if (CollectionUtils.isEmpty(userIds)) {
                    log.info("goods num permission invalid schedule done ... ");
                    break;
                }
                startUserId = userIds.get(userIds.size() - 1);
                //权益过期
                userPermissionService.updateExpiredPermission(userIds, UserPermissionTypeEnum.GOODS_NUM.getTypeId());
            }
        } catch (Exception e) {
            log.error("goods num permission invalid schedule error：", e);
        }
        log.info("goods num permission invalid schedule end.....");
    }

    /**
     * 货发货次卡(100202)权益自动失效
     */
    @XxlJob("ExposurePermissionInvalidSchedule")
    public void exposurePermissionInvalid() {
        log.info("exposure permission invalid schedule begin.....");
        try {
            //定时作废到期的货源曝光权益
            userPermissionService.updateExpiredExposurePermission();
        }catch (Exception e){
            log.error("exposure permission invalid schedule error：", e);
        }
        log.info("exposure permission invalid schedule end.....");
    }
}
