package com.teyuntong.infra.task.schedule.dispatch;

import com.teyuntong.infra.task.service.biz.dispatch.service.SpecialCarOfflinePaymentService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SpecialCarOfflinePaymentSchedule {

    @Autowired
    private SpecialCarOfflinePaymentService specialCarOfflinePaymentService;

    /**
     * 线下付款消息提醒
     * @return
     */
    @XxlJob("offlinePaymentMessage")
    public ReturnT<String> offlinePaymentMessage() {
        XxlJobHelper.log("线下付款消息提醒开始");
        try {
            specialCarOfflinePaymentService.offlinePaymentMessage();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("线下付款消息提醒", e);
        }
        XxlJobHelper.log("线下付款消息提醒结束");
        return ReturnT.FAIL;
    }

}
