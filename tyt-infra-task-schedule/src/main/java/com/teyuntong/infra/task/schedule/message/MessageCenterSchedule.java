package com.teyuntong.infra.task.schedule.message;

import com.teyuntong.infra.task.service.biz.message.center.service.MessageService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 消息中心调度任务
 *
 * <AUTHOR>
 * @since 2024/03/29 18:14
 */
@Slf4j
@Component
public class MessageCenterSchedule {

    @Autowired
    private MessageService messageService;


    /**
     * 处理消息中心MQ投递失败消息
     */
    @XxlJob("handleDeliverFail")
    public void repeatDeliverMq() {
        messageService.handleDeliverFail();
    }

    /**
     * 处理消息中心MQ投递失败消息，测试使用，随时可以删除
     */
    @XxlJob("handleDeliverFailTest")
    public void handleDeliverFailTest() {
        try {
            log.info("handleDeliverFailTest start");
            messageService.handleDeliverFailTest();
        } catch (Exception e) {
            log.info("handleDeliverFailTest exception e", e);
        }

    }

    @XxlJob("noOrderTransportRiskPush")
    public void noOrderTransportRiskPush() {
        try {
            log.info("noOrderTransportRiskPush start");
            XxlJobHelper.log("noOrderTransportRiskPush start");
            messageService.noOrderTransportRiskPush();
        } catch (Exception e) {
            log.info("noOrderTransportRiskPush exception e", e);
        }
    }


}
