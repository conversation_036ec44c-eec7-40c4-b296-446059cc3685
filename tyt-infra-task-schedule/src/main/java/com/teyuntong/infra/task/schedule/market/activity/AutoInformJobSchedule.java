package com.teyuntong.infra.task.schedule.market.activity;

import com.teyuntong.infra.task.service.biz.market.activity.service.AutomaticInformRuleService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024/04/01 14:05
 */
@Slf4j
@Component
public class AutoInformJobSchedule {

    @Autowired
    private AutomaticInformRuleService automaticInformRuleService;


    /**
     * 自动营销通知触达
     */
    @XxlJob("autoInformSend")
    public void autoInformSend() {
        log.info("自动营销提醒开始......");
        try{
            automaticInformRuleService.sendInform();
        }catch (Exception e){
            log.error("自动营销提醒失败，原因：", e);
        }
        log.info("自动营销提醒结束......");
    }
}
