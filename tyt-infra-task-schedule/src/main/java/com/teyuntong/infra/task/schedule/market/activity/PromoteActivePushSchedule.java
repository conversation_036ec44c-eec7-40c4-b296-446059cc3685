package com.teyuntong.infra.task.schedule.market.activity;

import com.teyuntong.infra.task.service.biz.market.activity.service.PromoteActivePushService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/01/23 11:00
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class PromoteActivePushSchedule {

    private final PromoteActivePushService promoteActivePushService;

    /**
     * 车 -- 未登录天数≥90天且近360天登录过
     */
    @XxlJob("carNoLoginPush")
    public void carNoLoginPush() {
        log.info("车-登录天数≥90天且近360天登录过push消息提醒 start");
        try{
            promoteActivePushService.carNoLoginPush();
        }catch (Exception e){
            log.error("车-登录天数≥90天且近360天登录过push消息提醒 error：", e);
        }
        log.info("车-登录天数≥90天且近360天登录过push消息提醒 end");
    }

    /**
     * 车-90>登录>=7天push消息提醒
     */
    @XxlJob("carNoActivePush")
    public void carNoActivePush() {
        log.info("车-90>登录>=7天push消息提醒 start");
        try{
            promoteActivePushService.carNoActivePush();
        }catch (Exception e){
            log.error("车-90>登录>=7天push消息提醒 error：", e);
        }
        log.info("车-90>登录>=7天push消息提醒 end");
    }

    /**
     * 货老客非会员用户促活push消息提醒
     */
    @XxlJob("goodsNoActivePush")
    public void goodsNoActivePush() {
        log.info("货老客非会员用户促活push消息提醒 start");
        try{
            promoteActivePushService.goodsNoActivePush();
        }catch (Exception e){
            log.error("货老客非会员用户促活push消息提醒 error：", e);
        }
        log.info("货老客非会员用户促活push消息提醒 end");
    }







}
