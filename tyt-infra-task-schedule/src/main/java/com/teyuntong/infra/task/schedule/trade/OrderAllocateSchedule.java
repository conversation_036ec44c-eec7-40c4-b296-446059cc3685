package com.teyuntong.infra.task.schedule.trade;

import com.teyuntong.infra.task.service.biz.trade.infofee.service.OrderAllocateService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/01/16 17:34
 */
@Slf4j
@Component
public class OrderAllocateSchedule {

    @Autowired
    private OrderAllocateService orderAllocateService;


    @XxlJob("orderAllocate")
    public void orderAllocate() {
        log.info("orderAllocate start .....");
        try {

            orderAllocateService.orderAllocate();
            log.info("orderAllocate end .....");
        } catch (Exception e) {
            log.error("orderAllocate error", e);
        }
    }
}
