package com.teyuntong.infra.task.schedule.trade;


import com.teyuntong.infra.task.service.biz.config.service.TytConfigService;
import com.teyuntong.infra.task.service.biz.trade.feedback.entity.FeedbackUser;
import com.teyuntong.infra.task.service.biz.trade.feedback.service.IFeedbackUserService;
import com.teyuntong.infra.task.service.biz.trade.infofee.bean.TransportOrderBean;
import com.teyuntong.infra.task.service.biz.trade.infofee.mapper.TransportOrdersMapper;
import com.teyuntong.infra.task.service.biz.trade.infofee.service.TransportOrdersService;
import com.teyuntong.infra.task.service.biz.user.car.pojo.ShortMessageBean;
import com.teyuntong.infra.task.service.common.mq.service.MessageCenterPushService;
import com.teyuntong.infra.task.service.common.tmpl.service.TytMessageTmplService;
import com.teyuntong.infra.task.service.utils.TimeUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2024/8/14 下午4:21
 */
@Slf4j
@Component
public class FeedbackSchedule {

    private final IFeedbackUserService feedbackUserService;

    private final TransportOrdersService transportOrdersService;

    private final TytConfigService tytConfigService;

    private final MessageCenterPushService messageCenterPushService;

    private final TytMessageTmplService tytMessageTmplService;
    private final TransportOrdersMapper transportOrdersMapper;

    public FeedbackSchedule(IFeedbackUserService feedbackUserService,
                            TytConfigService tytConfigService,
                            MessageCenterPushService messageCenterPushService,
                            TytMessageTmplService tytMessageTmplService,
                            TransportOrdersService transportOrdersService,
                            TransportOrdersMapper transportOrdersMapper) {
        this.feedbackUserService = feedbackUserService;
        this.transportOrdersService = transportOrdersService;
        this.tytConfigService = tytConfigService;
        this.messageCenterPushService = messageCenterPushService;
        this.tytMessageTmplService = tytMessageTmplService;
        this.transportOrdersMapper=transportOrdersMapper;
    }

    @XxlJob("handleTimeoutFeedback")
    public void handleTimeoutFeedback() {

        List<FeedbackUser> unHandledFeedbacks = feedbackUserService.getUnHandledFeedbacks();
        for (FeedbackUser feedbackUser : unHandledFeedbacks) {
            try {
                log.info("评价到期处理,id:{}", feedbackUser.getId());
                feedbackUserService.updateNeedHandleOnDeadline(feedbackUser.getId(), false);

                // 不是好评 仅更新状态
                if (!Objects.equals(feedbackUser.getFeedbackType(), 1)) {
                    continue;
                }

                // 好评 给对方也插入一条好评
                FeedbackUser one = feedbackUserService.getOne(feedbackUser.getReceiveUserId(), feedbackUser.getReceiveUserType(), feedbackUser.getPostUserId(), feedbackUser.getPostUserType(), feedbackUser.getOrderId());
                if (one != null) {
                    // 对方已经评价过
                    continue;
                }

                Date now = new Date();
                FeedbackUser save = FeedbackUser.builder()
                        .postUserId(feedbackUser.getReceiveUserId())
                        .postUserType(feedbackUser.getReceiveUserType())
                        .postUserPhone(feedbackUser.getReceiveUserPhone())
                        .postUserAvatar(feedbackUser.getReceiveUserAvatar())
                        .receiveUserId(feedbackUser.getPostUserId())
                        .receiveUserType(feedbackUser.getPostUserType())
                        .receiveUserAvatar(feedbackUser.getPostUserAvatar())
                        .receiveUserPhone(feedbackUser.getPostUserPhone())
                        .delFlag(false)
                        .needHandleOnDeadline(false)
                        .tsOrderId(feedbackUser.getTsOrderId())
                        .tsId(feedbackUser.getTsId())
                        .orderId(feedbackUser.getOrderId())
                        .transportOrderPubUserName(feedbackUser.getTransportOrderPubUserName())
                        .transportOrderStartPoint(feedbackUser.getTransportOrderStartPoint())
                        .transportOrderDestPoint(feedbackUser.getTransportOrderDestPoint())
                        .transportOrderPayUserName(feedbackUser.getTransportOrderPayUserName())
                        .feedbackType(1)
                        .receiverHideFlag(feedbackUser.getReceiverHideFlag())
                        .createTime(now)
                        .updateTime(now)
                        .feedbackDeadline(feedbackUser.getFeedbackDeadline())
                        .updateTimes(0).build();

                feedbackUserService.save(save);
            } catch (Exception e) {
                log.error("评价处理失败 feedback:{}", feedbackUser, e);
            }
        }
    }

    /**
     * 待评价的运单发送短信通知
     */
    @Transactional(transactionManager = "transactionManager")
    @XxlJob("todoFeedbackPushMessage")
    public void todoFeedbackPushMessage() {
        //推送开关是否打开
        if (!isOpenPushOnOff()) {
            return;
        }
        //获取偏移时间
        int day = getOffsetDays();
        if (day == 0) {
            return;
        }
        Date queryDate = TimeUtil.dateDiff(day);

        Set<String> carCellPhoneSet = new HashSet<>();
        Set<String> goodsCellPhoneSet = new HashSet<>();
        String carContent = tytMessageTmplService.getSmsTmpl("todo_feedback_notify_car_tmpl");
        String goodsContent = tytMessageTmplService.getSmsTmpl("todo_feedback_notify_goods_tmpl");

        Long orderCount = transportOrdersService.getTodoFeedBackOrderCount(queryDate);
        long pageNum = (orderCount / 1000) + 1;

        for (int i = 1; i <= pageNum; i++) {
            pushSortMessage(queryDate, carCellPhoneSet, goodsCellPhoneSet, carContent, goodsContent, i);
        }
    }

    /**
     * 待评价发送短信
     *
     * @param queryDate 查询时间
     * @param carCellPhoneSet 车主电话集合
     * @param goodsCellPhoneSet 货主电话集合
     * @param carContent 车短信模板
     * @param goodsContent 货短信模板
     * @param pageNum 分页页数
     */
    private void pushSortMessage(Date queryDate, Set<String> carCellPhoneSet, Set<String> goodsCellPhoneSet, String carContent, String goodsContent, int pageNum) {
        //查询符合条件的运单
        List<TransportOrderBean> orders = transportOrdersMapper.getTodoFeedBackTransportOrders(queryDate, (pageNum - 1) * 1000);
        if (orders == null || orders.isEmpty()) {
            return;
        }
        orders.forEach(o -> {
            if (o.getPayCellPhone() != null
                    && !carCellPhoneSet.contains(o.getPayCellPhone())) {
                // 发送短信
                ShortMessageBean shortMessageBean = new ShortMessageBean();
                shortMessageBean.setContent(carContent);
                shortMessageBean.setCellPhone(o.getPayCellPhone());
                shortMessageBean.setRemark("待评价运单车方短信通知");
                messageCenterPushService.sendMultiMessage(shortMessageBean,null,null);
                carCellPhoneSet.add(o.getPayCellPhone());
            }
            if (o.getUploadCellphone() != null
                    && !goodsCellPhoneSet.contains(o.getUploadCellphone())) {
                // 发送短信
                ShortMessageBean shortMessageBean = new ShortMessageBean();
                shortMessageBean.setContent(goodsContent);
                shortMessageBean.setCellPhone(o.getUploadCellphone());
                shortMessageBean.setRemark("待评价运单货方短信通知");
                messageCenterPushService.sendMultiMessage(shortMessageBean,null,null);
                goodsCellPhoneSet.add(o.getUploadCellphone());
            }
        });
    }

    /**
     * 获取偏移天数
     *
     * @return 偏移天数
     */
    private int getOffsetDays() {
        //判断是否到推送日（每周一三五）
        Calendar calendar = Calendar.getInstance();
        //向前偏移的天数
        int day = 0;
        if (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.MONDAY) {
            day = -3;
        } else if (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.WEDNESDAY
                || calendar.get(Calendar.DAY_OF_WEEK) == Calendar.FRIDAY) {
            day = -2;
        }
        log.info("todoFeedbackPushMessage getOffsetDays:" + day);
        return day;
    }

    /**
     * 判断推送开关是否打开
     *
     * @return true：是；false：否；
     */
    private boolean isOpenPushOnOff() {
        Integer todoFeedbackNotifyOnOff = tytConfigService.getIntValue("todo_feedback_notify_on_off", 0);
        log.info("todoFeedbackPushMessage isOpenPushOnOff:" + todoFeedbackNotifyOnOff);
        return todoFeedbackNotifyOnOff == 0;
    }
}
