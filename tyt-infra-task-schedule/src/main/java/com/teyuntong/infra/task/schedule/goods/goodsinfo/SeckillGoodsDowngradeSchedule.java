package com.teyuntong.infra.task.schedule.goods.goodsinfo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Pair;
import com.alibaba.nacos.common.utils.StringUtils;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.bean.TransportLabelJson;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.entity.TransportExtendDO;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.entity.TransportMainExtendDO;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.entity.TytTransport;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.entity.TytTransportMain;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.service.TransportMainService;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.service.TransportService;
import com.teyuntong.infra.task.service.biz.goods.goodsinfo.service.TransportVaryService;
import com.teyuntong.infra.task.service.remote.trade.service.OrderSnapshotRemoteService;
import com.teyuntong.infra.task.service.remote.tytconfig.service.TytConfigRemoteService;
import com.teyuntong.infra.task.service.utils.TimeUtil;
import com.teyuntong.infra.task.service.utils.TransportUtil;
import com.teyuntong.trade.service.client.orders.vo.TransportOrderSnapshotVO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.stream.Stream;

/**
 * 秒抢货源长时间未成交变成非秒抢货源，只针对实验组
 */
@Slf4j
@Component
public class SeckillGoodsDowngradeSchedule {

    @Autowired
    private TransportMainService transportMainService;
    @Autowired
    private TransportService transportService;
    @Autowired
    private TransportVaryService transportVaryService;
    @Autowired
    private TytConfigRemoteService tytConfigRemoteService;
    @Autowired
    private OrderSnapshotRemoteService orderSnapshotRemoteService;

    @XxlJob("downgradeSeckillGoodsHandler")
    public ReturnT<String> downgradeSeckillGoodsHandler() {
        try {
            log.info("秒抢货源未成交转非秒抢开始执行");
            XxlJobHelper.log("秒抢货源未成交转非秒抢开始执行");
            StopWatch watch = new StopWatch();
            watch.start();
            this.handle();
            watch.stop();
            log.info("秒抢货源未成交转非秒抢执行结束，耗时:{}", watch.getTotalTimeMillis());
            XxlJobHelper.log("秒抢货源未成交转非秒抢执行结束，耗时:{}", watch.getTotalTimeMillis());
            return ReturnT.SUCCESS;
        } catch (IllegalStateException e) {
            log.error("秒抢货源未成交转非秒抢执行异常", e);
            XxlJobHelper.log("秒抢货源未成交转非秒抢执行异常", e);
        }
        return ReturnT.FAIL;
    }

    /**
     * 秒抢货源未成交变成非秒抢货源
     */
    private void handle() {
        Date startTime = DateUtil.beginOfDay(new Date());
        Date endTime = new Date();
        List<Long> todayValidSeckillGoodsId = transportMainService.getValidSeckillGoodsId(startTime, endTime);
        log.info("秒抢货源未成交变成非秒抢任务，今天有【{}】个秒抢货源", todayValidSeckillGoodsId.size());
        if (CollectionUtils.isEmpty(todayValidSeckillGoodsId)) {
            return;
        }

        // 获取配置
        List<Pair<Integer, Integer>> configList = parseDowngradeConfig();
        if (configList.isEmpty()) {
            return;
        }

        for (Long srcMsgId : todayValidSeckillGoodsId) {
            TytTransport transport = transportService.getLastBySrcMygId(srcMsgId);
            if (transport == null) {
                continue;
            }
            // 校验是否有待支付单
            List<TransportOrderSnapshotVO> orderSnapshotVOS = orderSnapshotRemoteService.getBySrcMsgId(srcMsgId);
            if (CollectionUtils.isNotEmpty(orderSnapshotVOS)) {
                log.info("秒抢货源未成交变成非秒抢任务，有待支付单，货源id：{}", srcMsgId);
                continue;
            }
            // 判断距离
            BigDecimal distance = transport.getDistance();
            if (distance == null) {
                log.info("秒抢货源未成交变成非秒抢任务，货源id：{}，距离为空", srcMsgId);
                continue;
            }

            // 判断是否符合配置
            boolean downgrade = false;
            for (Pair<Integer, Integer> pair : configList) {
                if (distance.intValue() <= pair.getKey()) {
                    downgrade = DateUtil.between(transport.getReleaseTime(), DateUtil.date(), DateUnit.MINUTE) >= pair.getValue();
                    log.info("秒抢货源未成交变成非秒抢任务，货源id：{}，距离：{}，配置距离：{}，配置时间：{}，是否符合条件：{}",
                            srcMsgId, distance, pair.getKey(), pair.getValue(), downgrade);
                    break;
                }
            }
            if (downgrade) {
                updateDatabase(transport);
            }

        }
    }

    /**
     * 解析配置
     * 配置格式：200,Z;500,Y;10000,X 配置说明：(0-200] Z分钟，(200-500] Y分钟，(500~∞] X分钟
     */
    private List<Pair<Integer, Integer>> parseDowngradeConfig() {
        String configValue = tytConfigRemoteService.getStringValue("seckill_goods_downgrade_config");
        if (StringUtils.isBlank(configValue)) {
            log.error("秒抢货源未成交变成非秒抢任务，配置为空");
            return List.of();
        }
        return Stream.of(configValue.split(";"))
                .map(t -> {
                    String[] split1 = t.split(",");
                    return new Pair<>(Integer.valueOf(split1[0]), Integer.valueOf(split1[1]));
                }).toList();
    }


    /**
     * 更新数据库
     */
    private void updateDatabase(TytTransport oldTransport) {
        Long oldTsId = oldTransport.getId();
        TytTransport newTransport = BeanUtil.copyProperties(oldTransport, TytTransport.class);
        /* 修改原信息值生成一条新数据 */
        newTransport.setId(null);
        newTransport.setStatus(1);
        newTransport.setCtime(new Date());
        newTransport.setMtime(new Date());
        newTransport.setPubTime(TimeUtil.formatTime(new Date()));
        newTransport.setDisplayType("1"); //默认显示
        newTransport.setIsDisplay(1); //默认显示
        /* 重发次数，为了解决老版PC的序号问题 */
        int resendCounts = (newTransport.getResendCounts() == null ? 0 : newTransport.getResendCounts()) + 1;
        newTransport.setResendCounts(resendCounts);
        if (resendCounts > 0) {
            newTransport.setPcOldContent("[" + resendCounts + "]." + newTransport.getPcOldContent().replaceAll("\\[[0-9]{1,}\\]\\.", ""));
        }

        log.info("transport表旧数据置为无效，货源id：{}", oldTsId);
        TytTransport updateTransport = new TytTransport();
        updateTransport.setId(oldTsId);
        updateTransport.setStatus(0);
        updateTransport.setMtime(new Date());
        boolean update = transportService.updateById(updateTransport);
        if (!update) {
            log.warn("transport表旧数据置为无效失败，货源id：{}", oldTsId);
            return;
        }

        // 信息变化表添加数据，兼容老逻辑
        transportVaryService.save(oldTsId, 0);

        TransportLabelJson labelJson = TransportUtil.getLabelJson(newTransport);
        labelJson.setSeckillDowngrade(1);
        String jsonText = labelJson.getJsonText();

        // 新增transport & transportExtend => label_json新增标识，seckill_goods=0
        newTransport.setLabelJson(jsonText);
        TransportExtendDO newTransportExtend = transportService.getExtendByTsId(oldTsId);
        newTransportExtend.setSeckillGoods(0);
        transportService.saveTransportAndExtend(newTransport, newTransportExtend);

        // 更新main & mainExtend => label_json新增标识，seckill_goods=0
        TytTransportMain updateTransportMain = new TytTransportMain();
        updateTransportMain.setId(newTransport.getSrcMsgId());
        updateTransportMain.setLabelJson(jsonText);
        TransportMainExtendDO updateTransportMainExtend = new TransportMainExtendDO();
        updateTransportMainExtend.setSeckillGoods(0);
        transportMainService.updateMainAndExtend(updateTransportMain, updateTransportMainExtend);

        log.info("秒抢货源变非秒抢设置成功，旧货物ID【{}】，新货物ID【{}】：", oldTsId, newTransport.getId());
    }


}
