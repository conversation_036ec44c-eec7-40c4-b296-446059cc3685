package com.teyuntong.infra.task.schedule.trade;


import com.teyuntong.infra.task.service.biz.trade.infofee.service.TransportOrdersService;
import com.teyuntong.infra.task.service.utils.TimeUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

@Slf4j
@Component
public class InfoFeePayTypeSchedule {

    @Autowired
    private TransportOrdersService transportOrdersService;



    /**
     * 每10分钟查询一下 前20分钟支付成功的订单的支付方式
     */
    @XxlJob("updateInfoFeePayType")
    public void updateInfoFeePayType(){
        log.info("------------------------------");
        log.info("【请求满帮查询交易详情接口】同步支付成功的订单的支付方式更新开始,查询时间【{}】.....", TimeUtil.formatDateTime(new Date()));
        try {
            long nowMillis = System.currentTimeMillis();
            long beginMillis = nowMillis - 20*60*1000;
            Date beginTime = new Date(beginMillis);
            //由于订单可能提前生成  为防止更新遗漏 将订单查询时间设置为当前时间-1天
            long orderBeginMillis = nowMillis - 24*60*60*1000;
            Date orderBeginTime = new Date(orderBeginMillis);
            transportOrdersService.updateInfoFeePayType(beginTime,orderBeginTime);
        }catch (Exception e){
            log.error("【请求满帮查询交易详情接口】同步支付成功的订单的支付方式异常 异常信息为：", e);
        }
        log.info("【请求满帮查询交易详情接口】同步支付成功的订单的支付方式结束,结束时间【{}】.....", TimeUtil.formatDateTime(new Date()));
        log.info("------------------------------");
    }
}
