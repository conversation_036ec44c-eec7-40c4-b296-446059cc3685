package com.teyuntong.infra.task.schedule.goods.covergoods;

import com.teyuntong.infra.task.service.biz.goods.covergoods.service.CoverGoodsBeansConfigUserService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * 定时过期抢单豆
 *
 * <AUTHOR>
 * @since 2024/01/18 13:45
 */
@Slf4j
@Component
public class CoverGoodsConfigSchedule {


    private final CoverGoodsBeansConfigUserService coverGoodsBeansConfigUserService;

    public CoverGoodsConfigSchedule(CoverGoodsBeansConfigUserService coverGoodsBeansConfigUserService) {
        this.coverGoodsBeansConfigUserService = coverGoodsBeansConfigUserService;
    }

    @XxlJob("coverGoodsJobHandler")
    public void updateCoverGoodsStatus() {
        log.info("抢单豆过期定时任务执行开始");
        int count = 0;
        try {
            count = coverGoodsBeansConfigUserService.updateCoverGoodsStatus();
        } catch (Exception e) {
            log.error("抢单豆过期定时任务执行失败", e);
        }
        log.info("抢单豆过期定时任务执行结束,更新条数：{}", count);
    }

}
