package com.teyuntong.infra.task.schedule.trade;

import com.teyuntong.infra.task.service.biz.goods.custom.service.CustomInformationService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
* 用户订单定时任务
* <AUTHOR>
* @since 2024/8/20 10:27
*/
@Slf4j
@Component
public class PerformanceTimeUpdateSchedule {

    @Autowired
    private CustomInformationService customInformationService;

    /**
     * 保存最近一次成单时间开始
     * 每天凌晨三点执行一次
     */
    @XxlJob("scheduleUpdatePerformanceTime")
    public void scheduleUpdatePerformanceTime() {
        log.info("保存最近一次成单时间定时任务启动.....");
        try {
            customInformationService.scheduleUpdatePerformanceTime(null,null);
            log.info("保存最近一次成单时间定时任务结束.....");
        } catch (Exception e) {
            log.error("保存最近一次成单时间定时任务异常", e);
        }
    }

}
