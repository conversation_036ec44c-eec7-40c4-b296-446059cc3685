package com.teyuntong.infra.task.schedule.trade;

import com.aliyun.openservices.ons.shaded.commons.lang3.StringUtils;
import com.teyuntong.infra.task.service.biz.trade.infofee.service.RiskTrackBaseDataService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

import static com.baomidou.mybatisplus.core.toolkit.StringPool.AMPERSAND;

/**
* 根据轨迹判断是否风控基础数据处理定时任务
* <AUTHOR>
* @since 2024/3/18 15:46
*/
@Slf4j
@Component
@RequiredArgsConstructor
public class RiskTrackBaseDataSchedule {

    private final RiskTrackBaseDataService riskTrackBaseDataService;

    @XxlJob("actualStartConfirmJobHandler")
    public void actualStartConfirm() {
        log.info("订单轨迹确认实际装货时间定时任务执行开始");
        long startTime = System.currentTimeMillis();
        try {
            riskTrackBaseDataService.updateActualStartConfirm();
        } catch (Exception e) {
            log.error("订单轨迹确认实际装货时间定时任务执行失败", e);
        }finally {
            long endTime = System.currentTimeMillis();
            log.info("订单轨迹确认实际装货时间定时任务执行结束,耗时:{}ms",(endTime - startTime));
        }

    }

    @XxlJob("actualDestConfirmJobHandler")
    public void actualDestConfirm() {
        log.info("订单轨迹确认实际卸货时间定时任务执行开始");
        long startTime = System.currentTimeMillis();
        try {
             riskTrackBaseDataService.updateActualDestConfirm();
        } catch (Exception e) {
            log.error("订单轨迹确认实际卸货时间定时任务执行失败", e);
        }finally {
            long endTime = System.currentTimeMillis();
            log.info("订单轨迹确认实际卸货时间定时任务执行结束,耗时:{}ms",(endTime - startTime));
        }
    }

    @XxlJob("leaveStartConfirmJobHandler")
    public void leaveStartConfirmJobHandler() {
        log.info("订单轨迹确认离开装货时间定时任务执行开始");
        long startTime = System.currentTimeMillis();
        try {
            riskTrackBaseDataService.leaveStartConfirmJobHandler();
        } catch (Exception e) {
            log.error("订单轨迹确认离开装货时间定时任务执行失败", e);
        }finally {
            long endTime = System.currentTimeMillis();
            log.info("订单轨迹确认离开装货时间定时任务执行结束,耗时:{}ms",(endTime - startTime));
        }
    }

    @XxlJob("leaveDestConfirmJobHandler")
    public void leaveDestConfirmJobHandler() {
        log.info("订单轨迹确认离开卸货时间定时任务执行开始");
        long startTime = System.currentTimeMillis();
        try {
            riskTrackBaseDataService.leaveDestConfirmJobHandler();
        } catch (Exception e) {
            log.error("订单轨迹确认离开卸货时间定时任务执行失败", e);
        }finally {
            long endTime = System.currentTimeMillis();
            log.info("订单轨迹确认离开卸货时间定时任务执行结束,耗时:{}ms",(endTime - startTime));
        }
    }


    @XxlJob("autoPickGoodsJobHandler")
    public void autoPickGoodsJobHandler() {
        log.info("订单轨迹符合要求时自动起运定时任务执行开始");
        long startTime = System.currentTimeMillis();
        try {
            riskTrackBaseDataService.autoPickGoodsJobHandler();
        } catch (Exception e) {
            log.error("订单轨迹符合要求时自动起运定时任务执行失败", e);
        }finally {
            long endTime = System.currentTimeMillis();
            log.info("订单轨迹符合要求时自动起运定时任务执行结束,耗时:{}ms",(endTime - startTime));
        }
    }



    @XxlJob("autoUnLoadJobHandler")
    public void autoUnLoadJobHandler() {
        log.info("订单轨迹符合要求时自动抵运定时任务执行开始");
        long startTime = System.currentTimeMillis();
        try {
            riskTrackBaseDataService.autoUnLoadJobHandler();
        } catch (Exception e) {
            log.error("订单轨迹符合要求时自动抵运定时任务执行失败", e);
        }finally {
            long endTime = System.currentTimeMillis();
            log.info("订单轨迹符合要求时自动抵运定时任务执行结束,耗时:{}ms",(endTime - startTime));
        }
    }


    // 停用
    @XxlJob("manualStartConfirmJobHandler")
    public void manualStartConfirmJobHandler() {
        log.info("人工确认装货地轨迹点定时任务执行开始");
        long startTime = System.currentTimeMillis();
        try {
            String jobParam = XxlJobHelper.getJobParam();
            if (StringUtils.isNotBlank(jobParam)) {
                String[] jobParams = jobParam.split(AMPERSAND);
                String orderIds =jobParams[0];
                List<String> enterpriseCreditCodeList = Arrays.asList(orderIds.split(","));
                int day =Integer.parseInt(jobParams[1]);
                riskTrackBaseDataService.manualStartConfirm(enterpriseCreditCodeList,day);
            }

        } catch (Exception e) {
            log.error("人工确认装货地轨迹点任务执行失败", e);
        } finally {
            long endTime = System.currentTimeMillis();
            log.info("补充已开户企业昨天账户流水任务执行结束,耗时:{}ms", (endTime - startTime));
        }
    }
}
