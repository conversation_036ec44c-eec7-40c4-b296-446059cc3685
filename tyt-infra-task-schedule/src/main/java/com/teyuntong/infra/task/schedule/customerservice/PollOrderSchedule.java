package com.teyuntong.infra.task.schedule.customerservice;

import com.teyuntong.infra.task.service.biz.customerservice.service.CsComplaintRecordService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;

/**
 * 工单轮单
 *
 * <AUTHOR>
 * @since 2023/10/08 13:14
 */
@Component
@Slf4j
public class PollOrderSchedule {
    @Autowired
    private CsComplaintRecordService csComplaintRecordService;

    /**
     * 投诉工单轮单
     */
    @XxlJob("pollOrder")
    public ReturnT<String> pollOrder() {
        long now = System.currentTimeMillis();
        try {
            csComplaintRecordService.updateOpUser();
        } catch (ParseException e) {
            log.error("投诉工单轮单失败", e);
            XxlJobHelper.log("投诉工单轮单失败", e);
            return ReturnT.FAIL;
        }
        log.info("投诉工单轮单定时任务耗时：" + (System.currentTimeMillis() - now));
        XxlJobHelper.log("投诉工单轮单定时任务耗时：" + (System.currentTimeMillis() - now));
        return ReturnT.SUCCESS;
    }
}
