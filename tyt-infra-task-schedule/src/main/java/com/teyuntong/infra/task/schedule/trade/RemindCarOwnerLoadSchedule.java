package com.teyuntong.infra.task.schedule.trade;

import com.teyuntong.infra.task.service.biz.trade.infofee.service.TransportWaybillService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
* 催车主装货完成提醒T-1天提醒
* <AUTHOR>
* @since 2024/8/12 13:30
*/
@Slf4j
@Component
public class RemindCarOwnerLoadSchedule {

    @Autowired
    private TransportWaybillService transportWaybillService;

    /**
     *  催车主装货完成提醒T-1天提醒
     *  以同意装货时间为起点
     *  每5分钟执行一次
     */
    @XxlJob("carOwnerTReduced1")
    public void carOwnerTReduced1() {
        log.info("催车主装货完成提醒T-1天提醒定时任务启动.....");
        try {
            transportWaybillService.carOwnerTReduced1();
            log.info("催车主装货完成提醒T-1天提醒定时任务结束.....");
        } catch (Exception e) {
            log.error("催车主装货完成提醒T-1天提醒定时任务异常", e);
        }    }

}
