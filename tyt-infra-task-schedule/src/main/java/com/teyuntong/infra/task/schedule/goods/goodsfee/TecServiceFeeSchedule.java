package com.teyuntong.infra.task.schedule.goods.goodsfee;

import cn.hutool.core.date.DateUtil;
import com.teyuntong.infra.task.service.biz.goods.goodsfee.service.TecServiceFeeService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 抽佣货源超时免佣
 */
@Slf4j
@Component
public class TecServiceFeeSchedule {

    public TecServiceFeeSchedule(TecServiceFeeService tecServiceFeeService) {
        this.tecServiceFeeService = tecServiceFeeService;
    }

    private final TecServiceFeeService tecServiceFeeService;

    @XxlJob("freeTecServiceFee")
    public void carExpiredWarn() {
        log.info("超时免佣定时任务开始");
        try {
            String jobParam = XxlJobHelper.getJobParam();
            Date start = null;
            if (StringUtils.isNotBlank(jobParam)) {
                start = DateUtil.parse(jobParam);
            }
            tecServiceFeeService.freeTecServiceFee(start);
        } catch (Exception e) {
            log.error("超时免佣定时任务失败", e);
        }
        log.info("超时免佣定时任务结束");
    }

}
