package com.teyuntong.infra.task.schedule.trade;

import com.teyuntong.infra.task.service.biz.trade.infofee.service.CarTraceCheckDataService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
* 车辆轨迹校验定时任务
* <AUTHOR>
* @since 2025/2/6 10:39
*/
@Slf4j
@Component
@RequiredArgsConstructor
public class CarTrackCheckDataSchedule {

    private final CarTraceCheckDataService carTraceCheckDataService;

    @XxlJob("carTraceCheckJobHandler")
    public void carTraceCheck() {
        log.info("车辆轨迹校验定时任务执行开始");
        long startTime = System.currentTimeMillis();
        try {
            carTraceCheckDataService.carTraceCheck();
        } catch (Exception e) {
            log.error("车辆轨迹校验定时任务执行失败", e);
        }finally {
            long endTime = System.currentTimeMillis();
            log.info("车辆轨迹校验定时任务执行结束,耗时:{}ms",(endTime - startTime));
        }

    }


}
