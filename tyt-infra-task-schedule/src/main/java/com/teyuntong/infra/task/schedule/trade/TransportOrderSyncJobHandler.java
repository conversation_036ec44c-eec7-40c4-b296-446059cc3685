package com.teyuntong.infra.task.schedule.trade;

import com.alibaba.fastjson.JSON;
import com.teyuntong.infra.task.schedule.trade.req.TransportOrdersToInvoiceReq;
import com.teyuntong.infra.task.service.biz.trade.infofee.service.TransportOrdersInvoiceService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/06/18 19:39
 */
@Component
@Slf4j
public class TransportOrderSyncJobHandler {


    @Autowired
    private TransportOrdersInvoiceService transportOrdersInvoiceService;

    /**
     * 同步 tyt_transport_order 数据到 invoice_orders 表
     */
    @XxlJob("syncTransportOrdersToInvoiceJob")
    public void syncTransportOrdersToInvoiceJob() throws Exception {

        Integer second = 60;
        Long maxId = 0L;
        Integer pageSize = 500;
        String jobParam = XxlJobHelper.getJobParam();
        Date timeAgo = null;
        TransportOrdersToInvoiceReq transportOrdersToInvoiceReq = JSON.parseObject(jobParam, TransportOrdersToInvoiceReq.class);
        if(Objects.nonNull(transportOrdersToInvoiceReq)){
            second = transportOrdersToInvoiceReq.getSecond();
            pageSize = transportOrdersToInvoiceReq.getPageSize();

            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.SECOND, -second);
            timeAgo = calendar.getTime();
        }else{
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.YEAR, -3);
            timeAgo = calendar.getTime();
        }

        log.info("syncTransportOrdersToInvoiceJob start with maxId: {}, timeAgo: {}, pageSize: {}", maxId, timeAgo, pageSize);
        transportOrdersInvoiceService.syncTransportOrdersToInvoiceJob(maxId,timeAgo,pageSize);
    }

    /**
     * 同步三方运单状态
     * 1. 查询 three_waybill_status 不是 HAS_FINISH 或 HAS_CANCEL 的发票订单数据
     * 2. 调用三方接口获取最新状态
     * 3. 更新状态到本地数据库
     */
    @XxlJob("syncThirdPartyOrderStatusJob")
    public void syncThirdPartyOrderStatusJob() throws Exception {

        Integer second = 60;
        Long maxId = 0L;
        Integer pageSize = 500;
        String jobParam = XxlJobHelper.getJobParam();
        TransportOrdersToInvoiceReq transportOrdersToInvoiceReq = JSON.parseObject(jobParam, TransportOrdersToInvoiceReq.class);
        if(Objects.nonNull(transportOrdersToInvoiceReq)){
            second = transportOrdersToInvoiceReq.getSecond();
            pageSize = transportOrdersToInvoiceReq.getPageSize();
        }
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.SECOND, -second);
        Date timeAgo = calendar.getTime();
        log.info("syncThirdPartyOrderStatusJob start with maxId: {}, timeAgo: {}, pageSize: {}", maxId, timeAgo, pageSize);
        transportOrdersInvoiceService.syncThirdPartyOrderStatus(maxId,timeAgo,pageSize);
        log.info("syncThirdPartyOrderStatusJob end");

    }
}
